<?php $__env->startSection('main'); ?>
<?php $__env->startSection('title'); ?>
 Login Or Register | Easy RealEstate  
<?php $__env->stopSection(); ?>

<!--Page Title-->
<section class="page-title-two bg-color-1 centred">
    <div class="pattern-layer">
        <div class="pattern-1" style="background-image: url(<?php echo e(asset('frontend/assets/images/shape/shape-9.png')); ?>);"></div>
        <div class="pattern-2" style="background-image: url(<?php echo e(asset('frontend/assets/images/shape/shape-10.png')); ?>);"></div>
    </div>
    <div class="auto-container">
        <div class="content-box clearfix">
            <h1>Sign In / Register</h1>
            <ul class="bread-crumb clearfix">
                <li><a href="<?php echo e(url('/')); ?>">Home</a></li>
                <li>Sign In / Register</li>
            </ul>
        </div>
    </div>
</section>
<!--End Page Title-->

<!-- ragister-section -->
<section class="ragister-section centred sec-pad">
    <div class="auto-container">
        <div class="row clearfix">
            <div class="col-xl-8 col-lg-12 col-md-12 offset-xl-2 big-column">
                <div class="tabs-box">
                    <div class="tab-btn-box">
                        <ul class="tab-btns tab-buttons centred clearfix">
                            <li class="tab-btn active-btn" data-tab="#tab-1">Login</li>
                            <li class="tab-btn" data-tab="#tab-2">Register</li>
                        </ul>
                    </div>
                    <div class="tabs-content">
                        
                        <div class="tab active-tab" id="tab-1">
                            <div class="inner-box">
                                <h4>Sign in</h4>
                                
                                <?php if($errors->has('login') || $errors->has('password')): ?>
                                <div class="alert alert-danger">
                                    <ul>
                                        <?php if($errors->has('login')): ?>
                                            <li><?php echo e($errors->first('login')); ?></li>
                                        <?php endif; ?>
                                        <?php if($errors->has('password') && !$errors->has('login')): ?>
                                            <li><?php echo e($errors->first('password')); ?></li>
                                        <?php endif; ?>
                                        <?php if(session('status')): ?>
                                            <li><?php echo e(session('status')); ?></li> 
                                        <?php endif; ?>
                                    </ul>
                                </div>
                                <?php endif; ?>
                                 <?php if(session('status')): ?>
                                    <div class="alert alert-danger">
                                         <?php echo e(session('status')); ?> 
                                    </div>
                                <?php endif; ?>

                                <form action="<?php echo e(route('login')); ?>" method="post" class="default-form">
                                    <?php echo csrf_field(); ?>

                                    <div class="form-group">
                                        <label>Email/Name/Phone </label>
                                        <input type="text" name="login" id="login" required="" value="<?php echo e(old('login')); ?>">
                                         <?php $__errorArgs = ['login'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <span class="text-danger"><?php echo e($message); ?></span> 
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                     
                                    <div class="form-group">
                                        <label>Password</label>
                                        <input type="password" name="password" id="password" required="">
                                         <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <span class="text-danger"><?php echo e($message); ?></span> 
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>

                                    <div class="form-group message-btn">
                                        <button type="submit" class="theme-btn btn-one">Sign in</button>
                                    </div>
                                </form>
                                <div class="othre-text">
                                     
                                    
                                </div>
                            </div>
                        </div>

                        
                        <div class="tab" id="tab-2">
                            <div class="inner-box">
                                <h4>Register</h4>
                                
                                <?php if($errors->has('name') || $errors->has('email') || $errors->has('password') || $errors->has('role') || $errors->has('password_confirmation')): ?>
                                <div class="alert alert-danger">
                                    <ul>
                                        <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <li><?php echo e($error); ?></li>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </ul>
                                </div>
                                <?php endif; ?>

                                <form action="<?php echo e(route('register')); ?>" method="post" class="default-form">
                                    <?php echo csrf_field(); ?>

                                    <div class="form-group">
                                        <label>User name</label>
                                        <input type="text" name="name" id="name" required="" value="<?php echo e(old('name')); ?>">
                                    </div>
                                    <div class="form-group">
                                        <label>Email address</label>
                                        <input type="email" name="email" id="email" required="" value="<?php echo e(old('email')); ?>">
                                    </div>

                                    
                                    <div class="form-group">
                                        <label>Register As</label>
                                        <select name="role" class="form-control" required>
                                            <option value="" selected disabled>Select Role...</option>
                                            <option value="user" <?php echo e(old('role') == 'user' ? 'selected' : ''); ?>>User</option>
                                            <option value="agent" <?php echo e(old('role') == 'agent' ? 'selected' : ''); ?>>Agent</option>
                                            
                                            <option value="admin" <?php echo e(old('role') == 'admin' ? 'selected' : ''); ?>>Admin</option> 
                                        </select>
                                    </div>

                                    <div class="form-group">
                                        <label>Password</label>
                                        <input type="password" name="password" id="password" required="">
                                    </div>

                                     <div class="form-group">
                                        <label>Confirm Password</label>
                                        <input type="password" name="password_confirmation" id="password_confirmation" required="">
                                    </div>

                                    <div class="form-group message-btn">
                                        <button type="submit" class="theme-btn btn-one">Register</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- ragister-section end -->

<?php $__env->stopSection(); ?>
<?php echo $__env->make('frontend.frontend_dashboard', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\FinalProject\realestate\resources\views/auth/login.blade.php ENDPATH**/ ?>