.main-wrapper {
  display: flex;
  // overflow: hidden; // commented for fixing content-nav sticky position
  .page-wrapper {
    min-height: 100vh;
    background: $body-bg;
    width: calc(100% - #{$sidebar-width-lg});
    margin-left: $sidebar-width-lg;
    display: flex;
    flex-direction: column;
    -webkit-transition: margin .1s ease, width .1s ease;
    transition: margin .1s ease, width .1s ease;
    .page-content {
      flex-grow: 1;
      padding: 25px;
      margin-top: $navbar-height;
      @media(max-width: 767px) {
        padding: 25px 15px;
      }
    }
    &.full-page {
      width: 100%;
      margin-left: 0;
      .page-content {
        margin-top: 0;
      }
    }
    @media(max-width: 991px) {
      margin-left: 0;
      width: 100%;
    }
  }
}

.page-content {
  .main-content {
  }
  .content-nav-wrapper {
    padding: 0;
    position: sticky;
    top: 80px;
    height: calc(100vh - 6rem);
    overflow-y: auto;
    border-left: 1px solid $border-color;
    display: none;
    @media(min-width: 1200px) {
      display: block;
    }
    .content-nav {
      padding: 0px 25px;
      .nav-item {
        .nav-link {
          padding: 0;
          height: 30px;
          white-space: nowrap;
          color: $text-muted;
          display: flex;
          align-items: center;
        }
      }
    }
  }
}