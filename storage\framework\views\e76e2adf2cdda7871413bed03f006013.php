
<?php $__env->startSection('agent'); ?>



<div class="page-content">

				<nav class="page-breadcrumb">
					<ol class="breadcrumb">
	  
					</ol>
				</nav>

				<div class="row">
					<div class="col-md-12 grid-margin stretch-card">
            <div class="card">
              <div class="card-body">
                <h6 class="card-title">Schedule Request All </h6>
               
                <div class="table-responsive">
                  <table id="dataTableExample" class="table">
                    <thead>
                      <tr>
                        <th>Sl </th>
                        <th>User </th> 
                        <th>Property </th> 
                        <th>Date </th> 
                        <th>Time </th> 
                        <th>Status </th> 
                        <th>Action </th> 
                      </tr>
                    </thead>
                    <tbody>
                   <?php $__currentLoopData = $usermsg; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                      <tr>
                        <td><?php echo e($key+1); ?></td> 
                        <td><?php echo e($item['user']['name']); ?></td> 
                        <td><?php echo e($item['property']['property_name']); ?></td> 
                        <td><?php echo e($item->tour_date); ?></td> 
                        <td><?php echo e($item->tour_time); ?></td> 
                        <td>
                      <?php if($item->status == 1): ?>
                        <span class="badge rounded-pill bg-success">Confirmed</span>
                      <?php elseif($item->status == 2): ?>
                        <span class="badge rounded-pill bg-danger">Rejected</span>
                      <?php else: ?>
                        <span class="badge rounded-pill bg-warning">Pending</span>
                      <?php endif; ?>

                        </td>
                        <td>

        <a href="<?php echo e(route('agent.details.schedule',$item->id)); ?>" class="btn btn-inverse-info" title="Details"> <i data-feather="eye"></i> </a>
  
       <a href="<?php echo e(route('agent.delete.property',$item->id)); ?>" class="btn btn-inverse-danger" id="delete" title="Delete"> <i data-feather="trash-2"></i>  </a>
                        </td> 
                      </tr>
                     <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
					</div>
				</div>

			</div>









<?php $__env->stopSection(); ?>
<?php echo $__env->make('agent.agent_dashboard', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\FinalProject\realestate\resources\views/agent/schedule/schedule_request.blade.php ENDPATH**/ ?>