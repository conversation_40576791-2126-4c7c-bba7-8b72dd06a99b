{"version": 3, "sources": ["demo4/style.scss", "../../node_modules/bootstrap/scss/_root.scss", "../../node_modules/bootstrap/scss/vendor/_rfs.scss", "../../node_modules/bootstrap/scss/_reboot.scss", "../../node_modules/bootstrap/scss/_variables.scss", "theme-dark/_variables.scss", "../../node_modules/bootstrap/scss/mixins/_border-radius.scss", "../../node_modules/bootstrap/scss/_type.scss", "../../node_modules/bootstrap/scss/mixins/_lists.scss", "../../node_modules/bootstrap/scss/_images.scss", "../../node_modules/bootstrap/scss/mixins/_image.scss", "../../node_modules/bootstrap/scss/_containers.scss", "../../node_modules/bootstrap/scss/mixins/_container.scss", "../../node_modules/bootstrap/scss/mixins/_breakpoints.scss", "../../node_modules/bootstrap/scss/_grid.scss", "../../node_modules/bootstrap/scss/mixins/_grid.scss", "../../node_modules/bootstrap/scss/_tables.scss", "../../node_modules/bootstrap/scss/mixins/_table-variants.scss", "../../node_modules/bootstrap/scss/forms/_labels.scss", "../../node_modules/bootstrap/scss/forms/_form-text.scss", "../../node_modules/bootstrap/scss/forms/_form-control.scss", "../../node_modules/bootstrap/scss/mixins/_transition.scss", "../../node_modules/bootstrap/scss/mixins/_gradients.scss", "../../node_modules/bootstrap/scss/forms/_form-select.scss", "../../node_modules/bootstrap/scss/forms/_form-check.scss", "../../node_modules/bootstrap/scss/forms/_form-range.scss", "../../node_modules/bootstrap/scss/forms/_floating-labels.scss", "../../node_modules/bootstrap/scss/forms/_input-group.scss", "../../node_modules/bootstrap/scss/mixins/_forms.scss", "../../node_modules/bootstrap/scss/_buttons.scss", "../../node_modules/bootstrap/scss/mixins/_buttons.scss", "../../node_modules/bootstrap/scss/_transitions.scss", "../../node_modules/bootstrap/scss/_dropdown.scss", "../../node_modules/bootstrap/scss/mixins/_caret.scss", "../../node_modules/bootstrap/scss/_button-group.scss", "../../node_modules/bootstrap/scss/_nav.scss", "../../node_modules/bootstrap/scss/_navbar.scss", "../../node_modules/bootstrap/scss/_card.scss", "../../node_modules/bootstrap/scss/_accordion.scss", "../../node_modules/bootstrap/scss/_breadcrumb.scss", "../../node_modules/bootstrap/scss/_pagination.scss", "../../node_modules/bootstrap/scss/mixins/_pagination.scss", "../../node_modules/bootstrap/scss/_badge.scss", "../../node_modules/bootstrap/scss/_alert.scss", "../../node_modules/bootstrap/scss/mixins/_alert.scss", "../../node_modules/bootstrap/scss/_progress.scss", "../../node_modules/bootstrap/scss/_list-group.scss", "../../node_modules/bootstrap/scss/mixins/_list-group.scss", "../../node_modules/bootstrap/scss/_close.scss", "../../node_modules/bootstrap/scss/_toasts.scss", "../../node_modules/bootstrap/scss/_modal.scss", "../../node_modules/bootstrap/scss/mixins/_backdrop.scss", "../../node_modules/bootstrap/scss/_tooltip.scss", "../../node_modules/bootstrap/scss/mixins/_reset-text.scss", "../../node_modules/bootstrap/scss/_popover.scss", "../../node_modules/bootstrap/scss/_carousel.scss", "../../node_modules/bootstrap/scss/mixins/_clearfix.scss", "../../node_modules/bootstrap/scss/_spinners.scss", "../../node_modules/bootstrap/scss/helpers/_color-bg.scss", "../../node_modules/bootstrap/scss/helpers/_colored-links.scss", "../../node_modules/bootstrap/scss/helpers/_ratio.scss", "../../node_modules/bootstrap/scss/helpers/_position.scss", "../../node_modules/bootstrap/scss/helpers/_stacks.scss", "../../node_modules/bootstrap/scss/helpers/_visually-hidden.scss", "../../node_modules/bootstrap/scss/mixins/_visually-hidden.scss", "../../node_modules/bootstrap/scss/helpers/_stretched-link.scss", "../../node_modules/bootstrap/scss/helpers/_text-truncation.scss", "../../node_modules/bootstrap/scss/mixins/_text-truncate.scss", "../../node_modules/bootstrap/scss/helpers/_vr.scss", "../../node_modules/bootstrap/scss/mixins/_utilities.scss", "../../node_modules/bootstrap/scss/utilities/_api.scss", "common/mixins/_animation.scss", "common/mixins/_width.scss", "common/_background.scss", "common/_reset.scss", "common/_misc.scss", "common/_helpers.scss", "common/_typography.scss", "common/_demo.scss", "demo4/_horizontal-wrapper.scss", "demo4/_variables.scss", "demo4/_navbar.scss", "common/mixins/_misc.scss", "common/components/_badges.scss", "common/components/_bootstrap-alert.scss", "common/components/_breadcrumbs.scss", "common/components/_buttons.scss", "common/mixins/_buttons.scss", "common/components/_cards.scss", "common/components/_dropdown.scss", "common/components/_forms.scss", "common/components/_icons.scss", "common/components/_nav.scss", "common/components/_pagination.scss", "common/components/_tables.scss", "common/components/_timeline.scss", "common/components/_chat.scss", "common/components/_auth.scss", "common/components/email/_inbox.scss", "theme-dark/components/plugin-overrides/_ace.scss", "theme-dark/components/plugin-overrides/_apex-charts.scss", "theme-dark/components/plugin-overrides/_data-tables.scss", "theme-dark/components/plugin-overrides/_dropify.scss", "theme-dark/components/plugin-overrides/_dropzone.scss", "theme-dark/components/plugin-overrides/_flatpickr.scss", "theme-dark/components/plugin-overrides/_full-calendar.scss", "theme-dark/components/plugin-overrides/_jquery-flot.scss", "theme-dark/components/plugin-overrides/_morrisjs.scss", "theme-dark/components/plugin-overrides/_peity.scss", "theme-dark/components/plugin-overrides/_perfect-scrollbar.scss", "theme-dark/components/plugin-overrides/_sweet-alert.scss", "theme-dark/components/plugin-overrides/_select2.scss", "theme-dark/components/plugin-overrides/_easymde.scss", "theme-dark/components/plugin-overrides/_tags-input.scss", "theme-dark/components/plugin-overrides/_tinymce.scss", "theme-dark/components/plugin-overrides/_typeahead.scss", "theme-dark/components/plugin-overrides/_wizard.scss"], "names": [], "mappings": ";AAAA;AAAA;AAAA;AAAA;AAAA;ACAA;EAQI;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAIA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAIA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAIA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAGF;EACA;EACA;EACA;EAMA;EACA;EACA;EAOA;EC4PI,qBALI;EDrPR;EACA;EACA;EAIA;EAIA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EAGA;EACA;EAEA;EAEA;;;AExDF;AAAA;AAAA;EAGE;;;AAeE;EANJ;IAOM;;;;AAcN;EACE;EACA;EDmPI,WALI;EC5OR;EACA;EACA;EACA;EACA;EACA;EACA;;;AASF;EACE;EACA,OCijB4B;EDhjB5B;EACA;EACA,SEmH4B;;;AFzG9B;EACE;EACA,eEmG4B;EFhG5B,aEiG4B;EFhG5B,aCwf4B;;;ADpf9B;ED6MQ;;AAlKJ;EC3CJ;IDoNQ;;;;AC/MR;EDwMQ;;AAlKJ;ECtCJ;ID+MQ;;;;AC1MR;EDmMQ;;AAlKJ;ECjCJ;ID0MQ;;;;ACrMR;ED0LM,WALI;;;AChLV;EDqLM,WALI;;;AC3KV;EDgLM,WALI;;;AChKV;EACE;EACA,eEE6B;;;AFQ/B;EACE;EACA;EACA;;;AAMF;EACE;EACA;EACA;;;AAMF;AAAA;EAEE;;;AAGF;AAAA;AAAA;EAGE;EACA;;;AAGF;AAAA;AAAA;AAAA;EAIE;;;AAGF;EACE,aEViC;;;AFenC;EACE;EACA;;;AAMF;EACE;;;AAQF;AAAA;EAEE,aEjCiC;;;AFyCnC;EDmFM,WALI;;;ACvEV;EACE,SC+a4B;ED9a5B;;;AASF;AAAA;EAEE;ED+DI,WALI;ECxDR;EACA;;;AAGF;EAAM;;;AACN;EAAM;;;AAKN;EACE;EACA,iBEvH6B;;AFyH7B;EACE;;;AAWF;EAEE;EACA;;;AAOJ;AAAA;AAAA;AAAA;EAIE,aCkR4B;EF7PxB,WALI;;;ACRV;EACE;EACA;EACA;EACA;EDSI,WALI;;ACCR;EDII,WALI;ECGN;EACA;;;AAIJ;EDHM,WALI;ECUR;EACA;;AAGA;EACE;;;AAIJ;EACE;EDfI,WALI;ECsBR,OCsyCkC;EDryClC,kBCsyCkC;EE1kDhC;;AHuSF;EACE;EDtBE,WALI;;;ACsCV;EACE;;;AAMF;AAAA;EAEE;;;AAQF;EACE;EACA;;;AAGF;EACE,aE3JqC;EF4JrC,gBE5JqC;EF6JrC,OEpVa;EFqVb;;;AAOF;EAEE;EACA;;;AAGF;AAAA;AAAA;AAAA;AAAA;AAAA;EAME;EACA;EACA;;;AAQF;EACE;;;AAMF;EAEE;;;AAQF;EACE;;;AAKF;AAAA;AAAA;AAAA;AAAA;EAKE;EACA;EDrHI,WALI;EC4HR;;;AAIF;AAAA;EAEE;;;AAKF;EACE;;;AAGF;EAGE;;AAGA;EACE;;;AAOJ;EACE;;;AAQF;AAAA;AAAA;AAAA;EAIE;;AAGE;AAAA;AAAA;AAAA;EACE;;;AAON;EACE;EACA;;;AAKF;EACE;;;AAUF;EACE;EACA;EACA;EACA;;;AAQF;EACE;EACA;EACA;EACA,eC8I4B;EFxVtB;EC6MN;;AD/WE;ECwWJ;ID/LQ;;;ACwMN;EACE;;;AAOJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAOE;;;AAGF;EACE;;;AASF;EACE;EACA;;;AAQF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;EACE;;;AAKF;EACE;;;AAOF;EACE;EACA;;;AAKF;EACE;;;AAKF;EACE;;;AAOF;EACE;EACA;;;AAQF;EACE;;;AAQF;EACE;;;AIpkBF;ELyQM,WALI;EKlQR,aHwkB4B;;;AGnkB5B;ELsQM;EKlQJ,aHyjBkB;EGxjBlB,aHwiB0B;;AFzc1B;EKpGF;IL6QM;;;;AK7QN;ELsQM;EKlQJ,aHyjBkB;EGxjBlB,aHwiB0B;;AFzc1B;EKpGF;IL6QM;;;;AK7QN;ELsQM;EKlQJ,aHyjBkB;EGxjBlB,aHwiB0B;;AFzc1B;EKpGF;IL6QM;;;;AK7QN;ELsQM;EKlQJ,aHyjBkB;EGxjBlB,aHwiB0B;;AFzc1B;EKpGF;IL6QM;;;;AK7QN;ELsQM;EKlQJ,aHyjBkB;EGxjBlB,aHwiB0B;;AFzc1B;EKpGF;IL6QM;;;;AK7QN;ELsQM;EKlQJ,aHyjBkB;EGxjBlB,aHwiB0B;;AFzc1B;EKpGF;IL6QM;;;;AKrPR;ECvDE;EACA;;;AD2DF;EC5DE;EACA;;;AD8DF;EACE;;AAEA;EACE,cHgkB0B;;;AGtjB9B;ELoNM,WALI;EK7MR;;;AAIF;EACE,eFNO;EHmNH,WALI;;AKrMR;EACE;;;AAIJ;EACE;EACA,eFhBO;EHmNH,WALI;EK5LR,OFzFa;;AE2Fb;EACE;;;AEhGJ;ECIE;EAGA;;;ADDF;EACE,SL68CkC;EK58ClC,kBJuG6B;EItG7B;EHGE;EIRF;EAGA;;;ADcF;EAEE;;;AAGF;EACE;EACA;;;AAGF;EP+PM,WALI;EOxPR,OJ7Ba;;;AMLb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;ECHA;EACA;EACA;EACA;EACA;EACA;EACA;;;ACsDE;EF5CE;IACE,WP6ae;;;ASlYnB;EF5CE;IACE,WP6ae;;;ASlYnB;EF5CE;IACE,WP6ae;;;ASlYnB;EF5CE;IACE,WP6ae;;;ASlYnB;EF5CE;IACE,WP6ae;;;AU5brB;ECAA;EACA;EACA;EACA;EAEA;EACA;EACA;;ADJE;ECaF;EACA;EACA;EACA;EACA;EACA;;;AA+CI;EACE;;;AAGF;EApCJ;EACA;;;AAcA;EACE;EACA;;;AAFF;EACE;EACA;;;AAFF;EACE;EACA;;;AAFF;EACE;EACA;;;AAFF;EACE;EACA;;;AAFF;EACE;EACA;;;AA+BE;EAhDJ;EACA;;;AAqDQ;EAhEN;EACA;;;AA+DM;EAhEN;EACA;;;AA+DM;EAhEN;EACA;;;AA+DM;EAhEN;EACA;;;AA+DM;EAhEN;EACA;;;AA+DM;EAhEN;EACA;;;AA+DM;EAhEN;EACA;;;AA+DM;EAhEN;EACA;;;AA+DM;EAhEN;EACA;;;AA+DM;EAhEN;EACA;;;AA+DM;EAhEN;EACA;;;AA+DM;EAhEN;EACA;;;AAuEQ;EAxDV;;;AAwDU;EAxDV;;;AAwDU;EAxDV;;;AAwDU;EAxDV;;;AAwDU;EAxDV;;;AAwDU;EAxDV;;;AAwDU;EAxDV;;;AAwDU;EAxDV;;;AAwDU;EAxDV;;;AAwDU;EAxDV;;;AAwDU;EAxDV;;;AAmEM;AAAA;EAEE;;;AAGF;AAAA;EAEE;;;AAPF;AAAA;EAEE;;;AAGF;AAAA;EAEE;;;AAPF;AAAA;EAEE;;;AAGF;AAAA;EAEE;;;AAPF;AAAA;EAEE;;;AAGF;AAAA;EAEE;;;AAPF;AAAA;EAEE;;;AAGF;AAAA;EAEE;;;AAPF;AAAA;EAEE;;;AAGF;AAAA;EAEE;;;AAPF;AAAA;EAEE;;;AAGF;AAAA;EAEE;;;AAPF;AAAA;EAEE;;;AAGF;AAAA;EAEE;;;AF1DN;EEUE;IACE;;EAGF;IApCJ;IACA;;EAcA;IACE;IACA;;EAFF;IACE;IACA;;EAFF;IACE;IACA;;EAFF;IACE;IACA;;EAFF;IACE;IACA;;EAFF;IACE;IACA;;EA+BE;IAhDJ;IACA;;EAqDQ;IAhEN;IACA;;EA+DM;IAhEN;IACA;;EA+DM;IAhEN;IACA;;EA+DM;IAhEN;IACA;;EA+DM;IAhEN;IACA;;EA+DM;IAhEN;IACA;;EA+DM;IAhEN;IACA;;EA+DM;IAhEN;IACA;;EA+DM;IAhEN;IACA;;EA+DM;IAhEN;IACA;;EA+DM;IAhEN;IACA;;EA+DM;IAhEN;IACA;;EAuEQ;IAxDV;;EAwDU;IAxDV;;EAwDU;IAxDV;;EAwDU;IAxDV;;EAwDU;IAxDV;;EAwDU;IAxDV;;EAwDU;IAxDV;;EAwDU;IAxDV;;EAwDU;IAxDV;;EAwDU;IAxDV;;EAwDU;IAxDV;;EAwDU;IAxDV;;EAmEM;AAAA;IAEE;;EAGF;AAAA;IAEE;;EAPF;AAAA;IAEE;;EAGF;AAAA;IAEE;;EAPF;AAAA;IAEE;;EAGF;AAAA;IAEE;;EAPF;AAAA;IAEE;;EAGF;AAAA;IAEE;;EAPF;AAAA;IAEE;;EAGF;AAAA;IAEE;;EAPF;AAAA;IAEE;;EAGF;AAAA;IAEE;;EAPF;AAAA;IAEE;;EAGF;AAAA;IAEE;;EAPF;AAAA;IAEE;;EAGF;AAAA;IAEE;;;AF1DN;EEUE;IACE;;EAGF;IApCJ;IACA;;EAcA;IACE;IACA;;EAFF;IACE;IACA;;EAFF;IACE;IACA;;EAFF;IACE;IACA;;EAFF;IACE;IACA;;EAFF;IACE;IACA;;EA+BE;IAhDJ;IACA;;EAqDQ;IAhEN;IACA;;EA+DM;IAhEN;IACA;;EA+DM;IAhEN;IACA;;EA+DM;IAhEN;IACA;;EA+DM;IAhEN;IACA;;EA+DM;IAhEN;IACA;;EA+DM;IAhEN;IACA;;EA+DM;IAhEN;IACA;;EA+DM;IAhEN;IACA;;EA+DM;IAhEN;IACA;;EA+DM;IAhEN;IACA;;EA+DM;IAhEN;IACA;;EAuEQ;IAxDV;;EAwDU;IAxDV;;EAwDU;IAxDV;;EAwDU;IAxDV;;EAwDU;IAxDV;;EAwDU;IAxDV;;EAwDU;IAxDV;;EAwDU;IAxDV;;EAwDU;IAxDV;;EAwDU;IAxDV;;EAwDU;IAxDV;;EAwDU;IAxDV;;EAmEM;AAAA;IAEE;;EAGF;AAAA;IAEE;;EAPF;AAAA;IAEE;;EAGF;AAAA;IAEE;;EAPF;AAAA;IAEE;;EAGF;AAAA;IAEE;;EAPF;AAAA;IAEE;;EAGF;AAAA;IAEE;;EAPF;AAAA;IAEE;;EAGF;AAAA;IAEE;;EAPF;AAAA;IAEE;;EAGF;AAAA;IAEE;;EAPF;AAAA;IAEE;;EAGF;AAAA;IAEE;;EAPF;AAAA;IAEE;;EAGF;AAAA;IAEE;;;AF1DN;EEUE;IACE;;EAGF;IApCJ;IACA;;EAcA;IACE;IACA;;EAFF;IACE;IACA;;EAFF;IACE;IACA;;EAFF;IACE;IACA;;EAFF;IACE;IACA;;EAFF;IACE;IACA;;EA+BE;IAhDJ;IACA;;EAqDQ;IAhEN;IACA;;EA+DM;IAhEN;IACA;;EA+DM;IAhEN;IACA;;EA+DM;IAhEN;IACA;;EA+DM;IAhEN;IACA;;EA+DM;IAhEN;IACA;;EA+DM;IAhEN;IACA;;EA+DM;IAhEN;IACA;;EA+DM;IAhEN;IACA;;EA+DM;IAhEN;IACA;;EA+DM;IAhEN;IACA;;EA+DM;IAhEN;IACA;;EAuEQ;IAxDV;;EAwDU;IAxDV;;EAwDU;IAxDV;;EAwDU;IAxDV;;EAwDU;IAxDV;;EAwDU;IAxDV;;EAwDU;IAxDV;;EAwDU;IAxDV;;EAwDU;IAxDV;;EAwDU;IAxDV;;EAwDU;IAxDV;;EAwDU;IAxDV;;EAmEM;AAAA;IAEE;;EAGF;AAAA;IAEE;;EAPF;AAAA;IAEE;;EAGF;AAAA;IAEE;;EAPF;AAAA;IAEE;;EAGF;AAAA;IAEE;;EAPF;AAAA;IAEE;;EAGF;AAAA;IAEE;;EAPF;AAAA;IAEE;;EAGF;AAAA;IAEE;;EAPF;AAAA;IAEE;;EAGF;AAAA;IAEE;;EAPF;AAAA;IAEE;;EAGF;AAAA;IAEE;;EAPF;AAAA;IAEE;;EAGF;AAAA;IAEE;;;AF1DN;EEUE;IACE;;EAGF;IApCJ;IACA;;EAcA;IACE;IACA;;EAFF;IACE;IACA;;EAFF;IACE;IACA;;EAFF;IACE;IACA;;EAFF;IACE;IACA;;EAFF;IACE;IACA;;EA+BE;IAhDJ;IACA;;EAqDQ;IAhEN;IACA;;EA+DM;IAhEN;IACA;;EA+DM;IAhEN;IACA;;EA+DM;IAhEN;IACA;;EA+DM;IAhEN;IACA;;EA+DM;IAhEN;IACA;;EA+DM;IAhEN;IACA;;EA+DM;IAhEN;IACA;;EA+DM;IAhEN;IACA;;EA+DM;IAhEN;IACA;;EA+DM;IAhEN;IACA;;EA+DM;IAhEN;IACA;;EAuEQ;IAxDV;;EAwDU;IAxDV;;EAwDU;IAxDV;;EAwDU;IAxDV;;EAwDU;IAxDV;;EAwDU;IAxDV;;EAwDU;IAxDV;;EAwDU;IAxDV;;EAwDU;IAxDV;;EAwDU;IAxDV;;EAwDU;IAxDV;;EAwDU;IAxDV;;EAmEM;AAAA;IAEE;;EAGF;AAAA;IAEE;;EAPF;AAAA;IAEE;;EAGF;AAAA;IAEE;;EAPF;AAAA;IAEE;;EAGF;AAAA;IAEE;;EAPF;AAAA;IAEE;;EAGF;AAAA;IAEE;;EAPF;AAAA;IAEE;;EAGF;AAAA;IAEE;;EAPF;AAAA;IAEE;;EAGF;AAAA;IAEE;;EAPF;AAAA;IAEE;;EAGF;AAAA;IAEE;;EAPF;AAAA;IAEE;;EAGF;AAAA;IAEE;;;AF1DN;EEUE;IACE;;EAGF;IApCJ;IACA;;EAcA;IACE;IACA;;EAFF;IACE;IACA;;EAFF;IACE;IACA;;EAFF;IACE;IACA;;EAFF;IACE;IACA;;EAFF;IACE;IACA;;EA+BE;IAhDJ;IACA;;EAqDQ;IAhEN;IACA;;EA+DM;IAhEN;IACA;;EA+DM;IAhEN;IACA;;EA+DM;IAhEN;IACA;;EA+DM;IAhEN;IACA;;EA+DM;IAhEN;IACA;;EA+DM;IAhEN;IACA;;EA+DM;IAhEN;IACA;;EA+DM;IAhEN;IACA;;EA+DM;IAhEN;IACA;;EA+DM;IAhEN;IACA;;EA+DM;IAhEN;IACA;;EAuEQ;IAxDV;;EAwDU;IAxDV;;EAwDU;IAxDV;;EAwDU;IAxDV;;EAwDU;IAxDV;;EAwDU;IAxDV;;EAwDU;IAxDV;;EAwDU;IAxDV;;EAwDU;IAxDV;;EAwDU;IAxDV;;EAwDU;IAxDV;;EAwDU;IAxDV;;EAmEM;AAAA;IAEE;;EAGF;AAAA;IAEE;;EAPF;AAAA;IAEE;;EAGF;AAAA;IAEE;;EAPF;AAAA;IAEE;;EAGF;AAAA;IAEE;;EAPF;AAAA;IAEE;;EAGF;AAAA;IAEE;;EAPF;AAAA;IAEE;;EAGF;AAAA;IAEE;;EAPF;AAAA;IAEE;;EAGF;AAAA;IAEE;;EAPF;AAAA;IAEE;;EAGF;AAAA;IAEE;;EAPF;AAAA;IAEE;;EAGF;AAAA;IAEE;;;ACrHV;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA,eXiEO;EWhEP;EACA,gBZqoB4B;EYpoB5B;;AAOA;EACE;EACA;EACA,qBZic0B;EYhc1B;;AAGF;EACE;;AAGF;EACE;;;AAIJ;EACE;;;AAOF;EACE;;;AAUA;EACE;;;AAeF;EACE;;AAGA;EACE;;;AAOJ;EACE;;AAGF;EACE;;;AAUF;EACE;EACA;;;AAMF;EACE;EACA;;;AAQJ;EACE;EACA;;;AAQA;EACE;EACA;;;ACrIF;EAOE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;;;AAlBF;EAOE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;;;AAlBF;EAOE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;;;AAlBF;EAOE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;;;AAlBF;EAOE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;;;AAlBF;EAOE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;;;AAlBF;EAOE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;;;AAlBF;EAOE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;;;AD0IA;EACE;EACA;;;AHpFF;EGkFA;IACE;IACA;;;AHpFF;EGkFA;IACE;IACA;;;AHpFF;EGkFA;IACE;IACA;;;AHpFF;EGkFA;IACE;IACA;;;AHpFF;EGkFA;IACE;IACA;;;AE5JN;EACE,ed8xBsC;;;AcrxBxC;EACE;EACA;EACA;EhBoRI,WALI;EgB3QR,ad+hB4B;;;Ac3hB9B;EACE;EACA;EhB0QI,WALI;;;AgBjQV;EACE;EACA;EhBoQI,WALI;;;AiB5RV;EACE,YfsxBsC;EFtflC,WALI;EiBvRR,OdEa;;;AePf;AAAA;EACE;EACA;EACA;ElB8RI,WALI;EkBtRR,af6JiC;Ee5JjC,ahByiB4B;EgBxiB5B,Of2G6B;Ee1G7B,kBfgPsC;Ee/OtC;EACA;EACA;EdGE;EeHE,YDMJ;;ACFI;EDhBN;AAAA;ICiBQ;;;ADGN;AAAA;EACE;;AAEA;AAAA;EACE;;AAKJ;AAAA;EACE,OfqF2B;EepF3B,kBf0NoC;EezNpC,cf4NoC;Ee3NpC;EAKE,Yf2KkC;;AepKtC;AAAA;EAEE;;AAIF;AAAA;EACE,Of9CW;EegDX;;AAQF;AAAA;EAEE,kBf0LoC;EevLpC;;AAIF;AAAA;EACE;EACA;EACA,mBfqIoC;EepIpC,OfwC2B;EiBnH7B,kBjB8QsC;EejMpC;EACA;EACA;EACA;EACA,yBhB0Y0B;EgBzY1B;ECtEE,YDuEF;;ACnEE;EDuDJ;AAAA;ICtDM;;;ADqEN;AAAA;EACE,kBhBs4B8B;;;AgB73BlC;EACE;EACA;EACA;EACA;EACA,ahB2c4B;EgB1c5B,Ofa6B;EeZ7B;EACA;EACA;;AAEA;EACE;;AAGF;EAEE;EACA;;;AAWJ;EACE,YhBstBsC;EgBrtBtC;ElBkKI,WALI;EI7QN;;AcoHF;EACE;EACA;EACA,mBfqFoC;;;AejFxC;EACE,YhB0sBsC;EgBzsBtC;ElBqJI,WALI;EI7QN;;AciIF;EACE;EACA;EACA,mBf4EoC;;;AepEtC;AAAA;EACE,YhBurBoC;;AgBprBtC;EACE,YhBorBoC;;AgBjrBtC;EACE,YhBirBoC;;;AgB5qBxC;EACE,OhB+qBsC;EgB9qBtC,QhBwqBsC;EgBvqBtC,SfkCsC;;AehCtC;EACE;;AAGF;EACE;EdpKA;;AcwKF;EdxKE;;Ac4KF;EAAoB,QhBypBkB;;AgBxpBtC;EAAoB,QhBypBkB;;;AmBp1BxC;EACE;EACA;EACA;EACA;ErB4RI,WALI;EqBpRR,alB2JiC;EkB1JjC,anBuiB4B;EmBtiB5B,OlByG6B;EkBxG7B,kBlB8OsC;EkB7OtC;EACA;EACA,qBnBw5BkC;EmBv5BlC,iBnBw5BkC;EmBv5BlC;EjBDE;EeHE,YEOJ;EACA;;AFJI;EEfN;IFgBQ;;;AEKN;EACE,clBqOoC;EkBpOpC;EAKE,YnBy5B4B;;AmBr5BhC;EAEE,elB4KoC;EkB3KpC;;AAGF;EAEE,kBlBtCW;;AkB2Cb;EACE;EACA;;;AAIJ;EACE,alBqKsC;EkBpKtC,gBlBoKsC;EkBnKtC,clBoKsC;EHsElC,WALI;EI7QN;;;AiB6CJ;EACE,alBiKsC;EkBhKtC,gBlBgKsC;EkB/JtC,clBgKsC;EHkElC,WALI;EI7QN;;;AkBfJ;EACE;EACA,YpB41BwC;EoB31BxC,cpB41BwC;EoB31BxC,epB41BwC;;AoB11BxC;EACE;EACA;;;AAIJ;EACE,epBk1BwC;EoBj1BxC;EACA;;AAEA;EACE;EACA;EACA;;;AAIJ;EACE,OnB0OsC;EmBzOtC,QnByOsC;EmBxOtC;EACA;EACA,kBnB4NsC;EmB3NtC;EACA;EACA;EACA,QnBmOsC;EmBlOtC;EACA;;AAGA;ElBvBE;;AkB2BF;EAEE,epB8zBsC;;AoB3zBxC;EACE,QpBqzBsC;;AoBlzBxC;EACE,cnBiNoC;EmBhNpC;EACA,YnB2JoC;;AmBxJtC;EACE,kBnB3Bc;EmB4Bd,cnB5Bc;;AmB8Bd;EAII;;AAIJ;EAII;;AAKN;EACE,kBnBhDc;EmBiDd,cnBjDc;EmBsDZ;;AAIJ;EACE;EACA;EACA,SpB6xBuC;;AoBtxBvC;EACE;EACA,SpBoxBqC;;;AoBtwB3C;EACE,cpB+wBgC;;AoB7wBhC;EACE,OpB2wB8B;EoB1wB9B;EACA;EACA;ElB3GA;EeHE,YGgHF;;AH5GE;EGsGJ;IHrGM;;;AG6GJ;EACE;;AAGF;EACE,qBpB0wB4B;EoBrwB1B;;AAKN;EACE,epBqvB8B;EoBpvB9B;;AAEA;EACE;EACA;;;AAKN;EACE;EACA,cpBmuBgC;;;AoBhuBlC;EACE;EACA;EACA;;AAIE;EACE;EACA;EACA,SpBolBwB;;;AqBzvB9B;EACE;EACA;EACA;EACA;EACA;;AAEA;EACE;;AAIA;EAA0B,YrBq8Ba;;AqBp8BvC;EAA0B,YrBo8Ba;;AqBj8BzC;EACE;;AAGF;EACE,OrBs7BuC;EqBr7BvC,QrBq7BuC;EqBp7BvC;EHzBF,kBjB+BgB;EoBJd,QrBq7BuC;EEj8BvC;EeHE,YIkBF;EACA;;AJfE;EIMJ;IJLM;;;AIgBJ;EHjCF,kBlBq9ByC;;AqB/6BzC;EACE,OrB+5B8B;EqB95B9B,QrB+5B8B;EqB95B9B;EACA,QrB85B8B;EqB75B9B,kBpBsOoC;EoBrOpC;EnB7BA;;AmBkCF;EACE,OrB25BuC;EqB15BvC,QrB05BuC;EkB78BzC,kBjB+BgB;EoBsBd,QrB25BuC;EEj8BvC;EeHE,YI4CF;EACA;;AJzCE;EIiCJ;IJhCM;;;AI0CJ;EH3DF,kBlBq9ByC;;AqBr5BzC;EACE,OrBq4B8B;EqBp4B9B,QrBq4B8B;EqBp4B9B;EACA,QrBo4B8B;EqBn4B9B,kBpB4MoC;EoB3MpC;EnBvDA;;AmB4DF;EACE;;AAEA;EACE,kBpBzES;;AoB4EX;EACE,kBpB7ES;;;AqBVf;EACE;;AAEA;AAAA;AAAA;AAAA;EAGE,QtB+9B8B;EsB99B9B,atB+9B8B;;AsB59BhC;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;ELNE,YKOF;;ALHE;EKVJ;ILWM;;;AKKN;AAAA;AAAA;EAEE;;AAEA;AAAA;AAAA;EACE;;AAGF;AAAA;AAAA;AAAA;AAAA;EAEE,atBq8B4B;EsBp8B5B,gBtBq8B4B;;AsBl8B9B;AAAA;AAAA;EACE,atBg8B4B;EsB/7B5B,gBtBg8B4B;;AsB57BhC;EACE,atB07B8B;EsBz7B9B,gBtB07B8B;;AsBn7B9B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EACE,StBm7B4B;EsBl7B5B,WtBm7B4B;;AsB96B9B;AAAA;EACE,StB46B4B;EsB36B5B,WtB46B4B;;AsBv6B9B;EACE;;;AClEN;EACE;EACA;EACA;EACA;EACA;;AAEA;AAAA;AAAA;AAAA;EAGE;EACA;EACA;EACA;;AAIF;AAAA;AAAA;AAAA;EAGE;;AAMF;EACE;EACA;;AAEA;EACE;;;AAWN;EACE;EACA;EACA;EzBoPI,WALI;EyB7OR,atBoHiC;EsBnHjC,avBggB4B;EuB/f5B,OtBkE6B;EsBjE7B;EACA;EACA,kBtB0NsC;EsBzNtC;ErBtCE;;;AqBgDJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAIE;EzB8NI,WALI;EI7QN;;;AqByDJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAIE;EzBqNI,WALI;EI7QN;;;AqBkEJ;AAAA;EAEE;;;AAaE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;ErBjEA;EACA;;AqByEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;ErB1EA;EACA;;AqBsFF;AAAA;AAAA;AAAA;AAAA;AAAA;EAGE;ErB5EA;EACA;;;AsBzBF;EACE;EACA;EACA,YxB+vBoC;EFtflC,WALI;E0BjQN,OxBi+BqB;;;AwB99BvB;EACE;EACA;EACA;EACA;EACA;EACA;EACA;E1B4PE,WALI;E0BpPN,OAvBc;EAwBd,kBAvBiB;EtBHjB;;;AsB+BA;AAAA;AAAA;AAAA;EAEE;;;AA9CF;AAAA;AAAA;EAoDE,cxBs8BmB;EwBn8BjB,exBsxBgC;EwBrxBhC;EACA;EACA;EACA;;AAGF;AAAA;AAAA;EACE,cxB27BiB;EwB17BjB,YA/Ca;;;AAjBjB;AAAA;AAAA;EAyEI,exBowBgC;EwBnwBhC;;;AA1EJ;EAiFE,cxBy6BmB;;AwBt6BjB;EAEE,exBm1B8B;EwBl1B9B;EACA;EACA;;AAIJ;EACE,cxB45BiB;EwB35BjB,YA9Ea;;;AAjBjB;EAuGI;;;AAvGJ;EA8GE,cxB44BmB;;AwB14BnB;EACE,kBxBy4BiB;;AwBt4BnB;EACE,YApGa;;AAuGf;EACE,OxBi4BiB;;;AwB53BrB;EACE;;;AA/HF;AAAA;AAAA;AAAA;AAAA;EAuII;;AAIF;AAAA;AAAA;AAAA;AAAA;EACE;;;AAzHN;EACE;EACA;EACA,YxB+vBoC;EFtflC,WALI;E0BjQN,OxBi+BqB;;;AwB99BvB;EACE;EACA;EACA;EACA;EACA;EACA;EACA;E1B4PE,WALI;E0BpPN,OAvBc;EAwBd,kBAvBiB;EtBHjB;;;AsB+BA;AAAA;AAAA;AAAA;EAEE;;;AA9CF;AAAA;AAAA;EAoDE,cxBs8BmB;EwBn8BjB,exBsxBgC;EwBrxBhC;EACA;EACA;EACA;;AAGF;AAAA;AAAA;EACE,cxB27BiB;EwB17BjB,YA/Ca;;;AAjBjB;AAAA;AAAA;EAyEI,exBowBgC;EwBnwBhC;;;AA1EJ;EAiFE,cxBy6BmB;;AwBt6BjB;EAEE,exBm1B8B;EwBl1B9B;EACA;EACA;;AAIJ;EACE,cxB45BiB;EwB35BjB,YA9Ea;;;AAjBjB;EAuGI;;;AAvGJ;EA8GE,cxB44BmB;;AwB14BnB;EACE,kBxBy4BiB;;AwBt4BnB;EACE,YApGa;;AAuGf;EACE,OxBi4BiB;;;AwB53BrB;EACE;;;AA/HF;AAAA;AAAA;AAAA;AAAA;EAyII;;AAEF;AAAA;AAAA;AAAA;AAAA;EACE;;;AC9IR;AAAA;AAAA;EAEE;EACA;EACA;E3B6RI,oBALI;E2BtRR;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAGA;EACA;EACA;E3B6QI,WALI;E2BtQR;EACA;EACA;EACA;EAGA;EACA;EACA;EACA;EvBhBE;EgBfF,kBOiCqB;ERrBjB,YQuBJ;;ARnBI;EQhBN;AAAA;AAAA;IRiBQ;;;AQoBN;EACE;EAEA;EACA;;AAGF;EAEE;EP9CF,kBO+CuB;EACrB;EACA;EAKE;;AAIJ;EAKE;EACA;EAGA;;AAGA;EAKI;;AAKN;EAGE;EACA;EACA;EAEA;EACA;;;AAYF;AAAA;AAAA;AAAA;ECpFA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;ADuEA;ECpFA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;ADuEA;ECpFA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;ADuEA;ECpFA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;ADuEA;ECpFA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;ADuEA;ECpFA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;ADuEA;ECpFA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;ADuEA;ECpFA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;ADiGA;ECrFA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;ADwEA;ECrFA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;ADwEA;ECrFA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;ADwEA;ECrFA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;ADwEA;ECrFA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;ADwEA;ECrFA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;ADwEA;ECrFA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;ADwEA;ECrFA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;ADoFF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA,iBxB/B6B;;AwBsC7B;EACE;;AAGF;EACE;;;AAWJ;ECnHE;EACA;E5BoOI,oBALI;E4B7NR;;;ADoHF;ECvHE;EACA;E5BoOI,oBALI;E4B7NR;;;ACnEF;EVgBM,YUfJ;;AVmBI;EUpBN;IVqBQ;;;AUlBN;EACE;;;AAMF;EACE;;;AAIJ;EACE;EACA;EVDI,YUEJ;;AVEI;EULN;IVMQ;;;AUDN;EACE;EACA;EVNE,YUOF;;AVHE;EUAJ;IVCM;;;;AWpBR;AAAA;AAAA;AAAA;AAAA;AAAA;EAME;;;AAGF;EACE;;ACmBE;EACE;EACA,a7BmewB;E6BlexB,gB7BiewB;E6BhexB;EAhCJ;EACA;EACA;EACA;;AAqDE;EACE;;;ADzCN;EAEE;EACA;EACA;EACA;E9B8QI,yBALI;E8BvQR;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAGA;EACA,S5Bk+BkC;E4Bj+BlC;EACA;EACA;EACA;E9BiPI,WALI;E8B1OR;EACA;EACA;EACA;EACA;EACA;E1BxCE;;A0B4CF;EACE;EACA;EACA;;;AAwBA;EACE;;AAEA;EACE;EACA;;;AAIJ;EACE;;AAEA;EACE;EACA;;;AnBzCJ;EmB2BA;IACE;;EAEA;IACE;IACA;;EAIJ;IACE;;EAEA;IACE;IACA;;;AnBzCJ;EmB2BA;IACE;;EAEA;IACE;IACA;;EAIJ;IACE;;EAEA;IACE;IACA;;;AnBzCJ;EmB2BA;IACE;;EAEA;IACE;IACA;;EAIJ;IACE;;EAEA;IACE;IACA;;;AnBzCJ;EmB2BA;IACE;;EAEA;IACE;IACA;;EAIJ;IACE;;EAEA;IACE;IACA;;;AnBzCJ;EmB2BA;IACE;;EAEA;IACE;IACA;;EAIJ;IACE;;EAEA;IACE;IACA;;;AAUN;EACE;EACA;EACA;EACA;;ACxFA;EACE;EACA,a7BmewB;E6BlexB,gB7BiewB;E6BhexB;EAzBJ;EACA;EACA;EACA;;AA8CE;EACE;;;ADoEJ;EACE;EACA;EACA;EACA;EACA;;ACtGA;EACE;EACA,a7BmewB;E6BlexB,gB7BiewB;E6BhexB;EAlBJ;EACA;EACA;EACA;;AAuCE;EACE;;AD8EF;EACE;;;AAMJ;EACE;EACA;EACA;EACA;EACA;;ACvHA;EACE;EACA,a7BmewB;E6BlexB,gB7BiewB;E6BhexB;;AAWA;EACE;;AAGF;EACE;EACA,c7BgdsB;E6B/ctB,gB7B8csB;E6B7ctB;EA9BN;EACA;EACA;;AAiCE;EACE;;AD+FF;EACE;;;AAON;EACE;EACA;EACA;EACA;EACA;;;AAMF;EACE;EACA;EACA;EACA;EACA,a3BXiC;E2BYjC;EACA;EAEA;EACA;EACA;;AAEA;EAEE;EVxLF,kBU0LuB;;AAGvB;EAEE;EACA;EVhMF,kBUiMuB;;AAGvB;EAEE;EACA;EACA;;;AAMJ;EACE;;;AAIF;EACE;EACA;EACA;E9B2EI,WALI;E8BpER;EACA;;;AAIF;EACE;EACA;EACA;;;AAIF;EAEE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AEpPF;AAAA;EAEE;EACA;EACA;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;EACE;EACA;;AAKF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAME;;;AAKJ;EACE;EACA;EACA;;AAEA;EACE;;;AAIJ;E5BhBI;;A4BoBF;AAAA;EAEE;;AAIF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;E5BVE;EACA;;A4BmBF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;E5BNE;EACA;;;A4BwBJ;EACE;EACA;;AAEA;EAGE;;AAGF;EACE;;;AAIJ;EACE;EACA;;;AAGF;EACE;EACA;;;AAoBF;EACE;EACA;EACA;;AAEA;AAAA;EAEE;;AAGF;AAAA;EAEE;;AAIF;AAAA;AAAA;AAAA;AAAA;AAAA;E5B1FE;EACA;;A4B8FF;AAAA;AAAA;AAAA;AAAA;AAAA;E5B7GE;EACA;;;A6BxBJ;EAEE;EACA;EAEA;EACA;EACA;EACA;EAGA;EACA;EACA;EACA;EACA;;;AAGF;EACE;EACA;EjC4QI,WALI;EiCrQR;EACA;EdZI,YccJ;;AdVI;EcGN;IdFQ;;;AcWN;EAEE;;AAKF;EACE;EACA;EACA;;;AAQJ;EAEE;EACA;EACA;EACA;EACA;EACA;EACA;EAGA;;AAEA;EACE;EACA;EACA;E7BtCA;EACA;;A6BwCA;EAGE;EACA;;AAGF;EAEE;EACA;EACA;;AAIJ;AAAA;EAEE;EACA;EACA;;AAGF;EAEE;E7BjEA;EACA;;;A6B2EJ;EAEE;EACA;EACA;;AAGA;EACE;EACA;E7B9FA;;A6BiGA;EACE;EACA;EACA;;AAIJ;AAAA;EAEE;EbzHF,kBa0HuB;;;AAUvB;AAAA;EAEE;EACA;;;AAKF;AAAA;EAEE;EACA;EACA;;;AAMF;AAAA;EACE;;;AAUF;EACE;;AAEF;EACE;;;ACpKJ;EAEE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAGA;EACA;EACA;EACA;EACA;EACA;;AAMA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EACE;EACA;EACA;EACA;;AAoBJ;EACE;EACA;EACA;ElCkOI,WALI;EkC3NR;EAEA;;AAEA;EAEE;;;AAUJ;EAEE;EACA;EAEA;EACA;EACA;EACA;EAGA;EACA;EACA;EACA;EACA;;AAEA;AAAA;EAEE;;AAGF;EACE;;;AASJ;EACE,ahC46BkC;EgC36BlC,gBhC26BkC;EgC16BlC;;AAEA;AAAA;AAAA;EAGE;;;AAaJ;EACE;EACA;EAGA;;;AAIF;EACE;ElCiJI,WALI;EkC1IR;EACA;EACA;EACA;E9BtIE;EeHE,Ye2IJ;;AfvII;Ee+HN;If9HQ;;;AewIN;EACE;;AAGF;EACE;EACA;EACA;;;AAMJ;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAGF;EACE;EACA;;;AvBxHE;EuBoIA;IAEI;IACA;;EAEA;IACE;;EAEA;IACE;;EAGF;IACE;IACA;;EAIJ;IACE;;EAGF;IACE;IACA;;EAGF;IACE;;EAGF;IAEE;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;If5NJ,Ye8NI;;EAGA;IACE;;EAGF;IACE;IACA;IACA;IACA;;;AvB1LR;EuBoIA;IAEI;IACA;;EAEA;IACE;;EAEA;IACE;;EAGF;IACE;IACA;;EAIJ;IACE;;EAGF;IACE;IACA;;EAGF;IACE;;EAGF;IAEE;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;If5NJ,Ye8NI;;EAGA;IACE;;EAGF;IACE;IACA;IACA;IACA;;;AvB1LR;EuBoIA;IAEI;IACA;;EAEA;IACE;;EAEA;IACE;;EAGF;IACE;IACA;;EAIJ;IACE;;EAGF;IACE;IACA;;EAGF;IACE;;EAGF;IAEE;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;If5NJ,Ye8NI;;EAGA;IACE;;EAGF;IACE;IACA;IACA;IACA;;;AvB1LR;EuBoIA;IAEI;IACA;;EAEA;IACE;;EAEA;IACE;;EAGF;IACE;IACA;;EAIJ;IACE;;EAGF;IACE;IACA;;EAGF;IACE;;EAGF;IAEE;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;If5NJ,Ye8NI;;EAGA;IACE;;EAGF;IACE;IACA;IACA;IACA;;;AvB1LR;EuBoIA;IAEI;IACA;;EAEA;IACE;;EAEA;IACE;;EAGF;IACE;IACA;;EAIJ;IACE;;EAGF;IACE;IACA;;EAGF;IACE;;EAGF;IAEE;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;If5NJ,Ye8NI;;EAGA;IACE;;EAGF;IACE;IACA;IACA;IACA;;;AAtDR;EAEI;EACA;;AAEA;EACE;;AAEA;EACE;;AAGF;EACE;EACA;;AAIJ;EACE;;AAGF;EACE;EACA;;AAGF;EACE;;AAGF;EAEE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;Ef5NJ,Ye8NI;;AAGA;EACE;;AAGF;EACE;EACA;EACA;EACA;;;AAiBZ;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AC9QF;EAEE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAGA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;E/BdE;;A+BkBF;EACE;EACA;;AAGF;EACE;EACA;;AAEA;EACE;E/BnBF;EACA;;A+BsBA;EACE;E/BVF;EACA;;A+BgBF;AAAA;EAEE;;;AAIJ;EAGE;EACA;EACA;;;AAGF;EACE;;;AAGF;EACE;EACA;;;AAGF;EACE;;;AAQA;EACE;;;AAQJ;EACE;EACA;EACA;EACA;EACA;;AAEA;E/BxFE;;;A+B6FJ;EACE;EACA;EACA;EACA;;AAEA;E/BnGE;;;A+B6GJ;EACE;EACA;EACA;EACA;;AAEA;EACE;EACA;;;AAIJ;EACE;EACA;;;AAIF;EACE;EACA;EACA;EACA;EACA;EACA;E/BrIE;;;A+ByIJ;AAAA;AAAA;EAGE;;;AAGF;AAAA;E/BtII;EACA;;;A+B0IJ;AAAA;E/B7HI;EACA;;;A+ByIF;EACE;;AxBtHA;EwBkHJ;IAQI;IACA;;EAGA;IAEE;IACA;;EAEA;IACE;IACA;;EAKA;I/BtKJ;IACA;;E+BwKM;AAAA;IAGE;;EAEF;AAAA;IAGE;;EAIJ;I/BvKJ;IACA;;E+ByKM;AAAA;IAGE;;EAEF;AAAA;IAGE;;;;AC/NZ;EAEE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAIF;EACE;EACA;EACA;EACA;EACA;EpCiQI,WALI;EoC1PR;EACA;EACA;EACA;EhCtBE;EgCwBF;EjB3BI,YiB4BJ;;AjBxBI;EiBWN;IjBVQ;;;AiByBN;EACE;EACA;EACA;;AAEA;EACE;EACA;;AAKJ;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EjBlDE,YiBmDF;;AjB/CE;EiBsCJ;IjBrCM;;;AiBiDN;EACE;;AAGF;EACE;EACA;EACA;EACA;;;AAIJ;EACE;;;AAGF;EACE;EACA;EACA;;AAEA;EhC/DE;EACA;;AgCiEA;EhClEA;EACA;;AgCsEF;EACE;;AAIF;EhC9DE;EACA;;AgCiEE;EhClEF;EACA;;AgCsEA;EhCvEA;EACA;;;AgC4EJ;EACE;;;AASA;EACE;;AAGF;EACE;EACA;EhCpHA;;AgCuHA;EAAgB;;AAChB;EAAe;;AAEf;EhC1HA;;;AiCnBJ;EAEE;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EAGA;EACA;EACA;EACA;ErCqRI,WALI;EqC9QR;EACA;EjCAE;;;AiCMF;EACE;;AAEA;EACE;EACA;EACA;EACA;;AAIJ;EACE;;;ACrCJ;EAEE;EACA;EtCkSI,2BALI;EsC3RR;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAGA;EhCpBA;EACA;;;AgCuBF;EACE;EACA;EACA;EtCsQI,WALI;EsC/PR;EAEA;EACA;EnBpBI,YmBqBJ;;AnBjBI;EmBQN;InBPQ;;;AmBkBN;EACE;EACA;EAEA;EACA;;AAGF;EACE;EACA;EACA;EACA,SpCioCgC;EoChoChC;;AAGF;EAEE;EACA;ElBtDF,kBkBuDuB;EACrB;;AAGF;EAEE;EACA;EACA;EACA;;;AAKF;EACE,apComCgC;;AoC/lC9B;ElC9BF;EACA;;AkCmCE;ElClDF;EACA;;;AkCkEJ;EClGE;EACA;EvCgSI,2BALI;EuCzRR;;;ADmGF;ECtGE;EACA;EvCgSI,2BALI;EuCzRR;;;ACFF;EAEE;EACA;ExC6RI,sBALI;EwCtRR;EACA;EACA;EAGA;EACA;ExCqRI,WALI;EwC9QR;EACA;EACA;EACA;EACA;EACA;EpCJE;;AoCSF;EACE;;;AAKJ;EACE;EACA;;;AChCF;EAEE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAGA;EACA;EACA;EACA;EACA;EACA;ErCFE;;;AqCOJ;EAEE;;;AAIF;EACE,atCuIiC;;;AsC/HnC;EACE,evC63C8B;;AuC13C9B;EACE;EACA;EACA;EACA;EACA;;;AAgBF;EChEA;EACA;EACA;;AAMA;EACE;;;ADuDF;EChEA;EACA;EACA;;AAMA;EACE;;;ADuDF;EChEA;EACA;EACA;;AAMA;EACE;;;ADuDF;EChEA;EACA;EACA;;AAMA;EACE;;;ADuDF;EChEA;EACA;EACA;;AAMA;EACE;;;ADuDF;EChEA;EACA;EACA;;AAMA;EACE;;;ADuDF;EChEA;EACA;EACA;;AAMA;EACE;;;ADuDF;EChEA;EACA;EACA;;AAMA;EACE;;;ACPF;EACE;IAAK,uBzCy6C2B;;;AyCp6CpC;EAEE;E3CyRI,yBALI;E2ClRR;EACA;EACA;EACA;EACA;EACA;EAGA;EACA;EACA;E3C6QI,WALI;E2CtQR;EvCPE;;;AuCYJ;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;ExBvBI,YwBwBJ;;AxBpBI;EwBWN;IxBVQ;;;;AwBsBR;EvBCE;EuBCA;;;AAIA;EACE;;AAGE;EAJJ;IAKM;;;;AClDR;EAEE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAGA;EACA;EAGA;EACA;ExCXE;;;AwCeJ;EACE;EACA;;AAEA;EAEE;EACA;;;AASJ;EACE;EACA;EACA;;AAGA;EAEE;EACA;EACA;EACA;;AAGF;EACE;EACA;;;AAQJ;EACE;EACA;EACA;EACA;EAEA;EACA;;AAEA;ExCvDE;EACA;;AwC0DF;ExC7CE;EACA;;AwCgDF;EAEE;EACA;EACA;;AAIF;EACE;EACA;EACA;EACA;;AAGF;EACE;;AAEA;EACE;EACA;;;AAaF;EACE;;AAGE;ExCtDJ;EAZA;;AwCuEI;ExCvEJ;EAYA;;AwCgEI;EACE;;AAGF;EACE;EACA;;AAEA;EACE;EACA;;;AjCrFR;EiC6DA;IACE;;EAGE;IxCtDJ;IAZA;;EwCuEI;IxCvEJ;IAYA;;EwCgEI;IACE;;EAGF;IACE;IACA;;EAEA;IACE;IACA;;;AjCrFR;EiC6DA;IACE;;EAGE;IxCtDJ;IAZA;;EwCuEI;IxCvEJ;IAYA;;EwCgEI;IACE;;EAGF;IACE;IACA;;EAEA;IACE;IACA;;;AjCrFR;EiC6DA;IACE;;EAGE;IxCtDJ;IAZA;;EwCuEI;IxCvEJ;IAYA;;EwCgEI;IACE;;EAGF;IACE;IACA;;EAEA;IACE;IACA;;;AjCrFR;EiC6DA;IACE;;EAGE;IxCtDJ;IAZA;;EwCuEI;IxCvEJ;IAYA;;EwCgEI;IACE;;EAGF;IACE;IACA;;EAEA;IACE;IACA;;;AjCrFR;EiC6DA;IACE;;EAGE;IxCtDJ;IAZA;;EwCuEI;IxCvEJ;IAYA;;EwCgEI;IACE;;EAGF;IACE;IACA;;EAEA;IACE;IACA;;;AAcZ;ExC/II;;AwCkJF;EACE;;AAEA;EACE;;;ACrKJ;EACE,ODkLyB;ECjLzB,kBDgLsB;;AC7KpB;EAEE,OD4KqB;EC3KrB;;AAGF;EACE,O1CXO;E0CYP,kBDsKqB;ECrKrB,cDqKqB;;;ACnL3B;EACE,ODkLyB;ECjLzB,kBDgLsB;;AC7KpB;EAEE,OD4KqB;EC3KrB;;AAGF;EACE,O1CXO;E0CYP,kBDsKqB;ECrKrB,cDqKqB;;;ACnL3B;EACE,ODkLyB;ECjLzB,kBDgLsB;;AC7KpB;EAEE,OD4KqB;EC3KrB;;AAGF;EACE,O1CXO;E0CYP,kBDsKqB;ECrKrB,cDqKqB;;;ACnL3B;EACE,ODkLyB;ECjLzB,kBDgLsB;;AC7KpB;EAEE,OD4KqB;EC3KrB;;AAGF;EACE,O1CXO;E0CYP,kBDsKqB;ECrKrB,cDqKqB;;;ACnL3B;EACE,ODkLyB;ECjLzB,kBDgLsB;;AC7KpB;EAEE,OD4KqB;EC3KrB;;AAGF;EACE,O1CXO;E0CYP,kBDsKqB;ECrKrB,cDqKqB;;;ACnL3B;EACE,ODkLyB;ECjLzB,kBDgLsB;;AC7KpB;EAEE,OD4KqB;EC3KrB;;AAGF;EACE,O1CXO;E0CYP,kBDsKqB;ECrKrB,cDqKqB;;;ACnL3B;EACE,ODkLyB;ECjLzB,kBDgLsB;;AC7KpB;EAEE,OD4KqB;EC3KrB;;AAGF;EACE,O1CXO;E0CYP,kBDsKqB;ECrKrB,cDqKqB;;;ACnL3B;EACE,ODkLyB;ECjLzB,kBDgLsB;;AC7KpB;EAEE,OD4KqB;EC3KrB;;AAGF;EACE,O1CXO;E0CYP,kBDsKqB;ECrKrB,cDqKqB;;;AElL7B;EACE;EACA,O3C6X2B;E2C5X3B,Q3C4X2B;E2C3X3B;EACA,O3CCa;E2CAb;EACA;E1COE;E0CLF,S5C4iD2B;;A4CziD3B;EACE,O3CPW;E2CQX;EACA,S5CuiDyB;;A4CpiD3B;EACE;EACA,Y3C4LoC;E2C3LpC,S5CkiDyB;;A4C/hD3B;EAEE;EACA;EACA,S5C4hDyB;;;A4CxhD7B;EACE,Q5CwhD2B;;;A6C9jD7B;EAEE;EACA;EACA;EACA;E/CgSI,sBALI;E+CzRR;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAGA;EACA;E/CkRI,WALI;E+C3QR;EACA;EACA;EACA;EACA;EACA;E3CPE;;A2CUF;EACE;;AAGF;EACE;;;AAIJ;EACE;EACA,S7Cm/BkC;E6Cl/BlC;EACA;EACA;;AAEA;EACE;;;AAIJ;EACE;EACA;EACA;EACA;EACA;EACA;EACA;E3C7BE;EACA;;A2C+BF;EACE;EACA;;;AAIJ;EACE;EACA;;;AC3DF;EAEE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAGA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAGA;;;AAOF;EACE;EACA;EACA;EAEA;;AAGA;E7B5CI,Y6B6CF;EACA,W7C6S4B;;AgBvV1B;E6BwCJ;I7BvCM;;;A6B2CN;EACE,W9Ck1CgC;;A8C90ClC;EACE,W9C+0CgC;;;A8C30CpC;EACE;;AAEA;EACE;EACA;;AAGF;EACE;;;AAIJ;EACE;EACA;EACA;;;AAIF;EACE;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;E5CrFE;E4CyFF;;;AAIF;EAEE;EACA;EACA;EClHA;EACA;EACA;EACA,SDkH0B;ECjH1B;EACA;EACA,kBD+G4D;;AC5G5D;EAAS;;AACT;EAAS,SD2GiF;;;AAK5F;EACE;EACA;EACA;EACA;EACA;EACA;E5CtGE;EACA;;A4CwGF;EACE;EACA;;;AAKJ;EACE;EACA;;;AAKF;EACE;EAGA;EACA;;;AAIF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;E5C1HE;EACA;;A4C+HF;EACE;;;ArC5GA;EqCkHF;IACE;IACA;;EAIF;IACE;IACA;IACA;;EAGF;IACE;;;ArC/HA;EqCoIF;AAAA;IAEE;;;ArCtIA;EqC2IF;IACE;;;AAUA;EACE;EACA;EACA;EACA;;AAEA;EACE;EACA;E5C1MJ;;A4C8ME;AAAA;E5C9MF;;A4CmNE;EACE;;;ArC3JJ;EqCyIA;IACE;IACA;IACA;IACA;;EAEA;IACE;IACA;I5C1MJ;;E4C8ME;AAAA;I5C9MF;;E4CmNE;IACE;;;ArC3JJ;EqCyIA;IACE;IACA;IACA;IACA;;EAEA;IACE;IACA;I5C1MJ;;E4C8ME;AAAA;I5C9MF;;E4CmNE;IACE;;;ArC3JJ;EqCyIA;IACE;IACA;IACA;IACA;;EAEA;IACE;IACA;I5C1MJ;;E4C8ME;AAAA;I5C9MF;;E4CmNE;IACE;;;ArC3JJ;EqCyIA;IACE;IACA;IACA;IACA;;EAEA;IACE;IACA;I5C1MJ;;E4C8ME;AAAA;I5C9MF;;E4CmNE;IACE;;;ArC3JJ;EqCyIA;IACE;IACA;IACA;IACA;;EAEA;IACE;IACA;I5C1MJ;;E4C8ME;AAAA;I5C9MF;;E4CmNE;IACE;;;AEtOR;EAEE;EACA;EACA;EACA;EACA;ElD8RI,wBALI;EkDvRR;EACA;EACA;EACA;EACA;EACA;EAGA;EACA;EACA;EACA;ECnBA,ajDgiB4B;EiD9hB5B;EACA,ahDmKiC;EgDlKjC,ajD+iB4B;EiD9iB5B;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EnDsRI,WALI;EkDrQR;EACA;;AAEA;EAAS;;AAET;EACE;EACA;EACA;;AAEA;EACE;EACA;EACA;EACA;;;AAKN;EACE;;AAEA;EACE;EACA;EACA;;;AAIJ;AACA;EACE;EACA;EACA;;AAEA;EACE;EACA;EACA;;;AAIJ;AAEA;EACE;;AAEA;EACE;EACA;EACA;;;AAIJ;AACA;EACE;EACA;EACA;;AAEA;EACE;EACA;EACA;;;AAIJ;AAkBA;EACE;EACA;EACA;EACA;EACA;E9ClGE;;;AgDnBJ;EAEE;EACA;EpDkSI,wBALI;EoD3RR;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EpDyRI,+BALI;EoDlRR;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAGA;EACA;EACA;EDzBA,ajDgiB4B;EiD9hB5B;EACA,ahDmKiC;EgDlKjC,ajD+iB4B;EiD9iB5B;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EnDsRI,WALI;EoDhQR;EACA;EACA;EACA;EhDhBE;;AgDoBF;EACE;EACA;EACA;;AAEA;EAEE;EACA;EACA;EACA;EACA;EACA;;;AAMJ;EACE;;AAEA;EAEE;;AAGF;EACE;EACA;;AAGF;EACE;EACA;;;AAKN;AAEE;EACE;EACA;EACA;;AAEA;EAEE;;AAGF;EACE;EACA;;AAGF;EACE;EACA;;;AAKN;AAGE;EACE;;AAEA;EAEE;;AAGF;EACE;EACA;;AAGF;EACE;EACA;;AAKJ;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAIJ;AAEE;EACE;EACA;EACA;;AAEA;EAEE;;AAGF;EACE;EACA;;AAGF;EACE;EACA;;;AAKN;AAkBA;EACE;EACA;EpDiHI,WALI;EoD1GR;EACA;EACA;EhD5JE;EACA;;AgD8JF;EACE;;;AAIJ;EACE;EACA;;;ACrLF;EACE;;;AAGF;EACE;;;AAGF;EACE;EACA;EACA;;ACtBA;EACE;EACA;EACA;;;ADuBJ;EACE;EACA;EACA;EACA;EACA;EACA;ElClBI,YkCmBJ;;AlCfI;EkCQN;IlCPQ;;;;AkCiBR;AAAA;AAAA;EAGE;;;AAGF;AACA;AAAA;EAEE;;;AAGF;AAAA;EAEE;;;AAGF;AAQE;EACE;EACA;EACA;;AAGF;AAAA;AAAA;EAGE;EACA;;AAGF;AAAA;EAEE;EACA;ElC/DE,YkCgEF;;AlC5DE;EkCwDJ;AAAA;IlCvDM;;;;AkCoER;AAAA;EAEE;EACA;EACA;EACA;EAEA;EACA;EACA;EACA,OnD65CmC;EmD55CnC;EACA,OlDhGa;EkDiGb;EACA;EACA;EACA,SnDw5CmC;EiBj/C/B,YkC0FJ;;AlCtFI;EkCqEN;AAAA;IlCpEQ;;;AkCwFN;AAAA;AAAA;EAEE,OlD1GW;EkD2GX;EACA;EACA,SnDg5CiC;;;AmD74CrC;EACE;;;AAGF;EACE;;;AAKF;AAAA;EAEE;EACA,OnDi5CmC;EmDh5CnC,QnDg5CmC;EmD/4CnC;EACA;EACA;;;AAGF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;EACE;;;AAEF;EACE;;;AAQF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA,cnDy1CmC;EmDx1CnC;EACA,anDu1CmC;EmDt1CnC;;AAEA;EACE;EACA;EACA,OnDs1CiC;EmDr1CjC,QnDs1CiC;EmDr1CjC;EACA,cnDs1CiC;EmDr1CjC,anDq1CiC;EmDp1CjC;EACA;EACA,kBlDjLW;EkDkLX;EACA;EAEA;EACA;EACA,SnD60CiC;EiBz/C/B,YkC6KF;;AlCzKE;EkCwJJ;IlCvJM;;;AkC2KN;EACE,SnD00CiC;;;AmDj0CrC;EACE;EACA;EACA,QnDo0CmC;EmDn0CnC;EACA,anDi0CmC;EmDh0CnC,gBnDg0CmC;EmD/zCnC,OlD5Ma;EkD6Mb;;;AAMA;AAAA;EAEE,QnDm0CiC;;AmDh0CnC;EACE,kBlD/MW;;AkDkNb;EACE,OlDnNW;;;AoDXf;AAAA;EAEE;EACA;EACA;EACA;EAEA;EACA;;;AAIF;EACE;IAAK;;;AAIP;EAEE;EACA;EACA;EACA;EACA;EACA;EAGA;EACA;;;AAGF;EAEE;EACA;EACA;;;AASF;EACE;IACE;;EAEF;IACE;IACA;;;AAKJ;EAEE;EACA;EACA;EACA;EACA;EAGA;EACA;;;AAGF;EACE;EACA;;;AAIA;EACE;AAAA;IAEE;;;AD/EJ;EACE;EACA;EACA;;;AEAF;EACE;EACA;;;AAFF;EACE;EACA;;;AAFF;EACE;EACA;;;AAFF;EACE;EACA;;;AAFF;EACE;EACA;;;AAFF;EACE;EACA;;;AAFF;EACE;EACA;;;AAFF;EACE;EACA;;;ACNF;EACE;;AAGE;EAEE;;;AANN;EACE;;AAGE;EAEE;;;AANN;EACE;;AAGE;EAEE;;;AANN;EACE;;AAGE;EAEE;;;AANN;EACE;;AAGE;EAEE;;;AANN;EACE;;AAGE;EAEE;;;AANN;EACE;;AAGE;EAEE;;;AANN;EACE;;AAGE;EAEE;;;ACLR;EACE;EACA;;AAEA;EACE;EACA;EACA;;AAGF;EACE;EACA;EACA;EACA;EACA;;;AAKF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;ACrBJ;EACE;EACA;EACA;EACA;EACA,SzD6gCkC;;;AyD1gCpC;EACE;EACA;EACA;EACA;EACA,SzDqgCkC;;;AyD7/BhC;EACE;EACA;EACA,SzDy/B8B;;;AyDt/BhC;EACE;EACA;EACA,SzDm/B8B;;;ASp9BhC;EgDxCA;IACE;IACA;IACA,SzDy/B8B;;EyDt/BhC;IACE;IACA;IACA,SzDm/B8B;;;ASp9BhC;EgDxCA;IACE;IACA;IACA,SzDy/B8B;;EyDt/BhC;IACE;IACA;IACA,SzDm/B8B;;;ASp9BhC;EgDxCA;IACE;IACA;IACA,SzDy/B8B;;EyDt/BhC;IACE;IACA;IACA,SzDm/B8B;;;ASp9BhC;EgDxCA;IACE;IACA;IACA,SzDy/B8B;;EyDt/BhC;IACE;IACA;IACA,SzDm/B8B;;;ASp9BhC;EgDxCA;IACE;IACA;IACA,SzDy/B8B;;EyDt/BhC;IACE;IACA;IACA,SzDm/B8B;;;A0DlhCpC;EACE;EACA;EACA;EACA;;;AAGF;EACE;EACA;EACA;EACA;;;ACRF;AAAA;ECIE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;ACXA;EACE;EACA;EACA;EACA;EACA;EACA,S7DoZsC;E6DnZtC;;;ACRJ;ECAE;EACA;EACA;;;ACNF;EACE;EACA;EACA;EACA;EACA;EACA,S/DqL4B;;;AgEzHtB;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAIQ;EAGJ;;;AAPJ;EAIQ;EAGJ;;;AAPJ;EAIQ;EAGJ;;;AAPJ;EAIQ;EAGJ;;;AAPJ;EAIQ;EAGJ;;;AAPJ;EAIQ;EAGJ;;;AAPJ;EAIQ;EAGJ;;;AAPJ;EAIQ;EAGJ;;;AAPJ;EAIQ;EAGJ;;;AAjBJ;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AASF;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;EAAA;;;AAPJ;EAOI;EAAA;;;AAPJ;EAOI;EAAA;;;AAPJ;EAOI;EAAA;;;AAPJ;EAOI;EAAA;;;AAPJ;EAOI;EAAA;;;AAPJ;EAOI;EAAA;;;AAPJ;EAOI;EAAA;;;AAPJ;EAOI;EAAA;;;AAPJ;EAOI;EAAA;;;AAPJ;EAOI;EAAA;;;AAPJ;EAOI;EAAA;;;AAPJ;EAOI;EAAA;;;AAPJ;EAOI;EAAA;;;AAPJ;EAOI;EAAA;;;AAPJ;EAOI;EAAA;;;AAPJ;EAOI;EAAA;;;AAPJ;EAOI;EAAA;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;EAAA;;;AAPJ;EAOI;EAAA;;;AAPJ;EAOI;EAAA;;;AAPJ;EAOI;EAAA;;;AAPJ;EAOI;EAAA;;;AAPJ;EAOI;EAAA;;;AAPJ;EAOI;EAAA;;;AAPJ;EAOI;EAAA;;;AAPJ;EAOI;EAAA;;;AAPJ;EAOI;EAAA;;;AAPJ;EAOI;EAAA;;;AAPJ;EAOI;EAAA;;;AAPJ;EAOI;EAAA;;;AAPJ;EAOI;EAAA;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;EAAA;;;AAPJ;EAOI;EAAA;;;AAPJ;EAOI;EAAA;;;AAPJ;EAOI;EAAA;;;AAPJ;EAOI;EAAA;;;AAPJ;EAOI;EAAA;;;AAPJ;EAOI;EAAA;;;AAPJ;EAOI;EAAA;;;AAPJ;EAOI;EAAA;;;AAPJ;EAOI;EAAA;;;AAPJ;EAOI;EAAA;;;AAPJ;EAOI;EAAA;;;AAPJ;EAOI;EAAA;;;AAPJ;EAOI;EAAA;;;AAPJ;EAOI;EAAA;;;AAPJ;EAOI;EAAA;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AArBJ;AAcA;EAOI;EAAA;;;AAmBJ;AA1BA;EAIQ;EAGJ;;;AAPJ;EAIQ;EAGJ;;;AAPJ;EAIQ;EAGJ;;;AAPJ;EAIQ;EAGJ;;;AAPJ;EAIQ;EAGJ;;;AAPJ;EAIQ;EAGJ;;;AAPJ;EAIQ;EAGJ;;;AAPJ;EAIQ;EAGJ;;;AAPJ;EAIQ;EAGJ;;;AAPJ;EAIQ;EAGJ;;;AAPJ;EAIQ;EAGJ;;;AAPJ;EAIQ;EAGJ;;;AAPJ;EAIQ;EAGJ;;;AAPJ;EAIQ;EAGJ;;;AAPJ;EAIQ;EAGJ;;;AAjBJ;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AASF;EAIQ;EAGJ;;;AAPJ;EAIQ;EAGJ;;;AAPJ;EAIQ;EAGJ;;;AAPJ;EAIQ;EAGJ;;;AAPJ;EAIQ;EAGJ;;;AAPJ;EAIQ;EAGJ;;;AAPJ;EAIQ;EAGJ;;;AAPJ;EAIQ;EAGJ;;;AAPJ;EAIQ;EAGJ;;;AAPJ;EAIQ;EAGJ;;;AAPJ;EAIQ;EAGJ;;;AAPJ;EAIQ;EAGJ;;;AAjBJ;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AASF;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;EAAA;;;AAPJ;EAOI;EAAA;;;AAPJ;EAOI;EAAA;;;AAPJ;EAOI;EAAA;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AAPJ;EAOI;;;AxDVR;EwDGI;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;;AxDVR;EwDGI;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;;AxDVR;EwDGI;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;;AxDVR;EwDGI;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;;AxDVR;EwDGI;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;IAAA;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;;ACtDZ;ED+CQ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;;ACnCZ;ED4BQ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;EAPJ;IAOI;;;AEvEZ;EACE;IACE;IACA;;EAEF;IACE;IACA;IACA;;;AAIJ;EACE;EACQ;EACR,4BlEqI2B;EkEpI3B,oBlEoI2B;EkEnI3B;EACA;;;AAGF;EACE;IACE;;EAEF;IACE;;;AAIJ;EACE;;;AAGF;EACE;IACE;IACA;;EAEF;IACE;IACA;;;AAIJ;EACE;;;AAGF;EASE;EACA;EACA;EACA;;AAXA;EACE;IACE;;EAEF;IACE;;;;AAiBN;EA4BE;EACA;EACA;EACA;;AA5BA;EACE;IACE;IACA,OALG;IAMH,QANG;IAOH;IACA;;EAGF;IACE;IACA;IACA;IACA;IACA;;EAGF;IACE;IACA,OArBG;IAsBH,QAtBG;IAuBH;IACA;;;;ACzFJ;EAAyB;;;AACzB;EAA0B;;;AAC1B;EAA6B;;;AAC7B;EAA6B;;;AAE7B;EAA2B;;;AAC3B;EAA4B;;;AAC5B;EAA+B;;;AAC/B;EAA+B;;;AAR/B;EAAyB;;;AACzB;EAA0B;;;AAC1B;EAA6B;;;AAC7B;EAA6B;;;AAE7B;EAA2B;;;AAC3B;EAA4B;;;AAC5B;EAA+B;;;AAC/B;EAA+B;;;AAR/B;EAAyB;;;AACzB;EAA0B;;;AAC1B;EAA6B;;;AAC7B;EAA6B;;;AAE7B;EAA2B;;;AAC3B;EAA4B;;;AAC5B;EAA+B;;;AAC/B;EAA+B;;;AAR/B;EAAyB;;;AACzB;EAA0B;;;AAC1B;EAA6B;;;AAC7B;EAA6B;;;AAE7B;EAA2B;;;AAC3B;EAA4B;;;AAC5B;EAA+B;;;AAC/B;EAA+B;;;AAR/B;EAAyB;;;AACzB;EAA0B;;;AAC1B;EAA6B;;;AAC7B;EAA6B;;;AAE7B;EAA2B;;;AAC3B;EAA4B;;;AAC5B;EAA+B;;;AAC/B;EAA+B;;;AAR/B;EAAyB;;;AACzB;EAA0B;;;AAC1B;EAA6B;;;AAC7B;EAA6B;;;AAE7B;EAA2B;;;AAC3B;EAA4B;;;AAC5B;EAA+B;;;AAC/B;EAA+B;;;AAR/B;EAAyB;;;AACzB;EAA0B;;;AAC1B;EAA6B;;;AAC7B;EAA6B;;;AAE7B;EAA2B;;;AAC3B;EAA4B;;;AAC5B;EAA+B;;;AAC/B;EAA+B;;;AAR/B;EAAyB;;;AACzB;EAA0B;;;AAC1B;EAA6B;;;AAC7B;EAA6B;;;AAE7B;EAA2B;;;AAC3B;EAA4B;;;AAC5B;EAA+B;;;AAC/B;EAA+B;;;AAR/B;EAAyB;;;AACzB;EAA0B;;;AAC1B;EAA6B;;;AAC7B;EAA6B;;;AAE7B;EAA2B;;;AAC3B;EAA4B;;;AAC5B;EAA+B;;;AAC/B;EAA+B;;;AAR/B;EAAyB;;;AACzB;EAA0B;;;AAC1B;EAA6B;;;AAC7B;EAA6B;;;AAE7B;EAA2B;;;AAC3B;EAA4B;;;AAC5B;EAA+B;;;AAC/B;EAA+B;;;AAR/B;EAAyB;;;AACzB;EAA0B;;;AAC1B;EAA6B;;;AAC7B;EAA6B;;;AAE7B;EAA2B;;;AAC3B;EAA4B;;;AAC5B;EAA+B;;;AAC/B;EAA+B;;;AAR/B;EAAyB;;;AACzB;EAA0B;;;AAC1B;EAA6B;;;AAC7B;EAA6B;;;AAE7B;EAA2B;;;AAC3B;EAA4B;;;AAC5B;EAA+B;;;AAC/B;EAA+B;;;AAR/B;EAAyB;;;AACzB;EAA0B;;;AAC1B;EAA6B;;;AAC7B;EAA6B;;;AAE7B;EAA2B;;;AAC3B;EAA4B;;;AAC5B;EAA+B;;;AAC/B;EAA+B;;;AAR/B;EAAyB;;;AACzB;EAA0B;;;AAC1B;EAA6B;;;AAC7B;EAA6B;;;AAE7B;EAA2B;;;AAC3B;EAA4B;;;AAC5B;EAA+B;;;AAC/B;EAA+B;;;AAR/B;EAAyB;;;AACzB;EAA0B;;;AAC1B;EAA6B;;;AAC7B;EAA6B;;;AAE7B;EAA2B;;;AAC3B;EAA4B;;;AAC5B;EAA+B;;;AAC/B;EAA+B;;;AAR/B;EAAyB;;;AACzB;EAA0B;;;AAC1B;EAA6B;;;AAC7B;EAA6B;;;AAE7B;EAA2B;;;AAC3B;EAA4B;;;AAC5B;EAA+B;;;AAC/B;EAA+B;;;AAR/B;EAAyB;;;AACzB;EAA0B;;;AAC1B;EAA6B;;;AAC7B;EAA6B;;;AAE7B;EAA2B;;;AAC3B;EAA4B;;;AAC5B;EAA+B;;;AAC/B;EAA+B;;;AAR/B;EAAyB;;;AACzB;EAA0B;;;AAC1B;EAA6B;;;AAC7B;EAA6B;;;AAE7B;EAA2B;;;AAC3B;EAA4B;;;AAC5B;EAA+B;;;AAC/B;EAA+B;;;AAR/B;EAAyB;;;AACzB;EAA0B;;;AAC1B;EAA6B;;;AAC7B;EAA6B;;;AAE7B;EAA2B;;;AAC3B;EAA4B;;;AAC5B;EAA+B;;;AAC/B;EAA+B;;;AAR/B;EAAyB;;;AACzB;EAA0B;;;AAC1B;EAA6B;;;AAC7B;EAA6B;;;AAE7B;EAA2B;;;AAC3B;EAA4B;;;AAC5B;EAA+B;;;AAC/B;EAA+B;;;AAW/B;EAAc;;;AACd;EAAgB;;;AADhB;EAAc;;;AACd;EAAgB;;;AADhB;EAAc;;;AACd;EAAgB;;;AADhB;EAAc;;;AACd;EAAgB;;;AADhB;EAAc;;;AACd;EAAgB;;;AADhB;EAAc;;;AACd;EAAgB;;;AADhB;EAAc;;;AACd;EAAgB;;;AADhB;EAAc;;;AACd;EAAgB;;;AADhB;EAAc;;;AACd;EAAgB;;;AADhB;EAAc;;;AACd;EAAgB;;;AADhB;EAAc;;;AACd;EAAgB;;;AADhB;EAAc;;;AACd;EAAgB;;;AADhB;EAAc;;;AACd;EAAgB;;;AADhB;EAAc;;;AACd;EAAgB;;;AADhB;EAAc;;;AACd;EAAgB;;;AADhB;EAAc;;;AACd;EAAgB;;;AADhB;EAAc;;;AACd;EAAgB;;;AADhB;EAAc;;;AACd;EAAgB;;;AAWlB;EA/BE;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EA2B/B;IAAc;;EACd;IAAgB;;;A3DiBd;E2DrDF;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAmC/B;IAAc;;EACd;IAAgB;;;A3DSd;E2DrDF;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EA2C/B;IAAc;;EACd;IAAgB;;EAGhB;IAAa;;;A3DFX;E2DrDF;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAsD/B;IAAc;;EACd;IAAgB;;;A3DVd;E2DrDF;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EA8D/B;IAAc;;EACd;IAAc;;;A3DlBZ;E2DrDF;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAR/B;IAAyB;;EACzB;IAA0B;;EAC1B;IAA6B;;EAC7B;IAA6B;;EAE7B;IAA2B;;EAC3B;IAA4B;;EAC5B;IAA+B;;EAC/B;IAA+B;;EAsE/B;IAAe;;EACf;IAAe;;;ACxFf;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;ACAJ;EACE;EACA;;;AAGF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EA+BE;EACA;EACA;;;AAGF;AAAA;EAEE;EACA;;;AAGF;AAAA;AAAA;AAAA;EAIE;EACA,yBrEiE6B;;;AsErH/B;AAAA;AAAA;EAGE;;;AAIF;EACE;EACA;EACA;;;AAGF;EACE;EACA,atE8IiC;EsE7IjC;EACA,WtE8I+B;EsE7I/B;;;AClBF;EACE;;;AAGA;EADF;IAEI;;;;AAIF;EADF;IAEI;;;;AAIF;EADF;IAEI;;;;AAIF;EADF;IAEI;;;;AAMJ;EACE;EACA;EACA;;AACA;EACE;EACA;;;AAMJ;EACE;EACA;;;AAEF;EACE;EACA;;;AAEF;EACE;EACA;;;AAEF;EACE;EACA;;;AAEF;EACE;EACA;;;AAMF;EACE;;;AAKF;EAAS;;;AACT;EAAS;;;AACT;EAAS;;;AACT;EAAS;;;AACT;EAAS;;;AACT;EAAS;;;AACT;EAAS;;;AAIT;EACE;EACA;;;AAEF;EACE;EACA;;;AAEF;EACE;EACA;;;AAEF;EACE;EACA;;;AAEF;EACE;EACA;;;AAEF;EACE;EACA;;;AAKF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAKF;EACE;;;AAEF;EACE;;;AAMF;EAAU;;;AACV;EAAU;;;AACV;EAAU;;;AAEV;EAAU;;;AACV;EAAU;;;AACV;EAAU;;;AAEV;EAAU;;;AACV;EAAU;;;AACV;EAAU;;;AAEV;EAAU;;;AACV;EAAU;;;AACV;EAAU;;;AAKV;EAAQ;;;AACR;EAAS;;;AACT;EAAS;;;AACT;EAAS;;;AACT;EAAS;;;AACT;EAAS;;;AACT;EAAS;;;AACT;EAAS;;;AACT;EAAS;;;AACT;EAAS;;;AACT;EAAS;;;AACT;EAAU;;;ACtKV;EACE;EACA;;;AAKA;EACE,OxEuCY;;;AwExCd;EACE,OxEuCY;;;AwExCd;EACE,OxEuCY;;;AwExCd;EACE,OxEuCY;;;AwExCd;EACE,OxEuCY;;;AwExCd;EACE,OxEuCY;;;AwExCd;EACE,OxEuCY;;;AwExCd;EACE,OxEuCY;;;AwExCd;EACE,OxEuCY;;;AwExCd;EACE,OxEuCY;;;AwExCd;EACE,OxEuCY;;;AwExCd;EACE,OxEuCY;;;AyE/ChB;EACE,OzEoH6B;EyEnH7B;;AACA;EACE;EACA;;AAEF;EACE;EACA;;AACA;EACE;EACA;EACA;EACA;;AAGJ;EACE;EACA;;AAEF;EACE;EACA;EACA;EACA,kBzEoTgC;EyEnThC;EACA;;AACA;EAPF;IAQI;;;AAGJ;EACE;EACA,kBzE2SgC;EyE1ShC;;AACA;EACE;EACA;EACA,azEsH6B;EyErH7B;EACA;EACA;;AACA;EACE,azEiH2B;EyEhH3B;EACA;EACA,OzEsEuB;EyErEvB;;AAEE;EAIE;;AAKR;EACE;EACA;EACA;EACA;EACA;EACA;;AACA;EAEE;EACA;EACA;;;AAQJ;EACE;;AAQF;EACE;EACA;;AAGJ;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACE;;AAIF;EACE;;AAGJ;EACE;EACA;EACA,YzE4NgC;;AyE1NlC;EACE;EACA;EACA;EACA;;AAEF;EACE;EACA;EACA;;AAGA;EACE;;AAIA;EACE;;;AAMR;EACE;;AACA;EACE;EACA,YzE3B2B;;;AyE+B/B;EACE;EACA;EACA;;AACA;EACE,OzEvHc;EyEwHd;;AAEF;EACE;;AAEF;EACE,OzE1C2B;;;AyE8C/B;EACE;EACA;EACA;EACA;;AACA;EACE;EACA;;AAGA;EACE;EACA;;;AC/KF;EACE;EACA;;AAGJ;EAPF;IAQI,YCPY;;;ADSf;EACG;EnERF;EACA;EACA;EACA;EACA;EACA;EACA;EmEKE;EACA;;AAEA;EACE;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EARF;IASI;;;AAEF;EACE;;AAEE;EACE;EACA;EACA;EACA,O1ExBG;E0EyBH,W1E6HqB;E0E5HrB;EACA;;;AErCV;EACE;EACA,QDFY;ECGZ,Y5E2UgC;E4E1UhC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEA;EAbF;IAcI;IACA;;EACA;IACE;;;AAIJ;EACE;EACA;EACA;;AACA;EAJF;IAKI;;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;EACA,O5EmFuB;E4ElFvB;EACA;EACA;;AACA;EACE,O5ENQ;E4EOR;;AAIJ;EAIE;EACA;EACA;;AAEE;EACE;EACA;EACA,O5E9CG;E4E+CH,Y5EmM4B;;A4ElM5B;EACE;EACA;EACA;;AAGJ;AAAA;EACE;EACA;;AACA;AAAA;EACE,O5E1DC;;A4E4DH;AAAA;EACE,O5E7DC;;A4E+DH;AAAA;EACE,O5EhEC;;A4EkEH;AAAA;EACE,O5EnEC;;A4EwET;EACE;EACA;EACA;;AACA;EACE;EACA;EACA;EACA;EACA;EACA;;AACA;EAPF;IAQI;;;AAEF;EACE,O5EvFG;E4EwFH;EACA;EACA;EACA;;AACA;EAEE,O5EtEI;;A4EwEN;EACE;;AAEF;EACE;EACA;;AAEF;EACE;EACA;EACA;;AACA;EACI,Y5EpFA;E4EqFA;EACA;EACA;;AACA;EACI,kB5EzFJ;E4E0FI;EACA;EACA;EACA;;AAOV;EADF;IAEI;;;AAEF;EACE;EACA;EACA;EACA;EAEA;;AACA;EACE;EACA;EACA;EACA,Y5EvID;E4EwIC;EACA;EACA;EACA;EACQ;EACR;EACA;;AAEF;EApBF;IAqBI;IACA;;EACA;IACE;;;AAQV;EACE;;AAEF;EACE;EACA;EACA,O5ErKK;;A4E0Kb;EACE,Y5EyJgC;E4ExJhC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;ApEnHA;EoEyGF;IAYI;;EACA;IACE;;;ApEvHJ;EoE2HE;IACE;IACA;;;AAIJ;EACE;EACA;EACA;EACA;EACA;EACA;;ApEvIF;EoEiIA;IAQI;;;AAGF;EACE;EACA;;ApE9IJ;EoE4IE;IAII;IACA;IACA;;;ApE/JN;EoEoKQ;IACE;;;AAKN;EACE;;ApE3KN;EoE6KQ;IACE;;;AAKN;EACE;EACA;EACA;EACA,O5EvOK;E4EwOL;EACA;;AAIF;EACE,O5ElPK;E4EmPL;EACA;EACA;;AAGA;EACE;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;;AAGF;EACE;EACA;EC7QR,gBD8Q2B;EC7QzB,cD6QyB;EC5Q1B,eD4Q0B;EC3Q9B,mBD2Q8B;EC1QxB,WD0QwB;ECpP9B,sBDqPqC;ECpPhC,qBDoPgC;ECnPjC,sBDmPiC;EClPrC,0BDkPqC;ECjP/B,kBDiP+B;EAC1B,qB5E9HiB;;A4EgIjB;EACE;EACA;EACA;EACA;EACA;EACA;;AAMJ;EACE;;AAGF;EACE;;AACA;AAAA;AAAA;EAGE,O5ExQI;E4EyQJ;EACA;;AAEF;EC3SR,gBD4S6B;EC3S3B,cD2S2B;EC1S5B,eD0S4B;ECzShC,mBDySgC;ECxS1B,WDwS0B;EClRhC,sBDmRuC;EClRlC,qBDkRkC;ECjRnC,sBDiRmC;EChRvC,0BDgRuC;EC/QjC,kBD+QiC;EAC1B,qB5E5Je;;AQ1EzB;EoE4OM;IACE;;EAGF;IACE;;EACA;IC1TR,gBD2T6B;IC1T3B,cD0T2B;ICzT5B,eDyT4B;ICxThC,mBDwTgC;ICvT1B,WDuT0B;ICjShC,sBDkSuC;ICjSlC,qBDiSkC;IChSnC,sBDgSmC;IC/RvC,0BD+RuC;IC9RjC,kBD8RiC;IAC1B,qB5E3Ke;;;A4EiLnB;EACE;;AACA;EACE;EACA;EACA;EACA;EACA;EACA;;AACA;EAPF;IAQI;IACA;IACA;IACA;IACA;;;AAGJ;AAAA;AAAA;EAGE,O5ExTI;;A4E6TV;EACE;EAEA;;AACA;EACE;EACA;;AAEA;EACE;EACA;;AAEA;EACE;EACA;EACA,O5EpWD;E4EqWC;EACA;EACA;EACA;;AAEA;EACE,O5EnVA;EkEgChB,oBUoTqC;EVnTrC,iBUmTqC;EVlTrC,gBUkTqC;EVjTrC,eUiTqC;EVhTrC,YUgTqC;;AACrB;EACE,Y5EtVF;;A4EyVF;EACE,O5E1VA;;A4E+VF;EACE,O5EhWA;;A4EiWA;EACE,Y5ElWF;;A4E0WV;EACE;;AACA;EACE;EACA;EACA;EACA;EACA;EACA;;ApEtVR;EoEgVM;IAQI;IACA;IACA;IACA,Y5E3EsB;I4E4EtB;IACA,e5EzQM;I4E0QN;IACA;;EAEE;IACE;IACA;;EACA;IACE;IACA;IACA;IACA;IACA;IACA,Y5E9ZL;I4E+ZK;IACA;;;ApE/VhB;EoEmUM;IAkCI;IACA;IACA;IACQ;IACR;;;AAEF;EACE;EACA;;ApE7WV;EoE2WQ;IAII;;;AAON;EAiCE;EACA;EACA;EACA;;ApEvaR;EoEmYM;IAEI;IACA;IACA;IACA,Y5ExHsB;I4EyHtB;IACA;IACA;IACA;;EAEE;IACE;IACA;;EACA;IACE;IACA;IACA;IACA;IACA;IACA,Y5E3cL;I4E4cK;IACA;;;ApE5YhB;EoEsXM;IA4BI;IACA;IACA;IACQ;;;AAOV;EACE;;ApE7ZV;EoEsXM;IA0CI;;EAEA;IACE;IACA;IACA;;EAEA;IACE;IACA;IACA;;EAGE;IACE;;;AAYlB;EACE,aDtgBU;;ACwgBZ;EAEE;;AAGJ;EA9gBF;IA+gBI;IACA;IACA;IACA;IACA;;;;AEhhBF;EACE,O9ECW;;;A+EDb;EACE;EACA;EACA;;AACA;EACE;;AAEF;EACE;EACA;EACA;;AAEF;EACE;EACA;;;AAKJ;ExCrBA;EACA;EACA;;AAMA;EACE;;AwCcA;EACE,O/EfS;;;A+EPb;EACE;EACA;EACA;;AACA;EACE;;AAEF;EACE;EACA;EACA;;AAEF;EACE;EACA;;;AAKJ;ExCrBA;EACA;EACA;;AAMA;EACE;;AwCcA;EACE,O/EfS;;;A+EPb;EACE;EACA;EACA;;AACA;EACE;;AAEF;EACE;EACA;EACA;;AAEF;EACE;EACA;;;AAKJ;ExCrBA;EACA;EACA;;AAMA;EACE;;AwCcA;EACE,O/EfS;;;A+EPb;EACE;EACA;EACA;;AACA;EACE;;AAEF;EACE;EACA;EACA;;AAEF;EACE;EACA;;;AAKJ;ExCrBA;EACA;EACA;;AAMA;EACE;;AwCcA;EACE,O/EfS;;;A+EPb;EACE;EACA;EACA;;AACA;EACE;;AAEF;EACE;EACA;EACA;;AAEF;EACE;EACA;;;AAKJ;ExCrBA;EACA;EACA;;AAMA;EACE;;AwCcA;EACE,O/EfS;;;A+EPb;EACE;EACA;EACA;;AACA;EACE;;AAEF;EACE;EACA;EACA;;AAEF;EACE;EACA;;;AAKJ;ExCrBA;EACA;EACA;;AAMA;EACE;;AwCcA;EACE,O/EfS;;;A+EPb;EACE;EACA;EACA;;AACA;EACE;;AAEF;EACE;EACA;EACA;;AAEF;EACE;EACA;;;AAKJ;ExCrBA;EACA;EACA;;AAMA;EACE;;AwCcA;EACE,O/EfS;;;A+EPb;EACE;EACA;EACA;;AACA;EACE;;AAEF;EACE;EACA;EACA;;AAEF;EACE;EACA;;;AAKJ;ExCrBA;EACA;EACA;;AAMA;EACE;;AwCcA;EACE,O/EfS;;;AgFPP;EACE;;AAQF;EACE;;AAQF;EACE;;;ACvBV;AAGE;AAAA;AAAA;EACE;;AAEF;EhFaE;;AgFVF;EACE;EACA,WjFiNoC;;AiF7MtC;EACE;EACA;EACA;EACA;EACA;EACA;;AACA;EACE;;AAEF;EACE;EACA;;AACA;EACE;;AAGJ;EACE;EACA;;AACA;EACE;;AAGJ;EACE;EACA;;AACA;EACE;;AAOJ;EACE;;AAEF;EACE;;AAEF;AAAA;AAAA;AAAA;AAAA;AAAA;EAEE;EACA;;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;EAEE;EACA;;AAIF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAEE;EACA;;AAIF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAEE;EACA;;;AAQN;ECtFA,YDuFyB;ECtFzB,OlFGa;;AkFDb;EAEE;EACA,OlFFW;;AkFIb;EACE;EACA;;AACA;EACE,YD2EqB;EC1ErB;EACA;EACA;;;AD0EJ;ECpEF;EACA,ODoEmC;;ACnEjC;EACE,YDkE+B;ECjE/B,OlFrBW;;;AiFkFb;ECtFA,YDuFyB;ECtFzB,OlFGa;;AkFDb;EAEE;EACA,OlFFW;;AkFIb;EACE;EACA;;AACA;EACE,YD2EqB;EC1ErB;EACA;EACA;;;AD0EJ;ECpEF;EACA,ODoEmC;;ACnEjC;EACE,YDkE+B;ECjE/B,OlFrBW;;;AiFkFb;ECtFA,YDuFyB;ECtFzB,OlFGa;;AkFDb;EAEE;EACA,OlFFW;;AkFIb;EACE;EACA;;AACA;EACE,YD2EqB;EC1ErB;EACA;EACA;;;AD0EJ;ECpEF;EACA,ODoEmC;;ACnEjC;EACE,YDkE+B;ECjE/B,OlFrBW;;;AiFkFb;ECtFA,YDuFyB;ECtFzB,OlFGa;;AkFDb;EAEE;EACA,OlFFW;;AkFIb;EACE;EACA;;AACA;EACE,YD2EqB;EC1ErB;EACA;EACA;;;AD0EJ;ECpEF;EACA,ODoEmC;;ACnEjC;EACE,YDkE+B;ECjE/B,OlFrBW;;;AiFkFb;ECtFA,YDuFyB;ECtFzB,OlFGa;;AkFDb;EAEE;EACA,OlFFW;;AkFIb;EACE;EACA;;AACA;EACE,YD2EqB;EC1ErB;EACA;EACA;;;AD0EJ;ECpEF;EACA,ODoEmC;;ACnEjC;EACE,YDkE+B;ECjE/B,OlFrBW;;;AiFkFb;ECtFA,YDuFyB;ECtFzB,OlFGa;;AkFDb;EAEE;EACA,OlFFW;;AkFIb;EACE;EACA;;AACA;EACE,YD2EqB;EC1ErB;EACA;EACA;;;AD0EJ;ECpEF;EACA,ODoEmC;;ACnEjC;EACE,YDkE+B;ECjE/B,OlFrBW;;;AiFkFb;ECtFA,YDuFyB;ECtFzB,OlFGa;;AkFDb;EAEE;EACA,OlFFW;;AkFIb;EACE;EACA;;AACA;EACE,YD2EqB;EC1ErB;EACA;EACA;;;AD0EJ;ECpEF;EACA,ODoEmC;;ACnEjC;EACE,YDkE+B;ECjE/B,OlFrBW;;;AiFkFb;ECtFA,YDuFyB;ECtFzB,OlFGa;;AkFDb;EAEE;EACA,OlFFW;;AkFIb;EACE;EACA;;AACA;EACE,YD2EqB;EC1ErB;EACA;EACA;;;AD0EJ;ECpEF;EACA,ODoEmC;;ACnEjC;EACE,YDkE+B;ECjE/B,OlFrBW;;;AiFkFb;ECtFA,YDuFyB;ECtFzB,OlFGa;;AkFDb;EAEE;EACA,OlFFW;;AkFIb;EACE;EACA;;AACA;EACE,YD2EqB;EC1ErB;EACA;EACA;;;AD0EJ;ECpEF;EACA,ODoEmC;;ACnEjC;EACE,YDkE+B;ECjE/B,OlFrBW;;;AiFkFb;ECtFA,YDuFyB;ECtFzB,OlFGa;;AkFDb;EAEE;EACA,OlFFW;;AkFIb;EACE;EACA;;AACA;EACE,YD2EqB;EC1ErB;EACA;EACA;;;AD0EJ;ECpEF;EACA,ODoEmC;;ACnEjC;EACE,YDkE+B;ECjE/B,OlFrBW;;;AiFkFb;ECtFA,YDuFyB;ECtFzB,OlFGa;;AkFDb;EAEE;EACA,OlFFW;;AkFIb;EACE;EACA;;AACA;EACE,YD2EqB;EC1ErB;EACA;EACA;;;AD0EJ;ECpEF;EACA,ODoEmC;;ACnEjC;EACE,YDkE+B;ECjE/B,OlFrBW;;;AiFkFb;ECtFA,YDuFyB;ECtFzB,OlFGa;;AkFDb;EAEE;EACA,OlFFW;;AkFIb;EACE;EACA;;AACA;EACE,YD2EqB;EC1ErB;EACA;EACA;;;AD0EJ;ECpEF;EACA,ODoEmC;;ACnEjC;EACE,YDkE+B;ECjE/B,OlFrBW;;;AiF4Fb;EClEA;EACA;EACA;;AACA;EACE,OnFoRW;;AmFlRb;EAIE;EACA;;AAGF;EAGE;EACA;;AAGF;EAEE,OnFiQW;EmFhQX;;;AD0CF;EClEA;EACA;EACA;;AACA;EACE,OnFoRW;;AmFlRb;EAIE;EACA;;AAGF;EAGE;EACA;;AAGF;EAEE,OnFiQW;EmFhQX;;;AD0CF;EClEA;EACA;EACA;;AACA;EACE,OnFoRW;;AmFlRb;EAIE;EACA;;AAGF;EAGE;EACA;;AAGF;EAEE,OnFiQW;EmFhQX;;;AD0CF;EClEA;EACA;EACA;;AACA;EACE,OnFoRW;;AmFlRb;EAIE;EACA;;AAGF;EAGE;EACA;;AAGF;EAEE,OnFiQW;EmFhQX;;;AD0CF;EClEA;EACA;EACA;;AACA;EACE,OnFoRW;;AmFlRb;EAIE;EACA;;AAGF;EAGE;EACA;;AAGF;EAEE,OnFiQW;EmFhQX;;;AD0CF;EClEA;EACA;EACA;;AACA;EACE,OnFoRW;;AmFlRb;EAIE;EACA;;AAGF;EAGE;EACA;;AAGF;EAEE,OnFiQW;EmFhQX;;;AD0CF;EClEA;EACA;EACA;;AACA;EACE,OnFoRW;;AmFlRb;EAIE;EACA;;AAGF;EAGE;EACA;;AAGF;EAEE,OnFiQW;EmFhQX;;;AD0CF;EClEA;EACA;EACA;;AACA;EACE,OnFoRW;;AmFlRb;EAIE;EACA;;AAGF;EAGE;EACA;;AAGF;EAEE,OnFiQW;EmFhQX;;;ACrDJ;EACE,YnFoUkC;EmFnUlC,oBnFmUkC;EmFlUlC,iBnFkUkC;EmFjUlC,gBnFiUkC;;AmF/ThC;EACE;;AAGJ;EACE;EACA;EACA;;;AAIJ;EACE,YnFmTkC;;AmFlTlC;EACE;;;ACjBA;AAAA;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAKA;AAAA;EACE;;AAMF;AAAA;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAMF;AAAA;EACE;;;AAMR;EACE;EACA;EACA,YpFmPiC;;;AoFhPnC;EACE;EACA;EACA;;AACA;EACE,OpFjDW;;AoFmDb;EACE;;AACA;EACE,OpF9BY;;AoFmCd;EACE,OpFlES;;;AqFHf;AAAA;EAEE;EACA,WrFuNsC;;;AqFnNtC;EACE,arFgNoC;EqF/MpC,gBrF+MoC;;;AqF3MxC;EACE;;;AAIA;EACE;EACA;;;AAKJ;AAAA;AAAA;AAAA;EAIE;;;AC7BF;EACE;EACA;;AACA;EACE;EACA;EACA,YtF8G2B;EsF7G3B;EACA;EACA;EACA;EACA;;AAEA;EACE;EACA;EACA;EACA;EACA,OtFTS;EsFUT;;AAGF;EACE;EACA;EACA,OtFhBS;EsFiBT;;AAGF;EACE;;AACA;AAAA;EAEE;EACA,OtFDU;;;AuFhCZ;EACE,cvFwR8B;EuFvR9B,OvFkHuB;EuFjHvB,kBvFqR8B;EuFpR9B;;AACA;EACE,cvFqR4B;EuFpR5B,OvFyBQ;EuFxBR,YvFkP8B;;AuFhPhC;EACE;EACA,OvFJK;EuFKL;;AAIN;EACE;;AACA;EACE;EACA;EACA;EACA,cvFkQ8B;EuFjQ9B,OvF4FuB;EuF3FvB,kBvF+P8B;;AuF9P9B;EACE;;AAEF;EACE;;AAEF;EACE,kBvFyN8B;EuFxN9B,OvFFQ;EuFGR;;AAKJ;EACE;EACA;;AACA;EACE;;;AAOR;EACE;;;ACpDE;EACE;EACA;;AAKJ;EACE;EACA;;AACA;EACE;;AAEF;EACE;;AAKJ;EACE;EACA;;AACA;EvFNF;;;AwFjBJ;EACE;;AAEA;EACA;;AAIE;EACE;EACA;EACA;EACA;EACA,OzFJS;;AyFKT;EACE;;AAKN;AAAA;EAEE;;AAIA;EACE;EACA;EACA;;;AC/BN;EACE;EACA,4B1FiJkB;E0FhJlB,yB1FgJkB;E0F/IlB;EACA;EACA;EACA;EACA;EACA;;AACA;EAVF;IAWI;IACA;;;AAGF;EACE;EACA;EACA;EACA;;AAEA;EANF;IAOI;;;AAGF;EACE;EACA;EACA;;AAGF;EACE;EACA;EACA;;AAGF;EACE;EACA;EACA;;AAGF;EACE;EACA;EACA;EACA;EACA;EACA;;AACA;EAPF;IAQI;IACA;;;AAIJ;EACE;EACQ;EACR;EACA,Y1FmR8B;E0FlR9B;EACA;EACA;EACA;EACA;;AACA;EAVF;IAWI;;;;ACnER;EACE;;AACA;EAFF;IAGI;;;AAEF;EALF;IAMI;;;AAGA;EADF;IAEI;;;AAIE;EACE;EACA;;AAIM;EACE;EACA;;AAUd;EADF;IAEI;IACA,Y3F6S8B;I2F5S9B;IACA;IACA;IACA;IACA;;EACA;IACE;;;AAGJ;EACE;;AAEF;EACE;EACA;EAOA;EACA;;AAPA;EAHF;IAII;;;AAEF;EANF;IAOI;;;AAIF;EACE;EACA;;AACA;EACE;EACA;EACA;;AACA;EAJF;IAKI;;;AAGA;EACE;EACA;EACA;EACA;EACA;EACA;;AAEF;EACE;EACA,O3FpEC;;A2FwEH;EACE;EACA;;AAEF;EACE;;AACA;EACE;EACA;;AACA;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAKR;EACE;;AACA;EACE;EACA;;AAEF;EACE;EACA;;AACA;EACE;EACA;EACA;;AACA;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAGJ;EACE;EACA;;AAQd;EACE;;AACA;EACE;EACA;EACA,Y3FzIS;E2F0IT;EACA;EACA;EACA;EACA;;AACA;EACE,Y3FtHU;;A2FwHZ;EACE,Y3FnJO;;;A4FVb;EACE;EACA;EACA;EACA;;;ACJF;EACE;;AACA;EAFF;IAGI;;;AAGA;EADF;IAEI;;;AAIN;EACE;;AACA;EACE,O7FwGyB;;A6FvGzB;EACE,O7FNO;;A6FSX;EACE;;AAEE;EACE,O7FWQ;;;A6FJlB;EACE;EACA;EACA;EACA;EACA;;AACA;EACE;;AAEF;EACE;;AAEF;EACE;EACA;EACA;;AACA;EACE;;AAEF;EACE;EACA;EACA;;AAEE;EACE;EACA,O7F9CK;;A6FiDT;EACE;;AAIE;EACE,O7F3BM;;A6FiChB;EACE;EACA;EACA;EACA;EACA;;AACA;EACE;;AACA;EACE;EACA;EACA,O7FoCuB;;A6FlCzB;EACE;EACA,O7F5EO;E6F6EP;EACA;EACA;EACA;;AAGJ;EACE,O7FwByB;E6FvBzB;;AAEE;EACE;EACA;EACA;;AAKR;EACE;;AAEE;EACE;;AAEF;EACE;;;AC9GR;EACE,e9F+IkB;E8F9IlB;EACA;EACA;EACA;;AACA;EACE,W9FuJ6B;;;A+F7J/B;EACE;EACA,O/FiH2B;E+FhH3B,c/FyI0B;;A+FtI5B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQE,M/FPW;;A+FUb;AAAA;EAEE;EACA;;AAGF;EACE;;AAGF;EACE;EACA,O/FsF2B;E+FrF3B,Y/FqQ+B;E+FpQ/B,e/FgHgB;E+F/GhB;;AACA;EACE,c/F0GwB;E+FzGxB,kB/FzBS;;A+F2BX;EACE;;AAGJ;AAAA;EAEE;;AAEF;AAAA;AAAA;EAGE;;AAGF;EACE;;AAGF;AAAA;EAEE,Y/F/CW;E+FgDX,O/FyD2B;E+FxD3B,c/FiF0B;;A+F9E5B;EACE,qB/F6E0B;;A+F3E5B;EACE;;AAGF;EACE,mB/FsE0B;;A+FpE5B;EACE;;AAGF;AACE;AAAA;AAAA;AAAA;;;AC3EE;EADF;IAEI;;;AAEF;EACE;EACA;;AAIF;EADF;IAEI;;;;AAMR;AACE;AAAA;AAAA;AAAA;;;AAMF;AACE;AAAA;AAAA;AAAA;;;AC3BF;EACE,YjGyPsC;EiGxPtC;EACA,ejG6IkB;;AiG5IlB;EACE;;AAIE;EACE;EACA,OjGHO;;AiGIP;EACE;EACA;EACA;;AAKR;EACE,kBjGqOoC;;;AkG1PxC;EACE,YlGyPsC;EkGxPtC;EACA;EACA,elG4IkB;EkGxIlB;EACA;;AAJA;EALF;IAMI;;;AAKA;EACE;;AAOF;EACE,YlGsOkC;;AkGlOnC;EACG,elGuHY;;;AmGhJpB;EACE,kBnGUa;EmGTb;EACA,YnGiSiC;;;AmG9RnC;AAAA;EAEE,qBnGqI4B;;;AmGlI9B;AAAA;EAEE,kBnGgI4B;;;AmG7H9B;AAAA;EACC,kBnGyOuC;;;AmGtOxC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAkBC,YnGNiB;EmGOjB,cnGPiB;;;AmGUlB;EACC;EACA;;;AAOD;AAAA;EAGE;EACA;EACA;;;AAGF;AAAA;EAGE;EACA;EACA;;;AAGF;AAAA;EAEE,MnG7Da;;;AmGgEf;AAAA;EAEE,MnG1CgB;;;AmG6ClB;EACE;EACA,OnGvEa;;;AmG0Ef;EACE,qBnG3Ea;;;AmG8Ef;EACE,kBnG/Ea;;;AmGkFf;AAAA;EAEE;;;AAGF;EACE;EACA,enG+CkB;EmG9ClB;;;AAGF;EACE;;;AAGF;EACE,OnGlGa;;;AmGqGf;EACE;;;AAGF;EACE,OnGE6B;;;AmGC/B;EACE,cnG9Ga;;;AmGiHf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAOE;;;AAGF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAYE;EACA;;;AAGF;EACE,kBnGP4B;;;AmGU9B;AAAA;AAAA;AAAA;EAIE;;;AAGF;AAAA;AAAA;EAGE,OnG7C6B;;;AmGgD/B;EACE,qBnG7Ja;;;AmGgKf;EACE,kBnGjKa;;;AoGRf;EACE;EACA;;AAUA;EACE;;AAEF;AAAA;AAAA;EAGE;;AAGF;EACE,cpGUc;;AoGPhB;AAAA;EAEE,OpGyF2B;;AoGtF7B;EACE;;AAGF;EACE;;AAGF;EACE;;AAGF;AAAA;EAEE,OpGwE2B;;;AoGpE/B;AAAA;AAAA;EAGE,cpG0F4B;;;AoGvF9B;AAAA;AAAA;EAGE;;;AAGF;EACE,kBpGlDa;EoGmDb,cpG+E4B;EoG9E5B,YpGqOiC;;AoGpOjC;EACE,kBpGzDW;;;AoG6Df;EACE,kBpGsQkC;;;AoGnQpC;EACE,cpGmE4B;;;AoGhE9B;EACE;EACA;EACA;EACA;EACA;EACA;EACA,OpGgC6B;EoG/B7B;;;AAGF;EACE,OpG2B6B;;;AqGnH7B;EACE;EACA;EACA;EACA;;AAEF;EAPF;IAQI;IACA;;EACA;IACE;;;;AAQF;AAAA;AAAA;AAAA;EAEE,OrG+FyB;;;AsGpH/B;EACE,etG+IkB;EsG9IlB,OtGkH6B;EsGjH7B;EACA;EACA,atGuJiC;EsGtJjC,YtG8RiC;;;AuGnSjC;EACE;;;ACFJ;EACC;EACA;;;AAGD;AAAA;AAAA;EAGC;EACA;;;AAGD;EACC;;;AAGD;EACC;EACA;AACA;AAAA;AAAA;AAAA;;;AAMD;AAAA;AAAA;EAGC;EACA;;;AAGD;EACC;AACA;AAAA;AAAA;AAAA;;;AAMD;AAAA;AAAA;AAAA;AAAA;AAAA;EAMC,kBxGnCc;;;AyGXf;EACE,WzG6J+B;EyG5J/B;EACA,YzGiUkC;;AyG/TlC;EACE,YzG8TgC;EyG7ThC;;AAGF;EACE;EACA;EACA;EACA,OzGsG2B;EyGrG3B;;AAGF;EACE,WzG2I6B;EyG1I7B,OzGZW;EyGaX;EACA;EACA;;AAaE;EACE;EACA;;AAKN;EACE;;AACA;EACE;;AAIJ;EACE,YzG3CW;;;A0GPb;AAAA;EAEE,Y1GuPoC;E0GtPpC;EACA,e1G2IgB;;A0G1IP;EACP;;;AAKN;EACE,O1GuG6B;;;A0GpG/B;EACE,Y1GNa;E0GOb;EACA,e1G6HkB;;;A0GtHlB;AAAA;EAEE,kB1GIc;;A0GFhB;EACE,O1GqF2B;;;A0GjF/B;AAAA;EAEE;;;AAIA;EACE;EACA;;AAEF;EACE;;AACA;EACE;;;AAKN;EACE;;AACA;EACE;;AAEF;EACE,kB1G3Bc;E0G4Bd,O1G1DW;E0G2DX,c1G7Bc;E0G8Bd;EACA;EACA;;AAEF;EACE,O1GjEW;E0GkEX;;;AAIJ;EACE;;;ACzEF;EACE,Y3GyPsC;E2GxPtC;EACA,O3GiH6B;E2GhH7B;EACA;;;AAGF;EACE;EACA;EACA;;AACA;EACE;;AAEF;EACE;;AACA;EACE;EACA;;AAGJ;EACE;EACA;;;AAIJ;AAAA;AAAA;EAGE;;;AAGF;AAAA;AAAA;EAGE,Y3GqNsC;;;A2GlNxC;EACE,c3G2E6B;;;A2GxE/B;EACE,Y3G6MsC;;;A2G3MxC;AAAA;EAEE;EACA;;;AAGF;AAAA;EAEE;;;AAGF;EACE,c3GkF4B;;;A2G/E9B;EACE;EACA;EACA;EACA,2B3G8EkB;E2G7ElB,4B3G6EkB;;;A4GhJpB;EACE;EACA,c5G2I4B;E4G1I5B,e5G6IkB;E4G5IlB,Y5GsPsC;E4GrPtC,O5GGa;;A4GFb;EACE,Y5GyBc;E4GxBd;EACA,O5GPW;E4GQX;EACA;EACA;EACA;EACA;AACA;AAAA;AAAA;AAAA;;AAIA;EACE;EACA;EACA,O5GpBS;E4GqBT;;AAGJ;EACE;AACA;AAAA;AAAA;;AAIF;EACE;EACA;EACA,e5G6GgB;;;A6GhJpB;EACE;EACA,e7G8IkB;;A6G7IlB;AAAA;AAAA;AAAA;AAAA;EAKE,kB7GkPoC;E6GjPpC;EACA;;AAEF;EACE;;AAEF;EACE;EACA;;AAEF;EACE,kB7GsOoC;;A6GpOtC;EACE,oB7GsH0B;E6GrH1B,mB7GqH0B;;A6GnH5B;EACE,kB7G+NoC;E6G9NpC,c7GiH0B;E6GhH1B,O7GrBW;;A6GuBb;AAAA;AAAA;EAGE,O7G1BW;;A6G4Bb;EACE,O7G+E2B;;A6G7E7B;EACE;;AAEF;EACE;EACA,O7GwE2B;;A6GvE3B;EACE,M7GsEyB;;A6GnE7B;EACE,O7GkE2B;;A6GhE7B;EACE;EACA,O7G8D2B;;A6G5D7B;AAAA;EAEE;EACA,O7GyD2B;;A6GvD7B;EACE;;AAEF;AAAA;AAAA;AAAA;EAIE;;AAEF;EACE;;AAEF;EACE;;AAEF;AAAA;AAAA;EAGE;;;AAKF;EACE,kB7G1EW;E6G2EX,c7GuD0B;;A6GrD5B;EACE,O7GjFW;;A6GmFb;EACE,kB7G5Dc;E6G6Dd,O7GuB2B;;A6GrB7B;EACE,c7G6C0B;;A6G3C5B;EACE;;AAEF;EACE;;AAEF;EACE,O7GW2B;;A6GT7B;EACE,O7GQ2B;;A6GJ7B;EACE;;AAEF;AAAA;AAAA;EAGE,kB7GoIoC;E6GnIpC,c7GsB0B;E6GrB1B,O7GJ2B;;A6Ge7B;EACE,kB7G5HW;E6G6HX,c7G7HW;E6G8HX,O7GlB2B;;A6GoB7B;EACE;EACA;;AAEF;EACE;;AAEF;AAAA;AAAA;AAAA;EAIE,kB7GuGoC;E6GtGpC,c7GP0B;E6GQ1B,O7GjC2B;;A6GmC7B;AAAA;AAAA;EAGE,kB7GgGoC;E6G/FpC,c7GkGoC;;A6GhGtC;EACE,c7GjB0B;;A6GmB5B;EACE,O7G7C2B;;;A8GpH/B;AAAA;EAGE;AACA;AAAA;AAAA;;;AAMA;EAEE;;;ACZJ;EACE;;AACA;EAFF;IAGI;;;;AAIJ;EACE;EACA;EACA;;;AAGF;AAAA;AAAA;EAGE;EACA,O/Gfa;E+GgBb;;;AAGF;AAAA;AAAA;EAGE,kB/GQgB;E+GPhB;;;AAGF;AAAA;AAAA;EAGE;EACA,e/GgHkB;E+G/GlB;;;AAGF;AAAA;AAAA;EAGE;EACA,O/GRgB;E+GShB;;;AAKE;EACE;;AAKA;EACE;;;AAMR;EACE;;;AAGF;EACE,Y/G0LsC;E+GzLtC;EACA;EACA;EACA;;;AAIA;EADF;IAEI;IACA;IACA;;;;AAIJ;AACE;AAAA;AAAA;;;AAKF;AAAA;AAEE;AAAA;AAAA;;;AAKF;AACE;AAAA;AAAA;;;AAYF;AAAA;AAAA;EAGE;EACA;EACA;;;AAIA;EACE;EACA;;;AASF;EACE;;;AAIJ;EACE;EACA;;;AAGF;AAAA;AAAA;EAGE;;;AAGF;EACE;EACA", "file": "../../demo4/style.css", "sourcesContent": ["/*\n* NobleUI - HTML Bootstrap 5 Admin Dashboard Template v2.0.2 (https://nobleui.com/)\n* Copyright © 2022 NobleUI\n* Licensed under ThemeForest License\n*/\n\n// Theme style for demo4 (Horizontal Layout - Dark, Dark-RTL)\n\n\n\n\n// Custom variables\n@import '../theme-dark/variables';\n@import './variables';\n\n\n// Bootstrap stylesheets\n@import \"bootstrap/scss/functions\";\n@import \"bootstrap/scss/variables\";\n@import \"bootstrap/scss/maps\";\n@import \"bootstrap/scss/mixins\";\n@import \"bootstrap/scss/utilities\";\n\n// Bootstrap layout & components\n@import \"bootstrap/scss/root\";\n@import \"bootstrap/scss/reboot\";\n@import \"bootstrap/scss/type\";\n@import \"bootstrap/scss/images\";\n@import \"bootstrap/scss/containers\";\n@import \"bootstrap/scss/grid\";\n@import \"bootstrap/scss/tables\";\n@import \"bootstrap/scss/forms\";\n@import \"bootstrap/scss/buttons\";\n@import \"bootstrap/scss/transitions\";\n@import \"bootstrap/scss/dropdown\";\n@import \"bootstrap/scss/button-group\";\n@import \"bootstrap/scss/nav\";\n@import \"bootstrap/scss/navbar\";\n@import \"bootstrap/scss/card\";\n@import \"bootstrap/scss/accordion\";\n@import \"bootstrap/scss/breadcrumb\";\n@import \"bootstrap/scss/pagination\";\n@import \"bootstrap/scss/badge\";\n@import \"bootstrap/scss/alert\";\n@import \"bootstrap/scss/progress\";\n@import \"bootstrap/scss/list-group\";\n@import \"bootstrap/scss/close\";\n@import \"bootstrap/scss/toasts\";\n@import \"bootstrap/scss/modal\";\n@import \"bootstrap/scss/tooltip\";\n@import \"bootstrap/scss/popover\";\n@import \"bootstrap/scss/carousel\";\n@import \"bootstrap/scss/spinners\";\n\n// Bootstrap helpers\n@import \"bootstrap/scss/helpers\";\n\n// Bootstrap utilities\n@import '../common/utilities';\n@import \"bootstrap/scss/utilities/api\";\n\n\n// Custom mixins\n@import '../common/mixins/animation';\n@import '../common/mixins/buttons';\n@import '../common/mixins/misc';\n@import '../common/mixins/width';\n\n// Core styles\n@import '../common/background';\n@import '../common/reset';\n@import '../common/functions';\n@import '../common/misc';\n@import '../common/helpers';\n@import '../common/typography';\n@import '../common/demo';\n\n// Layout\n@import './horizontal-wrapper';\n@import './navbar';\n@import './layouts';\n\n// Custom components\n@import \"../common/components/badges\";\n@import \"../common/components/bootstrap-alert\";\n@import \"../common/components/breadcrumbs\";\n@import \"../common/components/buttons\";\n@import \"../common/components/cards\";\n// @import \"../common/components/checkbox-radio\";\n@import \"../common/components/dashboard\";\n@import \"../common/components/dropdown\";\n@import \"../common/components/forms\";\n@import \"../common/components/icons\";\n@import \"../common/components/nav\";\n@import \"../common/components/pagination\";\n@import \"../common/components/tables\";\n@import \"../common/components/timeline\";\n@import \"../common/components/chat\";\n@import \"../common/components/auth\";\n\n// Email app\n@import '../common/components/email/inbox';\n\n// 3rd-Party plugin overrides\n@import \"../theme-dark/components/plugin-overrides/ace\";\n@import \"../theme-dark/components/plugin-overrides/apex-charts\";\n@import \"../theme-dark/components/plugin-overrides/data-tables\";\n@import \"../theme-dark/components/plugin-overrides/dropify\";\n@import \"../theme-dark/components/plugin-overrides/dropzone\";\n@import \"../theme-dark/components/plugin-overrides/flatpickr\";\n@import \"../theme-dark/components/plugin-overrides/full-calendar\";\n@import \"../theme-dark/components/plugin-overrides/jquery-flot\";\n@import \"../theme-dark/components/plugin-overrides/morrisjs\";\n@import \"../theme-dark/components/plugin-overrides/peity\";\n@import \"../theme-dark/components/plugin-overrides/perfect-scrollbar\";\n@import \"../theme-dark/components/plugin-overrides/sweet-alert\";\n@import \"../theme-dark/components/plugin-overrides/select2\";\n@import \"../theme-dark/components/plugin-overrides/easymde\";\n@import \"../theme-dark/components/plugin-overrides/tags-input\";\n@import \"../theme-dark/components/plugin-overrides/tinymce\";\n@import \"../theme-dark/components/plugin-overrides/typeahead\";\n@import \"../theme-dark/components/plugin-overrides/wizard\";\n", ":root {\n  // Note: Custom variable values only support SassScript inside `#{}`.\n\n  // Colors\n  //\n  // Generate palettes for full colors, grays, and theme colors.\n\n  @each $color, $value in $colors {\n    --#{$prefix}#{$color}: #{$value};\n  }\n\n  @each $color, $value in $grays {\n    --#{$prefix}gray-#{$color}: #{$value};\n  }\n\n  @each $color, $value in $theme-colors {\n    --#{$prefix}#{$color}: #{$value};\n  }\n\n  @each $color, $value in $theme-colors-rgb {\n    --#{$prefix}#{$color}-rgb: #{$value};\n  }\n\n  --#{$prefix}white-rgb: #{to-rgb($white)};\n  --#{$prefix}black-rgb: #{to-rgb($black)};\n  --#{$prefix}body-color-rgb: #{to-rgb($body-color)};\n  --#{$prefix}body-bg-rgb: #{to-rgb($body-bg)};\n\n  // Fonts\n\n  // Note: Use `inspect` for lists so that quoted items keep the quotes.\n  // See https://github.com/sass/sass/issues/2383#issuecomment-336349172\n  --#{$prefix}font-sans-serif: #{inspect($font-family-sans-serif)};\n  --#{$prefix}font-monospace: #{inspect($font-family-monospace)};\n  --#{$prefix}gradient: #{$gradient};\n\n  // Root and body\n  // scss-docs-start root-body-variables\n  @if $font-size-root != null {\n    --#{$prefix}root-font-size: #{$font-size-root};\n  }\n  --#{$prefix}body-font-family: #{$font-family-base};\n  @include rfs($font-size-base, --#{$prefix}body-font-size);\n  --#{$prefix}body-font-weight: #{$font-weight-base};\n  --#{$prefix}body-line-height: #{$line-height-base};\n  --#{$prefix}body-color: #{$body-color};\n  @if $body-text-align != null {\n    --#{$prefix}body-text-align: #{$body-text-align};\n  }\n  --#{$prefix}body-bg: #{$body-bg};\n  // scss-docs-end root-body-variables\n\n  // scss-docs-start root-border-var\n  --#{$prefix}border-width: #{$border-width};\n  --#{$prefix}border-style: #{$border-style};\n  --#{$prefix}border-color: #{$border-color};\n  --#{$prefix}border-color-translucent: #{$border-color-translucent};\n\n  --#{$prefix}border-radius: #{$border-radius};\n  --#{$prefix}border-radius-sm: #{$border-radius-sm};\n  --#{$prefix}border-radius-lg: #{$border-radius-lg};\n  --#{$prefix}border-radius-xl: #{$border-radius-xl};\n  --#{$prefix}border-radius-2xl: #{$border-radius-2xl};\n  --#{$prefix}border-radius-pill: #{$border-radius-pill};\n  // scss-docs-end root-border-var\n\n  --#{$prefix}link-color: #{$link-color};\n  --#{$prefix}link-hover-color: #{$link-hover-color};\n\n  --#{$prefix}code-color: #{$code-color};\n\n  --#{$prefix}highlight-bg: #{$mark-bg};\n}\n", "// stylelint-disable property-blacklist, scss/dollar-variable-default\n\n// SCSS RFS mixin\n//\n// Automated responsive values for font sizes, paddings, margins and much more\n//\n// Licensed under MIT (https://github.com/twbs/rfs/blob/main/LICENSE)\n\n// Configuration\n\n// Base value\n$rfs-base-value: 1.25rem !default;\n$rfs-unit: rem !default;\n\n@if $rfs-unit != rem and $rfs-unit != px {\n  @error \"`#{$rfs-unit}` is not a valid unit for $rfs-unit. Use `px` or `rem`.\";\n}\n\n// Breakpoint at where values start decreasing if screen width is smaller\n$rfs-breakpoint: 1200px !default;\n$rfs-breakpoint-unit: px !default;\n\n@if $rfs-breakpoint-unit != px and $rfs-breakpoint-unit != em and $rfs-breakpoint-unit != rem {\n  @error \"`#{$rfs-breakpoint-unit}` is not a valid unit for $rfs-breakpoint-unit. Use `px`, `em` or `rem`.\";\n}\n\n// Resize values based on screen height and width\n$rfs-two-dimensional: false !default;\n\n// Factor of decrease\n$rfs-factor: 10 !default;\n\n@if type-of($rfs-factor) != number or $rfs-factor <= 1 {\n  @error \"`#{$rfs-factor}` is not a valid  $rfs-factor, it must be greater than 1.\";\n}\n\n// Mode. Possibilities: \"min-media-query\", \"max-media-query\"\n$rfs-mode: min-media-query !default;\n\n// Generate enable or disable classes. Possibilities: false, \"enable\" or \"disable\"\n$rfs-class: false !default;\n\n// 1 rem = $rfs-rem-value px\n$rfs-rem-value: 16 !default;\n\n// Safari iframe resize bug: https://github.com/twbs/rfs/issues/14\n$rfs-safari-iframe-resize-bug-fix: false !default;\n\n// Disable RFS by setting $enable-rfs to false\n$enable-rfs: true !default;\n\n// Cache $rfs-base-value unit\n$rfs-base-value-unit: unit($rfs-base-value);\n\n@function divide($dividend, $divisor, $precision: 10) {\n  $sign: if($dividend > 0 and $divisor > 0 or $dividend < 0 and $divisor < 0, 1, -1);\n  $dividend: abs($dividend);\n  $divisor: abs($divisor);\n  @if $dividend == 0 {\n    @return 0;\n  }\n  @if $divisor == 0 {\n    @error \"Cannot divide by 0\";\n  }\n  $remainder: $dividend;\n  $result: 0;\n  $factor: 10;\n  @while ($remainder > 0 and $precision >= 0) {\n    $quotient: 0;\n    @while ($remainder >= $divisor) {\n      $remainder: $remainder - $divisor;\n      $quotient: $quotient + 1;\n    }\n    $result: $result * 10 + $quotient;\n    $factor: $factor * .1;\n    $remainder: $remainder * 10;\n    $precision: $precision - 1;\n    @if ($precision < 0 and $remainder >= $divisor * 5) {\n      $result: $result + 1;\n    }\n  }\n  $result: $result * $factor * $sign;\n  $dividend-unit: unit($dividend);\n  $divisor-unit: unit($divisor);\n  $unit-map: (\n    \"px\": 1px,\n    \"rem\": 1rem,\n    \"em\": 1em,\n    \"%\": 1%\n  );\n  @if ($dividend-unit != $divisor-unit and map-has-key($unit-map, $dividend-unit)) {\n    $result: $result * map-get($unit-map, $dividend-unit);\n  }\n  @return $result;\n}\n\n// Remove px-unit from $rfs-base-value for calculations\n@if $rfs-base-value-unit == px {\n  $rfs-base-value: divide($rfs-base-value, $rfs-base-value * 0 + 1);\n}\n@else if $rfs-base-value-unit == rem {\n  $rfs-base-value: divide($rfs-base-value, divide($rfs-base-value * 0 + 1, $rfs-rem-value));\n}\n\n// Cache $rfs-breakpoint unit to prevent multiple calls\n$rfs-breakpoint-unit-cache: unit($rfs-breakpoint);\n\n// Remove unit from $rfs-breakpoint for calculations\n@if $rfs-breakpoint-unit-cache == px {\n  $rfs-breakpoint: divide($rfs-breakpoint, $rfs-breakpoint * 0 + 1);\n}\n@else if $rfs-breakpoint-unit-cache == rem or $rfs-breakpoint-unit-cache == \"em\" {\n  $rfs-breakpoint: divide($rfs-breakpoint, divide($rfs-breakpoint * 0 + 1, $rfs-rem-value));\n}\n\n// Calculate the media query value\n$rfs-mq-value: if($rfs-breakpoint-unit == px, #{$rfs-breakpoint}px, #{divide($rfs-breakpoint, $rfs-rem-value)}#{$rfs-breakpoint-unit});\n$rfs-mq-property-width: if($rfs-mode == max-media-query, max-width, min-width);\n$rfs-mq-property-height: if($rfs-mode == max-media-query, max-height, min-height);\n\n// Internal mixin used to determine which media query needs to be used\n@mixin _rfs-media-query {\n  @if $rfs-two-dimensional {\n    @if $rfs-mode == max-media-query {\n      @media (#{$rfs-mq-property-width}: #{$rfs-mq-value}), (#{$rfs-mq-property-height}: #{$rfs-mq-value}) {\n        @content;\n      }\n    }\n    @else {\n      @media (#{$rfs-mq-property-width}: #{$rfs-mq-value}) and (#{$rfs-mq-property-height}: #{$rfs-mq-value}) {\n        @content;\n      }\n    }\n  }\n  @else {\n    @media (#{$rfs-mq-property-width}: #{$rfs-mq-value}) {\n      @content;\n    }\n  }\n}\n\n// Internal mixin that adds disable classes to the selector if needed.\n@mixin _rfs-rule {\n  @if $rfs-class == disable and $rfs-mode == max-media-query {\n    // Adding an extra class increases specificity, which prevents the media query to override the property\n    &,\n    .disable-rfs &,\n    &.disable-rfs {\n      @content;\n    }\n  }\n  @else if $rfs-class == enable and $rfs-mode == min-media-query {\n    .enable-rfs &,\n    &.enable-rfs {\n      @content;\n    }\n  }\n  @else {\n    @content;\n  }\n}\n\n// Internal mixin that adds enable classes to the selector if needed.\n@mixin _rfs-media-query-rule {\n\n  @if $rfs-class == enable {\n    @if $rfs-mode == min-media-query {\n      @content;\n    }\n\n    @include _rfs-media-query {\n      .enable-rfs &,\n      &.enable-rfs {\n        @content;\n      }\n    }\n  }\n  @else {\n    @if $rfs-class == disable and $rfs-mode == min-media-query {\n      .disable-rfs &,\n      &.disable-rfs {\n        @content;\n      }\n    }\n    @include _rfs-media-query {\n      @content;\n    }\n  }\n}\n\n// Helper function to get the formatted non-responsive value\n@function rfs-value($values) {\n  // Convert to list\n  $values: if(type-of($values) != list, ($values,), $values);\n\n  $val: '';\n\n  // Loop over each value and calculate value\n  @each $value in $values {\n    @if $value == 0 {\n      $val: $val + ' 0';\n    }\n    @else {\n      // Cache $value unit\n      $unit: if(type-of($value) == \"number\", unit($value), false);\n\n      @if $unit == px {\n        // Convert to rem if needed\n        $val: $val + ' ' + if($rfs-unit == rem, #{divide($value, $value * 0 + $rfs-rem-value)}rem, $value);\n      }\n      @else if $unit == rem {\n        // Convert to px if needed\n        $val: $val + ' ' + if($rfs-unit == px, #{divide($value, $value * 0 + 1) * $rfs-rem-value}px, $value);\n      }\n      @else {\n        // If $value isn't a number (like inherit) or $value has a unit (not px or rem, like 1.5em) or $ is 0, just print the value\n        $val: $val + ' ' + $value;\n      }\n    }\n  }\n\n  // Remove first space\n  @return unquote(str-slice($val, 2));\n}\n\n// Helper function to get the responsive value calculated by RFS\n@function rfs-fluid-value($values) {\n  // Convert to list\n  $values: if(type-of($values) != list, ($values,), $values);\n\n  $val: '';\n\n  // Loop over each value and calculate value\n  @each $value in $values {\n    @if $value == 0 {\n      $val: $val + ' 0';\n    }\n\n    @else {\n      // Cache $value unit\n      $unit: if(type-of($value) == \"number\", unit($value), false);\n\n      // If $value isn't a number (like inherit) or $value has a unit (not px or rem, like 1.5em) or $ is 0, just print the value\n      @if not $unit or $unit != px and $unit != rem {\n        $val: $val + ' ' + $value;\n      }\n\n      @else {\n        // Remove unit from $value for calculations\n        $value: divide($value, $value * 0 + if($unit == px, 1, divide(1, $rfs-rem-value)));\n\n        // Only add the media query if the value is greater than the minimum value\n        @if abs($value) <= $rfs-base-value or not $enable-rfs {\n          $val: $val + ' ' +  if($rfs-unit == rem, #{divide($value, $rfs-rem-value)}rem, #{$value}px);\n        }\n        @else {\n          // Calculate the minimum value\n          $value-min: $rfs-base-value + divide(abs($value) - $rfs-base-value, $rfs-factor);\n\n          // Calculate difference between $value and the minimum value\n          $value-diff: abs($value) - $value-min;\n\n          // Base value formatting\n          $min-width: if($rfs-unit == rem, #{divide($value-min, $rfs-rem-value)}rem, #{$value-min}px);\n\n          // Use negative value if needed\n          $min-width: if($value < 0, -$min-width, $min-width);\n\n          // Use `vmin` if two-dimensional is enabled\n          $variable-unit: if($rfs-two-dimensional, vmin, vw);\n\n          // Calculate the variable width between 0 and $rfs-breakpoint\n          $variable-width: #{divide($value-diff * 100, $rfs-breakpoint)}#{$variable-unit};\n\n          // Return the calculated value\n          $val: $val + ' calc(' + $min-width + if($value < 0, ' - ', ' + ') + $variable-width + ')';\n        }\n      }\n    }\n  }\n\n  // Remove first space\n  @return unquote(str-slice($val, 2));\n}\n\n// RFS mixin\n@mixin rfs($values, $property: font-size) {\n  @if $values != null {\n    $val: rfs-value($values);\n    $fluidVal: rfs-fluid-value($values);\n\n    // Do not print the media query if responsive & non-responsive values are the same\n    @if $val == $fluidVal {\n      #{$property}: $val;\n    }\n    @else {\n      @include _rfs-rule {\n        #{$property}: if($rfs-mode == max-media-query, $val, $fluidVal);\n\n        // Include safari iframe resize fix if needed\n        min-width: if($rfs-safari-iframe-resize-bug-fix, (0 * 1vw), null);\n      }\n\n      @include _rfs-media-query-rule {\n        #{$property}: if($rfs-mode == max-media-query, $fluidVal, $val);\n      }\n    }\n  }\n}\n\n// Shorthand helper mixins\n@mixin font-size($value) {\n  @include rfs($value);\n}\n\n@mixin padding($value) {\n  @include rfs($value, padding);\n}\n\n@mixin padding-top($value) {\n  @include rfs($value, padding-top);\n}\n\n@mixin padding-right($value) {\n  @include rfs($value, padding-right);\n}\n\n@mixin padding-bottom($value) {\n  @include rfs($value, padding-bottom);\n}\n\n@mixin padding-left($value) {\n  @include rfs($value, padding-left);\n}\n\n@mixin margin($value) {\n  @include rfs($value, margin);\n}\n\n@mixin margin-top($value) {\n  @include rfs($value, margin-top);\n}\n\n@mixin margin-right($value) {\n  @include rfs($value, margin-right);\n}\n\n@mixin margin-bottom($value) {\n  @include rfs($value, margin-bottom);\n}\n\n@mixin margin-left($value) {\n  @include rfs($value, margin-left);\n}\n", "// stylelint-disable declaration-no-important, selector-no-qualifying-type, property-no-vendor-prefix\n\n\n// Reboot\n//\n// Normalization of HTML elements, manually forked from Normalize.css to remove\n// styles targeting irrelevant browsers while applying new styles.\n//\n// Normalize is licensed MIT. https://github.com/necolas/normalize.css\n\n\n// Document\n//\n// Change from `box-sizing: content-box` so that `width` is not affected by `padding` or `border`.\n\n*,\n*::before,\n*::after {\n  box-sizing: border-box;\n}\n\n\n// Root\n//\n// Ability to the value of the root font sizes, affecting the value of `rem`.\n// null by default, thus nothing is generated.\n\n:root {\n  @if $font-size-root != null {\n    @include font-size(var(--#{$prefix}root-font-size));\n  }\n\n  @if $enable-smooth-scroll {\n    @media (prefers-reduced-motion: no-preference) {\n      scroll-behavior: smooth;\n    }\n  }\n}\n\n\n// Body\n//\n// 1. Remove the margin in all browsers.\n// 2. As a best practice, apply a default `background-color`.\n// 3. Prevent adjustments of font size after orientation changes in iOS.\n// 4. Change the default tap highlight to be completely transparent in iOS.\n\n// scss-docs-start reboot-body-rules\nbody {\n  margin: 0; // 1\n  font-family: var(--#{$prefix}body-font-family);\n  @include font-size(var(--#{$prefix}body-font-size));\n  font-weight: var(--#{$prefix}body-font-weight);\n  line-height: var(--#{$prefix}body-line-height);\n  color: var(--#{$prefix}body-color);\n  text-align: var(--#{$prefix}body-text-align);\n  background-color: var(--#{$prefix}body-bg); // 2\n  -webkit-text-size-adjust: 100%; // 3\n  -webkit-tap-highlight-color: rgba($black, 0); // 4\n}\n// scss-docs-end reboot-body-rules\n\n\n// Content grouping\n//\n// 1. Reset Firefox's gray color\n\nhr {\n  margin: $hr-margin-y 0;\n  color: $hr-color; // 1\n  border: 0;\n  border-top: $hr-border-width solid $hr-border-color;\n  opacity: $hr-opacity;\n}\n\n\n// Typography\n//\n// 1. Remove top margins from headings\n//    By default, `<h1>`-`<h6>` all receive top and bottom margins. We nuke the top\n//    margin for easier control within type scales as it avoids margin collapsing.\n\n%heading {\n  margin-top: 0; // 1\n  margin-bottom: $headings-margin-bottom;\n  font-family: $headings-font-family;\n  font-style: $headings-font-style;\n  font-weight: $headings-font-weight;\n  line-height: $headings-line-height;\n  color: $headings-color;\n}\n\nh1 {\n  @extend %heading;\n  @include font-size($h1-font-size);\n}\n\nh2 {\n  @extend %heading;\n  @include font-size($h2-font-size);\n}\n\nh3 {\n  @extend %heading;\n  @include font-size($h3-font-size);\n}\n\nh4 {\n  @extend %heading;\n  @include font-size($h4-font-size);\n}\n\nh5 {\n  @extend %heading;\n  @include font-size($h5-font-size);\n}\n\nh6 {\n  @extend %heading;\n  @include font-size($h6-font-size);\n}\n\n\n// Reset margins on paragraphs\n//\n// Similarly, the top margin on `<p>`s get reset. However, we also reset the\n// bottom margin to use `rem` units instead of `em`.\n\np {\n  margin-top: 0;\n  margin-bottom: $paragraph-margin-bottom;\n}\n\n\n// Abbreviations\n//\n// 1. Add the correct text decoration in Chrome, Edge, Opera, and Safari.\n// 2. Add explicit cursor to indicate changed behavior.\n// 3. Prevent the text-decoration to be skipped.\n\nabbr[title] {\n  text-decoration: underline dotted; // 1\n  cursor: help; // 2\n  text-decoration-skip-ink: none; // 3\n}\n\n\n// Address\n\naddress {\n  margin-bottom: 1rem;\n  font-style: normal;\n  line-height: inherit;\n}\n\n\n// Lists\n\nol,\nul {\n  padding-left: 2rem;\n}\n\nol,\nul,\ndl {\n  margin-top: 0;\n  margin-bottom: 1rem;\n}\n\nol ol,\nul ul,\nol ul,\nul ol {\n  margin-bottom: 0;\n}\n\ndt {\n  font-weight: $dt-font-weight;\n}\n\n// 1. Undo browser default\n\ndd {\n  margin-bottom: .5rem;\n  margin-left: 0; // 1\n}\n\n\n// Blockquote\n\nblockquote {\n  margin: 0 0 1rem;\n}\n\n\n// Strong\n//\n// Add the correct font weight in Chrome, Edge, and Safari\n\nb,\nstrong {\n  font-weight: $font-weight-bolder;\n}\n\n\n// Small\n//\n// Add the correct font size in all browsers\n\nsmall {\n  @include font-size($small-font-size);\n}\n\n\n// Mark\n\nmark {\n  padding: $mark-padding;\n  background-color: var(--#{$prefix}highlight-bg);\n}\n\n\n// Sub and Sup\n//\n// Prevent `sub` and `sup` elements from affecting the line height in\n// all browsers.\n\nsub,\nsup {\n  position: relative;\n  @include font-size($sub-sup-font-size);\n  line-height: 0;\n  vertical-align: baseline;\n}\n\nsub { bottom: -.25em; }\nsup { top: -.5em; }\n\n\n// Links\n\na {\n  color: var(--#{$prefix}link-color);\n  text-decoration: $link-decoration;\n\n  &:hover {\n    color: var(--#{$prefix}link-hover-color);\n    text-decoration: $link-hover-decoration;\n  }\n}\n\n// And undo these styles for placeholder links/named anchors (without href).\n// It would be more straightforward to just use a[href] in previous block, but that\n// causes specificity issues in many other styles that are too complex to fix.\n// See https://github.com/twbs/bootstrap/issues/19402\n\na:not([href]):not([class]) {\n  &,\n  &:hover {\n    color: inherit;\n    text-decoration: none;\n  }\n}\n\n\n// Code\n\npre,\ncode,\nkbd,\nsamp {\n  font-family: $font-family-code;\n  @include font-size(1em); // Correct the odd `em` font sizing in all browsers.\n}\n\n// 1. Remove browser default top margin\n// 2. Reset browser default of `1em` to use `rem`s\n// 3. Don't allow content to break outside\n\npre {\n  display: block;\n  margin-top: 0; // 1\n  margin-bottom: 1rem; // 2\n  overflow: auto; // 3\n  @include font-size($code-font-size);\n  color: $pre-color;\n\n  // Account for some code outputs that place code tags in pre tags\n  code {\n    @include font-size(inherit);\n    color: inherit;\n    word-break: normal;\n  }\n}\n\ncode {\n  @include font-size($code-font-size);\n  color: var(--#{$prefix}code-color);\n  word-wrap: break-word;\n\n  // Streamline the style when inside anchors to avoid broken underline and more\n  a > & {\n    color: inherit;\n  }\n}\n\nkbd {\n  padding: $kbd-padding-y $kbd-padding-x;\n  @include font-size($kbd-font-size);\n  color: $kbd-color;\n  background-color: $kbd-bg;\n  @include border-radius($border-radius-sm);\n\n  kbd {\n    padding: 0;\n    @include font-size(1em);\n    font-weight: $nested-kbd-font-weight;\n  }\n}\n\n\n// Figures\n//\n// Apply a consistent margin strategy (matches our type styles).\n\nfigure {\n  margin: 0 0 1rem;\n}\n\n\n// Images and content\n\nimg,\nsvg {\n  vertical-align: middle;\n}\n\n\n// Tables\n//\n// Prevent double borders\n\ntable {\n  caption-side: bottom;\n  border-collapse: collapse;\n}\n\ncaption {\n  padding-top: $table-cell-padding-y;\n  padding-bottom: $table-cell-padding-y;\n  color: $table-caption-color;\n  text-align: left;\n}\n\n// 1. Removes font-weight bold by inheriting\n// 2. Matches default `<td>` alignment by inheriting `text-align`.\n// 3. Fix alignment for Safari\n\nth {\n  font-weight: $table-th-font-weight; // 1\n  text-align: inherit; // 2\n  text-align: -webkit-match-parent; // 3\n}\n\nthead,\ntbody,\ntfoot,\ntr,\ntd,\nth {\n  border-color: inherit;\n  border-style: solid;\n  border-width: 0;\n}\n\n\n// Forms\n//\n// 1. Allow labels to use `margin` for spacing.\n\nlabel {\n  display: inline-block; // 1\n}\n\n// Remove the default `border-radius` that macOS Chrome adds.\n// See https://github.com/twbs/bootstrap/issues/24093\n\nbutton {\n  // stylelint-disable-next-line property-disallowed-list\n  border-radius: 0;\n}\n\n// Explicitly remove focus outline in Chromium when it shouldn't be\n// visible (e.g. as result of mouse click or touch tap). It already\n// should be doing this automatically, but seems to currently be\n// confused and applies its very visible two-tone outline anyway.\n\nbutton:focus:not(:focus-visible) {\n  outline: 0;\n}\n\n// 1. Remove the margin in Firefox and Safari\n\ninput,\nbutton,\nselect,\noptgroup,\ntextarea {\n  margin: 0; // 1\n  font-family: inherit;\n  @include font-size(inherit);\n  line-height: inherit;\n}\n\n// Remove the inheritance of text transform in Firefox\nbutton,\nselect {\n  text-transform: none;\n}\n// Set the cursor for non-`<button>` buttons\n//\n// Details at https://github.com/twbs/bootstrap/pull/30562\n[role=\"button\"] {\n  cursor: pointer;\n}\n\nselect {\n  // Remove the inheritance of word-wrap in Safari.\n  // See https://github.com/twbs/bootstrap/issues/24990\n  word-wrap: normal;\n\n  // Undo the opacity change from Chrome\n  &:disabled {\n    opacity: 1;\n  }\n}\n\n// Remove the dropdown arrow only from text type inputs built with datalists in Chrome.\n// See https://stackoverflow.com/a/54997118\n\n[list]:not([type=\"date\"]):not([type=\"datetime-local\"]):not([type=\"month\"]):not([type=\"week\"]):not([type=\"time\"])::-webkit-calendar-picker-indicator {\n  display: none !important;\n}\n\n// 1. Prevent a WebKit bug where (2) destroys native `audio` and `video`\n//    controls in Android 4.\n// 2. Correct the inability to style clickable types in iOS and Safari.\n// 3. Opinionated: add \"hand\" cursor to non-disabled button elements.\n\nbutton,\n[type=\"button\"], // 1\n[type=\"reset\"],\n[type=\"submit\"] {\n  -webkit-appearance: button; // 2\n\n  @if $enable-button-pointers {\n    &:not(:disabled) {\n      cursor: pointer; // 3\n    }\n  }\n}\n\n// Remove inner border and padding from Firefox, but don't restore the outline like Normalize.\n\n::-moz-focus-inner {\n  padding: 0;\n  border-style: none;\n}\n\n// 1. Textareas should really only resize vertically so they don't break their (horizontal) containers.\n\ntextarea {\n  resize: vertical; // 1\n}\n\n// 1. Browsers set a default `min-width: min-content;` on fieldsets,\n//    unlike e.g. `<div>`s, which have `min-width: 0;` by default.\n//    So we reset that to ensure fieldsets behave more like a standard block element.\n//    See https://github.com/twbs/bootstrap/issues/12359\n//    and https://html.spec.whatwg.org/multipage/#the-fieldset-and-legend-elements\n// 2. Reset the default outline behavior of fieldsets so they don't affect page layout.\n\nfieldset {\n  min-width: 0; // 1\n  padding: 0; // 2\n  margin: 0; // 2\n  border: 0; // 2\n}\n\n// 1. By using `float: left`, the legend will behave like a block element.\n//    This way the border of a fieldset wraps around the legend if present.\n// 2. Fix wrapping bug.\n//    See https://github.com/twbs/bootstrap/issues/29712\n\nlegend {\n  float: left; // 1\n  width: 100%;\n  padding: 0;\n  margin-bottom: $legend-margin-bottom;\n  @include font-size($legend-font-size);\n  font-weight: $legend-font-weight;\n  line-height: inherit;\n\n  + * {\n    clear: left; // 2\n  }\n}\n\n// Fix height of inputs with a type of datetime-local, date, month, week, or time\n// See https://github.com/twbs/bootstrap/issues/18842\n\n::-webkit-datetime-edit-fields-wrapper,\n::-webkit-datetime-edit-text,\n::-webkit-datetime-edit-minute,\n::-webkit-datetime-edit-hour-field,\n::-webkit-datetime-edit-day-field,\n::-webkit-datetime-edit-month-field,\n::-webkit-datetime-edit-year-field {\n  padding: 0;\n}\n\n::-webkit-inner-spin-button {\n  height: auto;\n}\n\n// 1. Correct the outline style in Safari.\n// 2. This overrides the extra rounded corners on search inputs in iOS so that our\n//    `.form-control` class can properly style them. Note that this cannot simply\n//    be added to `.form-control` as it's not specific enough. For details, see\n//    https://github.com/twbs/bootstrap/issues/11586.\n\n[type=\"search\"] {\n  outline-offset: -2px; // 1\n  -webkit-appearance: textfield; // 2\n}\n\n// 1. A few input types should stay LTR\n// See https://rtlstyling.com/posts/rtl-styling#form-inputs\n// 2. RTL only output\n// See https://rtlcss.com/learn/usage-guide/control-directives/#raw\n\n/* rtl:raw:\n[type=\"tel\"],\n[type=\"url\"],\n[type=\"email\"],\n[type=\"number\"] {\n  direction: ltr;\n}\n*/\n\n// Remove the inner padding in Chrome and Safari on macOS.\n\n::-webkit-search-decoration {\n  -webkit-appearance: none;\n}\n\n// Remove padding around color pickers in webkit browsers\n\n::-webkit-color-swatch-wrapper {\n  padding: 0;\n}\n\n\n// 1. Inherit font family and line height for file input buttons\n// 2. Correct the inability to style clickable types in iOS and Safari.\n\n::file-selector-button {\n  font: inherit; // 1\n  -webkit-appearance: button; // 2\n}\n\n// Correct element displays\n\noutput {\n  display: inline-block;\n}\n\n// Remove border from iframe\n\niframe {\n  border: 0;\n}\n\n// Summary\n//\n// 1. Add the correct display in all browsers\n\nsummary {\n  display: list-item; // 1\n  cursor: pointer;\n}\n\n\n// Progress\n//\n// Add the correct vertical alignment in Chrome, Firefox, and Opera.\n\nprogress {\n  vertical-align: baseline;\n}\n\n\n// Hidden attribute\n//\n// Always hide an element with the `hidden` HTML attribute.\n\n[hidden] {\n  display: none !important;\n}\n", "// Variables\n//\n// Variables should follow the `$component-state-property-size` formula for\n// consistent naming. Ex: $nav-link-disabled-color and $modal-content-box-shadow-xs.\n\n// Color system\n\n// scss-docs-start gray-color-variables\n$white:    #fff !default;\n$gray-100: #f8f9fa !default;\n$gray-200: #e9ecef !default;\n$gray-300: #dee2e6 !default;\n$gray-400: #ced4da !default;\n$gray-500: #adb5bd !default;\n$gray-600: #6c757d !default;\n$gray-700: #495057 !default;\n$gray-800: #343a40 !default;\n$gray-900: #212529 !default;\n$black:    #000 !default;\n// scss-docs-end gray-color-variables\n\n// fusv-disable\n// scss-docs-start gray-colors-map\n$grays: (\n  \"100\": $gray-100,\n  \"200\": $gray-200,\n  \"300\": $gray-300,\n  \"400\": $gray-400,\n  \"500\": $gray-500,\n  \"600\": $gray-600,\n  \"700\": $gray-700,\n  \"800\": $gray-800,\n  \"900\": $gray-900\n) !default;\n// scss-docs-end gray-colors-map\n// fusv-enable\n\n// scss-docs-start color-variables\n$blue:    #0d6efd !default;\n$indigo:  #6610f2 !default;\n$purple:  #6f42c1 !default;\n$pink:    #d63384 !default;\n$red:     #dc3545 !default;\n$orange:  #fd7e14 !default;\n$yellow:  #ffc107 !default;\n$green:   #198754 !default;\n$teal:    #20c997 !default;\n$cyan:    #0dcaf0 !default;\n// scss-docs-end color-variables\n\n// scss-docs-start colors-map\n$colors: (\n  \"blue\":       $blue,\n  \"indigo\":     $indigo,\n  \"purple\":     $purple,\n  \"pink\":       $pink,\n  \"red\":        $red,\n  \"orange\":     $orange,\n  \"yellow\":     $yellow,\n  \"green\":      $green,\n  \"teal\":       $teal,\n  \"cyan\":       $cyan,\n  \"black\":      $black,\n  \"white\":      $white,\n  \"gray\":       $gray-600,\n  \"gray-dark\":  $gray-800\n) !default;\n// scss-docs-end colors-map\n\n// The contrast ratio to reach against white, to determine if color changes from \"light\" to \"dark\". Acceptable values for WCAG 2.0 are 3, 4.5 and 7.\n// See https://www.w3.org/TR/WCAG20/#visual-audio-contrast-contrast\n$min-contrast-ratio:   4.5 !default;\n\n// Customize the light and dark text colors for use in our color contrast function.\n$color-contrast-dark:      $black !default;\n$color-contrast-light:     $white !default;\n\n// fusv-disable\n$blue-100: tint-color($blue, 80%) !default;\n$blue-200: tint-color($blue, 60%) !default;\n$blue-300: tint-color($blue, 40%) !default;\n$blue-400: tint-color($blue, 20%) !default;\n$blue-500: $blue !default;\n$blue-600: shade-color($blue, 20%) !default;\n$blue-700: shade-color($blue, 40%) !default;\n$blue-800: shade-color($blue, 60%) !default;\n$blue-900: shade-color($blue, 80%) !default;\n\n$indigo-100: tint-color($indigo, 80%) !default;\n$indigo-200: tint-color($indigo, 60%) !default;\n$indigo-300: tint-color($indigo, 40%) !default;\n$indigo-400: tint-color($indigo, 20%) !default;\n$indigo-500: $indigo !default;\n$indigo-600: shade-color($indigo, 20%) !default;\n$indigo-700: shade-color($indigo, 40%) !default;\n$indigo-800: shade-color($indigo, 60%) !default;\n$indigo-900: shade-color($indigo, 80%) !default;\n\n$purple-100: tint-color($purple, 80%) !default;\n$purple-200: tint-color($purple, 60%) !default;\n$purple-300: tint-color($purple, 40%) !default;\n$purple-400: tint-color($purple, 20%) !default;\n$purple-500: $purple !default;\n$purple-600: shade-color($purple, 20%) !default;\n$purple-700: shade-color($purple, 40%) !default;\n$purple-800: shade-color($purple, 60%) !default;\n$purple-900: shade-color($purple, 80%) !default;\n\n$pink-100: tint-color($pink, 80%) !default;\n$pink-200: tint-color($pink, 60%) !default;\n$pink-300: tint-color($pink, 40%) !default;\n$pink-400: tint-color($pink, 20%) !default;\n$pink-500: $pink !default;\n$pink-600: shade-color($pink, 20%) !default;\n$pink-700: shade-color($pink, 40%) !default;\n$pink-800: shade-color($pink, 60%) !default;\n$pink-900: shade-color($pink, 80%) !default;\n\n$red-100: tint-color($red, 80%) !default;\n$red-200: tint-color($red, 60%) !default;\n$red-300: tint-color($red, 40%) !default;\n$red-400: tint-color($red, 20%) !default;\n$red-500: $red !default;\n$red-600: shade-color($red, 20%) !default;\n$red-700: shade-color($red, 40%) !default;\n$red-800: shade-color($red, 60%) !default;\n$red-900: shade-color($red, 80%) !default;\n\n$orange-100: tint-color($orange, 80%) !default;\n$orange-200: tint-color($orange, 60%) !default;\n$orange-300: tint-color($orange, 40%) !default;\n$orange-400: tint-color($orange, 20%) !default;\n$orange-500: $orange !default;\n$orange-600: shade-color($orange, 20%) !default;\n$orange-700: shade-color($orange, 40%) !default;\n$orange-800: shade-color($orange, 60%) !default;\n$orange-900: shade-color($orange, 80%) !default;\n\n$yellow-100: tint-color($yellow, 80%) !default;\n$yellow-200: tint-color($yellow, 60%) !default;\n$yellow-300: tint-color($yellow, 40%) !default;\n$yellow-400: tint-color($yellow, 20%) !default;\n$yellow-500: $yellow !default;\n$yellow-600: shade-color($yellow, 20%) !default;\n$yellow-700: shade-color($yellow, 40%) !default;\n$yellow-800: shade-color($yellow, 60%) !default;\n$yellow-900: shade-color($yellow, 80%) !default;\n\n$green-100: tint-color($green, 80%) !default;\n$green-200: tint-color($green, 60%) !default;\n$green-300: tint-color($green, 40%) !default;\n$green-400: tint-color($green, 20%) !default;\n$green-500: $green !default;\n$green-600: shade-color($green, 20%) !default;\n$green-700: shade-color($green, 40%) !default;\n$green-800: shade-color($green, 60%) !default;\n$green-900: shade-color($green, 80%) !default;\n\n$teal-100: tint-color($teal, 80%) !default;\n$teal-200: tint-color($teal, 60%) !default;\n$teal-300: tint-color($teal, 40%) !default;\n$teal-400: tint-color($teal, 20%) !default;\n$teal-500: $teal !default;\n$teal-600: shade-color($teal, 20%) !default;\n$teal-700: shade-color($teal, 40%) !default;\n$teal-800: shade-color($teal, 60%) !default;\n$teal-900: shade-color($teal, 80%) !default;\n\n$cyan-100: tint-color($cyan, 80%) !default;\n$cyan-200: tint-color($cyan, 60%) !default;\n$cyan-300: tint-color($cyan, 40%) !default;\n$cyan-400: tint-color($cyan, 20%) !default;\n$cyan-500: $cyan !default;\n$cyan-600: shade-color($cyan, 20%) !default;\n$cyan-700: shade-color($cyan, 40%) !default;\n$cyan-800: shade-color($cyan, 60%) !default;\n$cyan-900: shade-color($cyan, 80%) !default;\n\n$blues: (\n  \"blue-100\": $blue-100,\n  \"blue-200\": $blue-200,\n  \"blue-300\": $blue-300,\n  \"blue-400\": $blue-400,\n  \"blue-500\": $blue-500,\n  \"blue-600\": $blue-600,\n  \"blue-700\": $blue-700,\n  \"blue-800\": $blue-800,\n  \"blue-900\": $blue-900\n) !default;\n\n$indigos: (\n  \"indigo-100\": $indigo-100,\n  \"indigo-200\": $indigo-200,\n  \"indigo-300\": $indigo-300,\n  \"indigo-400\": $indigo-400,\n  \"indigo-500\": $indigo-500,\n  \"indigo-600\": $indigo-600,\n  \"indigo-700\": $indigo-700,\n  \"indigo-800\": $indigo-800,\n  \"indigo-900\": $indigo-900\n) !default;\n\n$purples: (\n  \"purple-100\": $purple-100,\n  \"purple-200\": $purple-200,\n  \"purple-300\": $purple-300,\n  \"purple-400\": $purple-400,\n  \"purple-500\": $purple-500,\n  \"purple-600\": $purple-600,\n  \"purple-700\": $purple-700,\n  \"purple-800\": $purple-800,\n  \"purple-900\": $purple-900\n) !default;\n\n$pinks: (\n  \"pink-100\": $pink-100,\n  \"pink-200\": $pink-200,\n  \"pink-300\": $pink-300,\n  \"pink-400\": $pink-400,\n  \"pink-500\": $pink-500,\n  \"pink-600\": $pink-600,\n  \"pink-700\": $pink-700,\n  \"pink-800\": $pink-800,\n  \"pink-900\": $pink-900\n) !default;\n\n$reds: (\n  \"red-100\": $red-100,\n  \"red-200\": $red-200,\n  \"red-300\": $red-300,\n  \"red-400\": $red-400,\n  \"red-500\": $red-500,\n  \"red-600\": $red-600,\n  \"red-700\": $red-700,\n  \"red-800\": $red-800,\n  \"red-900\": $red-900\n) !default;\n\n$oranges: (\n  \"orange-100\": $orange-100,\n  \"orange-200\": $orange-200,\n  \"orange-300\": $orange-300,\n  \"orange-400\": $orange-400,\n  \"orange-500\": $orange-500,\n  \"orange-600\": $orange-600,\n  \"orange-700\": $orange-700,\n  \"orange-800\": $orange-800,\n  \"orange-900\": $orange-900\n) !default;\n\n$yellows: (\n  \"yellow-100\": $yellow-100,\n  \"yellow-200\": $yellow-200,\n  \"yellow-300\": $yellow-300,\n  \"yellow-400\": $yellow-400,\n  \"yellow-500\": $yellow-500,\n  \"yellow-600\": $yellow-600,\n  \"yellow-700\": $yellow-700,\n  \"yellow-800\": $yellow-800,\n  \"yellow-900\": $yellow-900\n) !default;\n\n$greens: (\n  \"green-100\": $green-100,\n  \"green-200\": $green-200,\n  \"green-300\": $green-300,\n  \"green-400\": $green-400,\n  \"green-500\": $green-500,\n  \"green-600\": $green-600,\n  \"green-700\": $green-700,\n  \"green-800\": $green-800,\n  \"green-900\": $green-900\n) !default;\n\n$teals: (\n  \"teal-100\": $teal-100,\n  \"teal-200\": $teal-200,\n  \"teal-300\": $teal-300,\n  \"teal-400\": $teal-400,\n  \"teal-500\": $teal-500,\n  \"teal-600\": $teal-600,\n  \"teal-700\": $teal-700,\n  \"teal-800\": $teal-800,\n  \"teal-900\": $teal-900\n) !default;\n\n$cyans: (\n  \"cyan-100\": $cyan-100,\n  \"cyan-200\": $cyan-200,\n  \"cyan-300\": $cyan-300,\n  \"cyan-400\": $cyan-400,\n  \"cyan-500\": $cyan-500,\n  \"cyan-600\": $cyan-600,\n  \"cyan-700\": $cyan-700,\n  \"cyan-800\": $cyan-800,\n  \"cyan-900\": $cyan-900\n) !default;\n// fusv-enable\n\n// scss-docs-start theme-color-variables\n$primary:       $blue !default;\n$secondary:     $gray-600 !default;\n$success:       $green !default;\n$info:          $cyan !default;\n$warning:       $yellow !default;\n$danger:        $red !default;\n$light:         $gray-100 !default;\n$dark:          $gray-900 !default;\n// scss-docs-end theme-color-variables\n\n// scss-docs-start theme-colors-map\n$theme-colors: (\n  \"primary\":    $primary,\n  \"secondary\":  $secondary,\n  \"success\":    $success,\n  \"info\":       $info,\n  \"warning\":    $warning,\n  \"danger\":     $danger,\n  \"light\":      $light,\n  \"dark\":       $dark\n) !default;\n// scss-docs-end theme-colors-map\n\n// Characters which are escaped by the escape-svg function\n$escaped-characters: (\n  (\"<\", \"%3c\"),\n  (\">\", \"%3e\"),\n  (\"#\", \"%23\"),\n  (\"(\", \"%28\"),\n  (\")\", \"%29\"),\n) !default;\n\n// Options\n//\n// Quickly modify global styling by enabling or disabling optional features.\n\n$enable-caret:                true !default;\n$enable-rounded:              true !default;\n$enable-shadows:              false !default;\n$enable-gradients:            false !default;\n$enable-transitions:          true !default;\n$enable-reduced-motion:       true !default;\n$enable-smooth-scroll:        true !default;\n$enable-grid-classes:         true !default;\n$enable-container-classes:    true !default;\n$enable-cssgrid:              false !default;\n$enable-button-pointers:      true !default;\n$enable-rfs:                  true !default;\n$enable-validation-icons:     true !default;\n$enable-negative-margins:     false !default;\n$enable-deprecation-messages: true !default;\n$enable-important-utilities:  true !default;\n\n// Prefix for :root CSS variables\n\n$variable-prefix:             bs- !default; // Deprecated in v5.2.0 for the shorter `$prefix`\n$prefix:                      $variable-prefix !default;\n\n// Gradient\n//\n// The gradient which is added to components if `$enable-gradients` is `true`\n// This gradient is also added to elements with `.bg-gradient`\n// scss-docs-start variable-gradient\n$gradient: linear-gradient(180deg, rgba($white, .15), rgba($white, 0)) !default;\n// scss-docs-end variable-gradient\n\n// Spacing\n//\n// Control the default styling of most Bootstrap elements by modifying these\n// variables. Mostly focused on spacing.\n// You can add more entries to the $spacers map, should you need more variation.\n\n// scss-docs-start spacer-variables-maps\n$spacer: 1rem !default;\n$spacers: (\n  0: 0,\n  1: $spacer * .25,\n  2: $spacer * .5,\n  3: $spacer,\n  4: $spacer * 1.5,\n  5: $spacer * 3,\n) !default;\n// scss-docs-end spacer-variables-maps\n\n// Position\n//\n// Define the edge positioning anchors of the position utilities.\n\n// scss-docs-start position-map\n$position-values: (\n  0: 0,\n  50: 50%,\n  100: 100%\n) !default;\n// scss-docs-end position-map\n\n// Body\n//\n// Settings for the `<body>` element.\n\n$body-bg:                   $white !default;\n$body-color:                $gray-900 !default;\n$body-text-align:           null !default;\n\n// Links\n//\n// Style anchor elements.\n\n$link-color:                              $primary !default;\n$link-decoration:                         underline !default;\n$link-shade-percentage:                   20% !default;\n$link-hover-color:                        shift-color($link-color, $link-shade-percentage) !default;\n$link-hover-decoration:                   null !default;\n\n$stretched-link-pseudo-element:           after !default;\n$stretched-link-z-index:                  1 !default;\n\n// Paragraphs\n//\n// Style p element.\n\n$paragraph-margin-bottom:   1rem !default;\n\n\n// Grid breakpoints\n//\n// Define the minimum dimensions at which your layout will change,\n// adapting to different screen sizes, for use in media queries.\n\n// scss-docs-start grid-breakpoints\n$grid-breakpoints: (\n  xs: 0,\n  sm: 576px,\n  md: 768px,\n  lg: 992px,\n  xl: 1200px,\n  xxl: 1400px\n) !default;\n// scss-docs-end grid-breakpoints\n\n@include _assert-ascending($grid-breakpoints, \"$grid-breakpoints\");\n@include _assert-starts-at-zero($grid-breakpoints, \"$grid-breakpoints\");\n\n\n// Grid containers\n//\n// Define the maximum width of `.container` for different screen sizes.\n\n// scss-docs-start container-max-widths\n$container-max-widths: (\n  sm: 540px,\n  md: 720px,\n  lg: 960px,\n  xl: 1140px,\n  xxl: 1320px\n) !default;\n// scss-docs-end container-max-widths\n\n@include _assert-ascending($container-max-widths, \"$container-max-widths\");\n\n\n// Grid columns\n//\n// Set the number of columns and specify the width of the gutters.\n\n$grid-columns:                12 !default;\n$grid-gutter-width:           1.5rem !default;\n$grid-row-columns:            6 !default;\n\n// Container padding\n\n$container-padding-x: $grid-gutter-width !default;\n\n\n// Components\n//\n// Define common padding and border radius sizes and more.\n\n// scss-docs-start border-variables\n$border-width:                1px !default;\n$border-widths: (\n  1: 1px,\n  2: 2px,\n  3: 3px,\n  4: 4px,\n  5: 5px\n) !default;\n\n$border-style:                solid !default;\n$border-color:                $gray-300 !default;\n$border-color-translucent:    rgba($black, .175) !default;\n// scss-docs-end border-variables\n\n// scss-docs-start border-radius-variables\n$border-radius:               .375rem !default;\n$border-radius-sm:            .25rem !default;\n$border-radius-lg:            .5rem !default;\n$border-radius-xl:            1rem !default;\n$border-radius-2xl:           2rem !default;\n$border-radius-pill:          50rem !default;\n// scss-docs-end border-radius-variables\n\n// scss-docs-start box-shadow-variables\n$box-shadow:                  0 .5rem 1rem rgba($black, .15) !default;\n$box-shadow-sm:               0 .125rem .25rem rgba($black, .075) !default;\n$box-shadow-lg:               0 1rem 3rem rgba($black, .175) !default;\n$box-shadow-inset:            inset 0 1px 2px rgba($black, .075) !default;\n// scss-docs-end box-shadow-variables\n\n$component-active-color:      $white !default;\n$component-active-bg:         $primary !default;\n\n// scss-docs-start caret-variables\n$caret-width:                 .3em !default;\n$caret-vertical-align:        $caret-width * .85 !default;\n$caret-spacing:               $caret-width * .85 !default;\n// scss-docs-end caret-variables\n\n$transition-base:             all .2s ease-in-out !default;\n$transition-fade:             opacity .15s linear !default;\n// scss-docs-start collapse-transition\n$transition-collapse:         height .35s ease !default;\n$transition-collapse-width:   width .35s ease !default;\n// scss-docs-end collapse-transition\n\n// stylelint-disable function-disallowed-list\n// scss-docs-start aspect-ratios\n$aspect-ratios: (\n  \"1x1\": 100%,\n  \"4x3\": calc(3 / 4 * 100%),\n  \"16x9\": calc(9 / 16 * 100%),\n  \"21x9\": calc(9 / 21 * 100%)\n) !default;\n// scss-docs-end aspect-ratios\n// stylelint-enable function-disallowed-list\n\n// Typography\n//\n// Font, line-height, and color for body text, headings, and more.\n\n// scss-docs-start font-variables\n// stylelint-disable value-keyword-case\n$font-family-sans-serif:      system-ui, -apple-system, \"Segoe UI\", Roboto, \"Helvetica Neue\", \"Noto Sans\", \"Liberation Sans\", Arial, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\" !default;\n$font-family-monospace:       SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace !default;\n// stylelint-enable value-keyword-case\n$font-family-base:            var(--#{$prefix}font-sans-serif) !default;\n$font-family-code:            var(--#{$prefix}font-monospace) !default;\n\n// $font-size-root affects the value of `rem`, which is used for as well font sizes, paddings, and margins\n// $font-size-base affects the font size of the body text\n$font-size-root:              null !default;\n$font-size-base:              1rem !default; // Assumes the browser default, typically `16px`\n$font-size-sm:                $font-size-base * .875 !default;\n$font-size-lg:                $font-size-base * 1.25 !default;\n\n$font-weight-lighter:         lighter !default;\n$font-weight-light:           300 !default;\n$font-weight-normal:          400 !default;\n$font-weight-semibold:        600 !default;\n$font-weight-bold:            700 !default;\n$font-weight-bolder:          bolder !default;\n\n$font-weight-base:            $font-weight-normal !default;\n\n$line-height-base:            1.5 !default;\n$line-height-sm:              1.25 !default;\n$line-height-lg:              2 !default;\n\n$h1-font-size:                $font-size-base * 2.5 !default;\n$h2-font-size:                $font-size-base * 2 !default;\n$h3-font-size:                $font-size-base * 1.75 !default;\n$h4-font-size:                $font-size-base * 1.5 !default;\n$h5-font-size:                $font-size-base * 1.25 !default;\n$h6-font-size:                $font-size-base !default;\n// scss-docs-end font-variables\n\n// scss-docs-start font-sizes\n$font-sizes: (\n  1: $h1-font-size,\n  2: $h2-font-size,\n  3: $h3-font-size,\n  4: $h4-font-size,\n  5: $h5-font-size,\n  6: $h6-font-size\n) !default;\n// scss-docs-end font-sizes\n\n// scss-docs-start headings-variables\n$headings-margin-bottom:      $spacer * .5 !default;\n$headings-font-family:        null !default;\n$headings-font-style:         null !default;\n$headings-font-weight:        500 !default;\n$headings-line-height:        1.2 !default;\n$headings-color:              null !default;\n// scss-docs-end headings-variables\n\n// scss-docs-start display-headings\n$display-font-sizes: (\n  1: 5rem,\n  2: 4.5rem,\n  3: 4rem,\n  4: 3.5rem,\n  5: 3rem,\n  6: 2.5rem\n) !default;\n\n$display-font-family: null !default;\n$display-font-style:  null !default;\n$display-font-weight: 300 !default;\n$display-line-height: $headings-line-height !default;\n// scss-docs-end display-headings\n\n// scss-docs-start type-variables\n$lead-font-size:              $font-size-base * 1.25 !default;\n$lead-font-weight:            300 !default;\n\n$small-font-size:             .875em !default;\n\n$sub-sup-font-size:           .75em !default;\n\n$text-muted:                  $gray-600 !default;\n\n$initialism-font-size:        $small-font-size !default;\n\n$blockquote-margin-y:         $spacer !default;\n$blockquote-font-size:        $font-size-base * 1.25 !default;\n$blockquote-footer-color:     $gray-600 !default;\n$blockquote-footer-font-size: $small-font-size !default;\n\n$hr-margin-y:                 $spacer !default;\n$hr-color:                    inherit !default;\n\n// fusv-disable\n$hr-bg-color:                 null !default; // Deprecated in v5.2.0\n$hr-height:                   null !default; // Deprecated in v5.2.0\n// fusv-enable\n\n$hr-border-color:             null !default; // Allows for inherited colors\n$hr-border-width:             $border-width !default;\n$hr-opacity:                  .25 !default;\n\n$legend-margin-bottom:        .5rem !default;\n$legend-font-size:            1.5rem !default;\n$legend-font-weight:          null !default;\n\n$dt-font-weight:              $font-weight-bold !default;\n\n$list-inline-padding:         .5rem !default;\n\n$mark-padding:                .1875em !default;\n$mark-bg:                     $yellow-100 !default;\n// scss-docs-end type-variables\n\n\n// Tables\n//\n// Customizes the `.table` component with basic values, each used across all table variations.\n\n// scss-docs-start table-variables\n$table-cell-padding-y:        .5rem !default;\n$table-cell-padding-x:        .5rem !default;\n$table-cell-padding-y-sm:     .25rem !default;\n$table-cell-padding-x-sm:     .25rem !default;\n\n$table-cell-vertical-align:   top !default;\n\n$table-color:                 var(--#{$prefix}body-color) !default;\n$table-bg:                    transparent !default;\n$table-accent-bg:             transparent !default;\n\n$table-th-font-weight:        null !default;\n\n$table-striped-color:         $table-color !default;\n$table-striped-bg-factor:     .05 !default;\n$table-striped-bg:            rgba($black, $table-striped-bg-factor) !default;\n\n$table-active-color:          $table-color !default;\n$table-active-bg-factor:      .1 !default;\n$table-active-bg:             rgba($black, $table-active-bg-factor) !default;\n\n$table-hover-color:           $table-color !default;\n$table-hover-bg-factor:       .075 !default;\n$table-hover-bg:              rgba($black, $table-hover-bg-factor) !default;\n\n$table-border-factor:         .1 !default;\n$table-border-width:          $border-width !default;\n$table-border-color:          var(--#{$prefix}border-color) !default;\n\n$table-striped-order:         odd !default;\n$table-striped-columns-order: even !default;\n\n$table-group-separator-color: currentcolor !default;\n\n$table-caption-color:         $text-muted !default;\n\n$table-bg-scale:              -80% !default;\n// scss-docs-end table-variables\n\n// scss-docs-start table-loop\n$table-variants: (\n  \"primary\":    shift-color($primary, $table-bg-scale),\n  \"secondary\":  shift-color($secondary, $table-bg-scale),\n  \"success\":    shift-color($success, $table-bg-scale),\n  \"info\":       shift-color($info, $table-bg-scale),\n  \"warning\":    shift-color($warning, $table-bg-scale),\n  \"danger\":     shift-color($danger, $table-bg-scale),\n  \"light\":      $light,\n  \"dark\":       $dark,\n) !default;\n// scss-docs-end table-loop\n\n\n// Buttons + Forms\n//\n// Shared variables that are reassigned to `$input-` and `$btn-` specific variables.\n\n// scss-docs-start input-btn-variables\n$input-btn-padding-y:         .375rem !default;\n$input-btn-padding-x:         .75rem !default;\n$input-btn-font-family:       null !default;\n$input-btn-font-size:         $font-size-base !default;\n$input-btn-line-height:       $line-height-base !default;\n\n$input-btn-focus-width:         .25rem !default;\n$input-btn-focus-color-opacity: .25 !default;\n$input-btn-focus-color:         rgba($component-active-bg, $input-btn-focus-color-opacity) !default;\n$input-btn-focus-blur:          0 !default;\n$input-btn-focus-box-shadow:    0 0 $input-btn-focus-blur $input-btn-focus-width $input-btn-focus-color !default;\n\n$input-btn-padding-y-sm:      .25rem !default;\n$input-btn-padding-x-sm:      .5rem !default;\n$input-btn-font-size-sm:      $font-size-sm !default;\n\n$input-btn-padding-y-lg:      .5rem !default;\n$input-btn-padding-x-lg:      1rem !default;\n$input-btn-font-size-lg:      $font-size-lg !default;\n\n$input-btn-border-width:      $border-width !default;\n// scss-docs-end input-btn-variables\n\n\n// Buttons\n//\n// For each of Bootstrap's buttons, define text, background, and border color.\n\n// scss-docs-start btn-variables\n$btn-padding-y:               $input-btn-padding-y !default;\n$btn-padding-x:               $input-btn-padding-x !default;\n$btn-font-family:             $input-btn-font-family !default;\n$btn-font-size:               $input-btn-font-size !default;\n$btn-line-height:             $input-btn-line-height !default;\n$btn-white-space:             null !default; // Set to `nowrap` to prevent text wrapping\n\n$btn-padding-y-sm:            $input-btn-padding-y-sm !default;\n$btn-padding-x-sm:            $input-btn-padding-x-sm !default;\n$btn-font-size-sm:            $input-btn-font-size-sm !default;\n\n$btn-padding-y-lg:            $input-btn-padding-y-lg !default;\n$btn-padding-x-lg:            $input-btn-padding-x-lg !default;\n$btn-font-size-lg:            $input-btn-font-size-lg !default;\n\n$btn-border-width:            $input-btn-border-width !default;\n\n$btn-font-weight:             $font-weight-normal !default;\n$btn-box-shadow:              inset 0 1px 0 rgba($white, .15), 0 1px 1px rgba($black, .075) !default;\n$btn-focus-width:             $input-btn-focus-width !default;\n$btn-focus-box-shadow:        $input-btn-focus-box-shadow !default;\n$btn-disabled-opacity:        .65 !default;\n$btn-active-box-shadow:       inset 0 3px 5px rgba($black, .125) !default;\n\n$btn-link-color:              var(--#{$prefix}link-color) !default;\n$btn-link-hover-color:        var(--#{$prefix}link-hover-color) !default;\n$btn-link-disabled-color:     $gray-600 !default;\n\n// Allows for customizing button radius independently from global border radius\n$btn-border-radius:           $border-radius !default;\n$btn-border-radius-sm:        $border-radius-sm !default;\n$btn-border-radius-lg:        $border-radius-lg !default;\n\n$btn-transition:              color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n\n$btn-hover-bg-shade-amount:       15% !default;\n$btn-hover-bg-tint-amount:        15% !default;\n$btn-hover-border-shade-amount:   20% !default;\n$btn-hover-border-tint-amount:    10% !default;\n$btn-active-bg-shade-amount:      20% !default;\n$btn-active-bg-tint-amount:       20% !default;\n$btn-active-border-shade-amount:  25% !default;\n$btn-active-border-tint-amount:   10% !default;\n// scss-docs-end btn-variables\n\n\n// Forms\n\n// scss-docs-start form-text-variables\n$form-text-margin-top:                  .25rem !default;\n$form-text-font-size:                   $small-font-size !default;\n$form-text-font-style:                  null !default;\n$form-text-font-weight:                 null !default;\n$form-text-color:                       $text-muted !default;\n// scss-docs-end form-text-variables\n\n// scss-docs-start form-label-variables\n$form-label-margin-bottom:              .5rem !default;\n$form-label-font-size:                  null !default;\n$form-label-font-style:                 null !default;\n$form-label-font-weight:                null !default;\n$form-label-color:                      null !default;\n// scss-docs-end form-label-variables\n\n// scss-docs-start form-input-variables\n$input-padding-y:                       $input-btn-padding-y !default;\n$input-padding-x:                       $input-btn-padding-x !default;\n$input-font-family:                     $input-btn-font-family !default;\n$input-font-size:                       $input-btn-font-size !default;\n$input-font-weight:                     $font-weight-base !default;\n$input-line-height:                     $input-btn-line-height !default;\n\n$input-padding-y-sm:                    $input-btn-padding-y-sm !default;\n$input-padding-x-sm:                    $input-btn-padding-x-sm !default;\n$input-font-size-sm:                    $input-btn-font-size-sm !default;\n\n$input-padding-y-lg:                    $input-btn-padding-y-lg !default;\n$input-padding-x-lg:                    $input-btn-padding-x-lg !default;\n$input-font-size-lg:                    $input-btn-font-size-lg !default;\n\n$input-bg:                              $body-bg !default;\n$input-disabled-color:                  null !default;\n$input-disabled-bg:                     $gray-200 !default;\n$input-disabled-border-color:           null !default;\n\n$input-color:                           $body-color !default;\n$input-border-color:                    $gray-400 !default;\n$input-border-width:                    $input-btn-border-width !default;\n$input-box-shadow:                      $box-shadow-inset !default;\n\n$input-border-radius:                   $border-radius !default;\n$input-border-radius-sm:                $border-radius-sm !default;\n$input-border-radius-lg:                $border-radius-lg !default;\n\n$input-focus-bg:                        $input-bg !default;\n$input-focus-border-color:              tint-color($component-active-bg, 50%) !default;\n$input-focus-color:                     $input-color !default;\n$input-focus-width:                     $input-btn-focus-width !default;\n$input-focus-box-shadow:                $input-btn-focus-box-shadow !default;\n\n$input-placeholder-color:               $gray-600 !default;\n$input-plaintext-color:                 $body-color !default;\n\n$input-height-border:                   $input-border-width * 2 !default;\n\n$input-height-inner:                    add($input-line-height * 1em, $input-padding-y * 2) !default;\n$input-height-inner-half:               add($input-line-height * .5em, $input-padding-y) !default;\n$input-height-inner-quarter:            add($input-line-height * .25em, $input-padding-y * .5) !default;\n\n$input-height:                          add($input-line-height * 1em, add($input-padding-y * 2, $input-height-border, false)) !default;\n$input-height-sm:                       add($input-line-height * 1em, add($input-padding-y-sm * 2, $input-height-border, false)) !default;\n$input-height-lg:                       add($input-line-height * 1em, add($input-padding-y-lg * 2, $input-height-border, false)) !default;\n\n$input-transition:                      border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n\n$form-color-width:                      3rem !default;\n// scss-docs-end form-input-variables\n\n// scss-docs-start form-check-variables\n$form-check-input-width:                  1em !default;\n$form-check-min-height:                   $font-size-base * $line-height-base !default;\n$form-check-padding-start:                $form-check-input-width + .5em !default;\n$form-check-margin-bottom:                .125rem !default;\n$form-check-label-color:                  null !default;\n$form-check-label-cursor:                 null !default;\n$form-check-transition:                   null !default;\n\n$form-check-input-active-filter:          brightness(90%) !default;\n\n$form-check-input-bg:                     $input-bg !default;\n$form-check-input-border:                 1px solid rgba($black, .25) !default;\n$form-check-input-border-radius:          .25em !default;\n$form-check-radio-border-radius:          50% !default;\n$form-check-input-focus-border:           $input-focus-border-color !default;\n$form-check-input-focus-box-shadow:       $input-btn-focus-box-shadow !default;\n\n$form-check-input-checked-color:          $component-active-color !default;\n$form-check-input-checked-bg-color:       $component-active-bg !default;\n$form-check-input-checked-border-color:   $form-check-input-checked-bg-color !default;\n$form-check-input-checked-bg-image:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'><path fill='none' stroke='#{$form-check-input-checked-color}' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='m6 10 3 3 6-6'/></svg>\") !default;\n$form-check-radio-checked-bg-image:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='2' fill='#{$form-check-input-checked-color}'/></svg>\") !default;\n\n$form-check-input-indeterminate-color:          $component-active-color !default;\n$form-check-input-indeterminate-bg-color:       $component-active-bg !default;\n$form-check-input-indeterminate-border-color:   $form-check-input-indeterminate-bg-color !default;\n$form-check-input-indeterminate-bg-image:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'><path fill='none' stroke='#{$form-check-input-indeterminate-color}' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10h8'/></svg>\") !default;\n\n$form-check-input-disabled-opacity:        .5 !default;\n$form-check-label-disabled-opacity:        $form-check-input-disabled-opacity !default;\n$form-check-btn-check-disabled-opacity:    $btn-disabled-opacity !default;\n\n$form-check-inline-margin-end:    1rem !default;\n// scss-docs-end form-check-variables\n\n// scss-docs-start form-switch-variables\n$form-switch-color:               rgba($black, .25) !default;\n$form-switch-width:               2em !default;\n$form-switch-padding-start:       $form-switch-width + .5em !default;\n$form-switch-bg-image:            url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-color}'/></svg>\") !default;\n$form-switch-border-radius:       $form-switch-width !default;\n$form-switch-transition:          background-position .15s ease-in-out !default;\n\n$form-switch-focus-color:         $input-focus-border-color !default;\n$form-switch-focus-bg-image:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-focus-color}'/></svg>\") !default;\n\n$form-switch-checked-color:       $component-active-color !default;\n$form-switch-checked-bg-image:    url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-checked-color}'/></svg>\") !default;\n$form-switch-checked-bg-position: right center !default;\n// scss-docs-end form-switch-variables\n\n// scss-docs-start input-group-variables\n$input-group-addon-padding-y:           $input-padding-y !default;\n$input-group-addon-padding-x:           $input-padding-x !default;\n$input-group-addon-font-weight:         $input-font-weight !default;\n$input-group-addon-color:               $input-color !default;\n$input-group-addon-bg:                  $gray-200 !default;\n$input-group-addon-border-color:        $input-border-color !default;\n// scss-docs-end input-group-variables\n\n// scss-docs-start form-select-variables\n$form-select-padding-y:             $input-padding-y !default;\n$form-select-padding-x:             $input-padding-x !default;\n$form-select-font-family:           $input-font-family !default;\n$form-select-font-size:             $input-font-size !default;\n$form-select-indicator-padding:     $form-select-padding-x * 3 !default; // Extra padding for background-image\n$form-select-font-weight:           $input-font-weight !default;\n$form-select-line-height:           $input-line-height !default;\n$form-select-color:                 $input-color !default;\n$form-select-bg:                    $input-bg !default;\n$form-select-disabled-color:        null !default;\n$form-select-disabled-bg:           $gray-200 !default;\n$form-select-disabled-border-color: $input-disabled-border-color !default;\n$form-select-bg-position:           right $form-select-padding-x center !default;\n$form-select-bg-size:               16px 12px !default; // In pixels because image dimensions\n$form-select-indicator-color:       $gray-800 !default;\n$form-select-indicator:             url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'><path fill='none' stroke='#{$form-select-indicator-color}' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/></svg>\") !default;\n\n$form-select-feedback-icon-padding-end: $form-select-padding-x * 2.5 + $form-select-indicator-padding !default;\n$form-select-feedback-icon-position:    center right $form-select-indicator-padding !default;\n$form-select-feedback-icon-size:        $input-height-inner-half $input-height-inner-half !default;\n\n$form-select-border-width:        $input-border-width !default;\n$form-select-border-color:        $input-border-color !default;\n$form-select-border-radius:       $input-border-radius !default;\n$form-select-box-shadow:          $box-shadow-inset !default;\n\n$form-select-focus-border-color:  $input-focus-border-color !default;\n$form-select-focus-width:         $input-focus-width !default;\n$form-select-focus-box-shadow:    0 0 0 $form-select-focus-width $input-btn-focus-color !default;\n\n$form-select-padding-y-sm:        $input-padding-y-sm !default;\n$form-select-padding-x-sm:        $input-padding-x-sm !default;\n$form-select-font-size-sm:        $input-font-size-sm !default;\n$form-select-border-radius-sm:    $input-border-radius-sm !default;\n\n$form-select-padding-y-lg:        $input-padding-y-lg !default;\n$form-select-padding-x-lg:        $input-padding-x-lg !default;\n$form-select-font-size-lg:        $input-font-size-lg !default;\n$form-select-border-radius-lg:    $input-border-radius-lg !default;\n\n$form-select-transition:          $input-transition !default;\n// scss-docs-end form-select-variables\n\n// scss-docs-start form-range-variables\n$form-range-track-width:          100% !default;\n$form-range-track-height:         .5rem !default;\n$form-range-track-cursor:         pointer !default;\n$form-range-track-bg:             $gray-300 !default;\n$form-range-track-border-radius:  1rem !default;\n$form-range-track-box-shadow:     $box-shadow-inset !default;\n\n$form-range-thumb-width:                   1rem !default;\n$form-range-thumb-height:                  $form-range-thumb-width !default;\n$form-range-thumb-bg:                      $component-active-bg !default;\n$form-range-thumb-border:                  0 !default;\n$form-range-thumb-border-radius:           1rem !default;\n$form-range-thumb-box-shadow:              0 .1rem .25rem rgba($black, .1) !default;\n$form-range-thumb-focus-box-shadow:        0 0 0 1px $body-bg, $input-focus-box-shadow !default;\n$form-range-thumb-focus-box-shadow-width:  $input-focus-width !default; // For focus box shadow issue in Edge\n$form-range-thumb-active-bg:               tint-color($component-active-bg, 70%) !default;\n$form-range-thumb-disabled-bg:             $gray-500 !default;\n$form-range-thumb-transition:              background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n// scss-docs-end form-range-variables\n\n// scss-docs-start form-file-variables\n$form-file-button-color:          $input-color !default;\n$form-file-button-bg:             $input-group-addon-bg !default;\n$form-file-button-hover-bg:       shade-color($form-file-button-bg, 5%) !default;\n// scss-docs-end form-file-variables\n\n// scss-docs-start form-floating-variables\n$form-floating-height:            add(3.5rem, $input-height-border) !default;\n$form-floating-line-height:       1.25 !default;\n$form-floating-padding-x:         $input-padding-x !default;\n$form-floating-padding-y:         1rem !default;\n$form-floating-input-padding-t:   1.625rem !default;\n$form-floating-input-padding-b:   .625rem !default;\n$form-floating-label-opacity:     .65 !default;\n$form-floating-label-transform:   scale(.85) translateY(-.5rem) translateX(.15rem) !default;\n$form-floating-transition:        opacity .1s ease-in-out, transform .1s ease-in-out !default;\n// scss-docs-end form-floating-variables\n\n// Form validation\n\n// scss-docs-start form-feedback-variables\n$form-feedback-margin-top:          $form-text-margin-top !default;\n$form-feedback-font-size:           $form-text-font-size !default;\n$form-feedback-font-style:          $form-text-font-style !default;\n$form-feedback-valid-color:         $success !default;\n$form-feedback-invalid-color:       $danger !default;\n\n$form-feedback-icon-valid-color:    $form-feedback-valid-color !default;\n$form-feedback-icon-valid:          url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'><path fill='#{$form-feedback-icon-valid-color}' d='M2.3 6.73.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/></svg>\") !default;\n$form-feedback-icon-invalid-color:  $form-feedback-invalid-color !default;\n$form-feedback-icon-invalid:        url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='#{$form-feedback-icon-invalid-color}'><circle cx='6' cy='6' r='4.5'/><path stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/><circle cx='6' cy='8.2' r='.6' fill='#{$form-feedback-icon-invalid-color}' stroke='none'/></svg>\") !default;\n// scss-docs-end form-feedback-variables\n\n// scss-docs-start form-validation-states\n$form-validation-states: (\n  \"valid\": (\n    \"color\": $form-feedback-valid-color,\n    \"icon\": $form-feedback-icon-valid\n  ),\n  \"invalid\": (\n    \"color\": $form-feedback-invalid-color,\n    \"icon\": $form-feedback-icon-invalid\n  )\n) !default;\n// scss-docs-end form-validation-states\n\n// Z-index master list\n//\n// Warning: Avoid customizing these values. They're used for a bird's eye view\n// of components dependent on the z-axis and are designed to all work together.\n\n// scss-docs-start zindex-stack\n$zindex-dropdown:                   1000 !default;\n$zindex-sticky:                     1020 !default;\n$zindex-fixed:                      1030 !default;\n$zindex-offcanvas-backdrop:         1040 !default;\n$zindex-offcanvas:                  1045 !default;\n$zindex-modal-backdrop:             1050 !default;\n$zindex-modal:                      1055 !default;\n$zindex-popover:                    1070 !default;\n$zindex-tooltip:                    1080 !default;\n$zindex-toast:                      1090 !default;\n// scss-docs-end zindex-stack\n\n\n// Navs\n\n// scss-docs-start nav-variables\n$nav-link-padding-y:                .5rem !default;\n$nav-link-padding-x:                1rem !default;\n$nav-link-font-size:                null !default;\n$nav-link-font-weight:              null !default;\n$nav-link-color:                    var(--#{$prefix}link-color) !default;\n$nav-link-hover-color:              var(--#{$prefix}link-hover-color) !default;\n$nav-link-transition:               color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out !default;\n$nav-link-disabled-color:           $gray-600 !default;\n\n$nav-tabs-border-color:             $gray-300 !default;\n$nav-tabs-border-width:             $border-width !default;\n$nav-tabs-border-radius:            $border-radius !default;\n$nav-tabs-link-hover-border-color:  $gray-200 $gray-200 $nav-tabs-border-color !default;\n$nav-tabs-link-active-color:        $gray-700 !default;\n$nav-tabs-link-active-bg:           $body-bg !default;\n$nav-tabs-link-active-border-color: $gray-300 $gray-300 $nav-tabs-link-active-bg !default;\n\n$nav-pills-border-radius:           $border-radius !default;\n$nav-pills-link-active-color:       $component-active-color !default;\n$nav-pills-link-active-bg:          $component-active-bg !default;\n// scss-docs-end nav-variables\n\n\n// Navbar\n\n// scss-docs-start navbar-variables\n$navbar-padding-y:                  $spacer * .5 !default;\n$navbar-padding-x:                  null !default;\n\n$navbar-nav-link-padding-x:         .5rem !default;\n\n$navbar-brand-font-size:            $font-size-lg !default;\n// Compute the navbar-brand padding-y so the navbar-brand will have the same height as navbar-text and nav-link\n$nav-link-height:                   $font-size-base * $line-height-base + $nav-link-padding-y * 2 !default;\n$navbar-brand-height:               $navbar-brand-font-size * $line-height-base !default;\n$navbar-brand-padding-y:            ($nav-link-height - $navbar-brand-height) * .5 !default;\n$navbar-brand-margin-end:           1rem !default;\n\n$navbar-toggler-padding-y:          .25rem !default;\n$navbar-toggler-padding-x:          .75rem !default;\n$navbar-toggler-font-size:          $font-size-lg !default;\n$navbar-toggler-border-radius:      $btn-border-radius !default;\n$navbar-toggler-focus-width:        $btn-focus-width !default;\n$navbar-toggler-transition:         box-shadow .15s ease-in-out !default;\n// scss-docs-end navbar-variables\n\n// scss-docs-start navbar-theme-variables\n$navbar-dark-color:                 rgba($white, .55) !default;\n$navbar-dark-hover-color:           rgba($white, .75) !default;\n$navbar-dark-active-color:          $white !default;\n$navbar-dark-disabled-color:        rgba($white, .25) !default;\n$navbar-dark-toggler-icon-bg:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'><path stroke='#{$navbar-dark-color}' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/></svg>\") !default;\n$navbar-dark-toggler-border-color:  rgba($white, .1) !default;\n\n$navbar-light-color:                rgba($black, .55) !default;\n$navbar-light-hover-color:          rgba($black, .7) !default;\n$navbar-light-active-color:         rgba($black, .9) !default;\n$navbar-light-disabled-color:       rgba($black, .3) !default;\n$navbar-light-toggler-icon-bg:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'><path stroke='#{$navbar-light-color}' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/></svg>\") !default;\n$navbar-light-toggler-border-color: rgba($black, .1) !default;\n\n$navbar-light-brand-color:                $navbar-light-active-color !default;\n$navbar-light-brand-hover-color:          $navbar-light-active-color !default;\n$navbar-dark-brand-color:                 $navbar-dark-active-color !default;\n$navbar-dark-brand-hover-color:           $navbar-dark-active-color !default;\n// scss-docs-end navbar-theme-variables\n\n\n// Dropdowns\n//\n// Dropdown menu container and contents.\n\n// scss-docs-start dropdown-variables\n$dropdown-min-width:                10rem !default;\n$dropdown-padding-x:                0 !default;\n$dropdown-padding-y:                .5rem !default;\n$dropdown-spacer:                   .125rem !default;\n$dropdown-font-size:                $font-size-base !default;\n$dropdown-color:                    $body-color !default;\n$dropdown-bg:                       $white !default;\n$dropdown-border-color:             var(--#{$prefix}border-color-translucent) !default;\n$dropdown-border-radius:            $border-radius !default;\n$dropdown-border-width:             $border-width !default;\n$dropdown-inner-border-radius:      subtract($dropdown-border-radius, $dropdown-border-width) !default;\n$dropdown-divider-bg:               $dropdown-border-color !default;\n$dropdown-divider-margin-y:         $spacer * .5 !default;\n$dropdown-box-shadow:               $box-shadow !default;\n\n$dropdown-link-color:               $gray-900 !default;\n$dropdown-link-hover-color:         shade-color($dropdown-link-color, 10%) !default;\n$dropdown-link-hover-bg:            $gray-200 !default;\n\n$dropdown-link-active-color:        $component-active-color !default;\n$dropdown-link-active-bg:           $component-active-bg !default;\n\n$dropdown-link-disabled-color:      $gray-500 !default;\n\n$dropdown-item-padding-y:           $spacer * .25 !default;\n$dropdown-item-padding-x:           $spacer !default;\n\n$dropdown-header-color:             $gray-600 !default;\n$dropdown-header-padding-x:         $dropdown-item-padding-x !default;\n$dropdown-header-padding-y:         $dropdown-padding-y !default;\n// fusv-disable\n$dropdown-header-padding:           $dropdown-header-padding-y $dropdown-header-padding-x !default; // Deprecated in v5.2.0\n// fusv-enable\n// scss-docs-end dropdown-variables\n\n// scss-docs-start dropdown-dark-variables\n$dropdown-dark-color:               $gray-300 !default;\n$dropdown-dark-bg:                  $gray-800 !default;\n$dropdown-dark-border-color:        $dropdown-border-color !default;\n$dropdown-dark-divider-bg:          $dropdown-divider-bg !default;\n$dropdown-dark-box-shadow:          null !default;\n$dropdown-dark-link-color:          $dropdown-dark-color !default;\n$dropdown-dark-link-hover-color:    $white !default;\n$dropdown-dark-link-hover-bg:       rgba($white, .15) !default;\n$dropdown-dark-link-active-color:   $dropdown-link-active-color !default;\n$dropdown-dark-link-active-bg:      $dropdown-link-active-bg !default;\n$dropdown-dark-link-disabled-color: $gray-500 !default;\n$dropdown-dark-header-color:        $gray-500 !default;\n// scss-docs-end dropdown-dark-variables\n\n\n// Pagination\n\n// scss-docs-start pagination-variables\n$pagination-padding-y:              .375rem !default;\n$pagination-padding-x:              .75rem !default;\n$pagination-padding-y-sm:           .25rem !default;\n$pagination-padding-x-sm:           .5rem !default;\n$pagination-padding-y-lg:           .75rem !default;\n$pagination-padding-x-lg:           1.5rem !default;\n\n$pagination-font-size:              $font-size-base !default;\n\n$pagination-color:                  var(--#{$prefix}link-color) !default;\n$pagination-bg:                     $white !default;\n$pagination-border-radius:          $border-radius !default;\n$pagination-border-width:           $border-width !default;\n$pagination-margin-start:           ($pagination-border-width * -1) !default;\n$pagination-border-color:           $gray-300 !default;\n\n$pagination-focus-color:            var(--#{$prefix}link-hover-color) !default;\n$pagination-focus-bg:               $gray-200 !default;\n$pagination-focus-box-shadow:       $input-btn-focus-box-shadow !default;\n$pagination-focus-outline:          0 !default;\n\n$pagination-hover-color:            var(--#{$prefix}link-hover-color) !default;\n$pagination-hover-bg:               $gray-200 !default;\n$pagination-hover-border-color:     $gray-300 !default;\n\n$pagination-active-color:           $component-active-color !default;\n$pagination-active-bg:              $component-active-bg !default;\n$pagination-active-border-color:    $pagination-active-bg !default;\n\n$pagination-disabled-color:         $gray-600 !default;\n$pagination-disabled-bg:            $white !default;\n$pagination-disabled-border-color:  $gray-300 !default;\n\n$pagination-transition:              color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n\n$pagination-border-radius-sm:       $border-radius-sm !default;\n$pagination-border-radius-lg:       $border-radius-lg !default;\n// scss-docs-end pagination-variables\n\n\n// Placeholders\n\n// scss-docs-start placeholders\n$placeholder-opacity-max:           .5 !default;\n$placeholder-opacity-min:           .2 !default;\n// scss-docs-end placeholders\n\n// Cards\n\n// scss-docs-start card-variables\n$card-spacer-y:                     $spacer !default;\n$card-spacer-x:                     $spacer !default;\n$card-title-spacer-y:               $spacer * .5 !default;\n$card-border-width:                 $border-width !default;\n$card-border-color:                 var(--#{$prefix}border-color-translucent) !default;\n$card-border-radius:                $border-radius !default;\n$card-box-shadow:                   null !default;\n$card-inner-border-radius:          subtract($card-border-radius, $card-border-width) !default;\n$card-cap-padding-y:                $card-spacer-y * .5 !default;\n$card-cap-padding-x:                $card-spacer-x !default;\n$card-cap-bg:                       rgba($black, .03) !default;\n$card-cap-color:                    null !default;\n$card-height:                       null !default;\n$card-color:                        null !default;\n$card-bg:                           $white !default;\n$card-img-overlay-padding:          $spacer !default;\n$card-group-margin:                 $grid-gutter-width * .5 !default;\n// scss-docs-end card-variables\n\n// Accordion\n\n// scss-docs-start accordion-variables\n$accordion-padding-y:                     1rem !default;\n$accordion-padding-x:                     1.25rem !default;\n$accordion-color:                         var(--#{$prefix}body-color) !default;\n$accordion-bg:                            $body-bg !default;\n$accordion-border-width:                  $border-width !default;\n$accordion-border-color:                  var(--#{$prefix}border-color) !default;\n$accordion-border-radius:                 $border-radius !default;\n$accordion-inner-border-radius:           subtract($accordion-border-radius, $accordion-border-width) !default;\n\n$accordion-body-padding-y:                $accordion-padding-y !default;\n$accordion-body-padding-x:                $accordion-padding-x !default;\n\n$accordion-button-padding-y:              $accordion-padding-y !default;\n$accordion-button-padding-x:              $accordion-padding-x !default;\n$accordion-button-color:                  $accordion-color !default;\n$accordion-button-bg:                     var(--#{$prefix}accordion-bg) !default;\n$accordion-transition:                    $btn-transition, border-radius .15s ease !default;\n$accordion-button-active-bg:              tint-color($component-active-bg, 90%) !default;\n$accordion-button-active-color:           shade-color($primary, 10%) !default;\n\n$accordion-button-focus-border-color:     $input-focus-border-color !default;\n$accordion-button-focus-box-shadow:       $btn-focus-box-shadow !default;\n\n$accordion-icon-width:                    1.25rem !default;\n$accordion-icon-color:                    $accordion-button-color !default;\n$accordion-icon-active-color:             $accordion-button-active-color !default;\n$accordion-icon-transition:               transform .2s ease-in-out !default;\n$accordion-icon-transform:                rotate(-180deg) !default;\n\n$accordion-button-icon:         url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$accordion-icon-color}'><path fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/></svg>\") !default;\n$accordion-button-active-icon:  url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$accordion-icon-active-color}'><path fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/></svg>\") !default;\n// scss-docs-end accordion-variables\n\n// Tooltips\n\n// scss-docs-start tooltip-variables\n$tooltip-font-size:                 $font-size-sm !default;\n$tooltip-max-width:                 200px !default;\n$tooltip-color:                     $white !default;\n$tooltip-bg:                        $black !default;\n$tooltip-border-radius:             $border-radius !default;\n$tooltip-opacity:                   .9 !default;\n$tooltip-padding-y:                 $spacer * .25 !default;\n$tooltip-padding-x:                 $spacer * .5 !default;\n$tooltip-margin:                    null !default; // TODO: remove this in v6\n\n$tooltip-arrow-width:               .8rem !default;\n$tooltip-arrow-height:              .4rem !default;\n// fusv-disable\n$tooltip-arrow-color:               null !default; // Deprecated in Bootstrap 5.2.0 for CSS variables\n// fusv-enable\n// scss-docs-end tooltip-variables\n\n// Form tooltips must come after regular tooltips\n// scss-docs-start tooltip-feedback-variables\n$form-feedback-tooltip-padding-y:     $tooltip-padding-y !default;\n$form-feedback-tooltip-padding-x:     $tooltip-padding-x !default;\n$form-feedback-tooltip-font-size:     $tooltip-font-size !default;\n$form-feedback-tooltip-line-height:   null !default;\n$form-feedback-tooltip-opacity:       $tooltip-opacity !default;\n$form-feedback-tooltip-border-radius: $tooltip-border-radius !default;\n// scss-docs-end tooltip-feedback-variables\n\n\n// Popovers\n\n// scss-docs-start popover-variables\n$popover-font-size:                 $font-size-sm !default;\n$popover-bg:                        $white !default;\n$popover-max-width:                 276px !default;\n$popover-border-width:              $border-width !default;\n$popover-border-color:              var(--#{$prefix}border-color-translucent) !default;\n$popover-border-radius:             $border-radius-lg !default;\n$popover-inner-border-radius:       subtract($popover-border-radius, $popover-border-width) !default;\n$popover-box-shadow:                $box-shadow !default;\n\n$popover-header-font-size:          $font-size-base !default;\n$popover-header-bg:                 shade-color($popover-bg, 6%) !default;\n$popover-header-color:              var(--#{$prefix}heading-color) !default;\n$popover-header-padding-y:          .5rem !default;\n$popover-header-padding-x:          $spacer !default;\n\n$popover-body-color:                $body-color !default;\n$popover-body-padding-y:            $spacer !default;\n$popover-body-padding-x:            $spacer !default;\n\n$popover-arrow-width:               1rem !default;\n$popover-arrow-height:              .5rem !default;\n// scss-docs-end popover-variables\n\n// fusv-disable\n// Deprecated in Bootstrap 5.2.0 for CSS variables\n$popover-arrow-color:               $popover-bg !default;\n$popover-arrow-outer-color:         var(--#{$prefix}border-color-translucent) !default;\n// fusv-enable\n\n\n// Toasts\n\n// scss-docs-start toast-variables\n$toast-max-width:                   350px !default;\n$toast-padding-x:                   .75rem !default;\n$toast-padding-y:                   .5rem !default;\n$toast-font-size:                   .875rem !default;\n$toast-color:                       null !default;\n$toast-background-color:            rgba($white, .85) !default;\n$toast-border-width:                $border-width !default;\n$toast-border-color:                var(--#{$prefix}border-color-translucent) !default;\n$toast-border-radius:               $border-radius !default;\n$toast-box-shadow:                  $box-shadow !default;\n$toast-spacing:                     $container-padding-x !default;\n\n$toast-header-color:                $gray-600 !default;\n$toast-header-background-color:     rgba($white, .85) !default;\n$toast-header-border-color:         rgba($black, .05) !default;\n// scss-docs-end toast-variables\n\n\n// Badges\n\n// scss-docs-start badge-variables\n$badge-font-size:                   .75em !default;\n$badge-font-weight:                 $font-weight-bold !default;\n$badge-color:                       $white !default;\n$badge-padding-y:                   .35em !default;\n$badge-padding-x:                   .65em !default;\n$badge-border-radius:               $border-radius !default;\n// scss-docs-end badge-variables\n\n\n// Modals\n\n// scss-docs-start modal-variables\n$modal-inner-padding:               $spacer !default;\n\n$modal-footer-margin-between:       .5rem !default;\n\n$modal-dialog-margin:               .5rem !default;\n$modal-dialog-margin-y-sm-up:       1.75rem !default;\n\n$modal-title-line-height:           $line-height-base !default;\n\n$modal-content-color:               null !default;\n$modal-content-bg:                  $white !default;\n$modal-content-border-color:        var(--#{$prefix}border-color-translucent) !default;\n$modal-content-border-width:        $border-width !default;\n$modal-content-border-radius:       $border-radius-lg !default;\n$modal-content-inner-border-radius: subtract($modal-content-border-radius, $modal-content-border-width) !default;\n$modal-content-box-shadow-xs:       $box-shadow-sm !default;\n$modal-content-box-shadow-sm-up:    $box-shadow !default;\n\n$modal-backdrop-bg:                 $black !default;\n$modal-backdrop-opacity:            .5 !default;\n\n$modal-header-border-color:         var(--#{$prefix}border-color) !default;\n$modal-header-border-width:         $modal-content-border-width !default;\n$modal-header-padding-y:            $modal-inner-padding !default;\n$modal-header-padding-x:            $modal-inner-padding !default;\n$modal-header-padding:              $modal-header-padding-y $modal-header-padding-x !default; // Keep this for backwards compatibility\n\n$modal-footer-bg:                   null !default;\n$modal-footer-border-color:         $modal-header-border-color !default;\n$modal-footer-border-width:         $modal-header-border-width !default;\n\n$modal-sm:                          300px !default;\n$modal-md:                          500px !default;\n$modal-lg:                          800px !default;\n$modal-xl:                          1140px !default;\n\n$modal-fade-transform:              translate(0, -50px) !default;\n$modal-show-transform:              none !default;\n$modal-transition:                  transform .3s ease-out !default;\n$modal-scale-transform:             scale(1.02) !default;\n// scss-docs-end modal-variables\n\n\n// Alerts\n//\n// Define alert colors, border radius, and padding.\n\n// scss-docs-start alert-variables\n$alert-padding-y:               $spacer !default;\n$alert-padding-x:               $spacer !default;\n$alert-margin-bottom:           1rem !default;\n$alert-border-radius:           $border-radius !default;\n$alert-link-font-weight:        $font-weight-bold !default;\n$alert-border-width:            $border-width !default;\n$alert-bg-scale:                -80% !default;\n$alert-border-scale:            -70% !default;\n$alert-color-scale:             40% !default;\n$alert-dismissible-padding-r:   $alert-padding-x * 3 !default; // 3x covers width of x plus default padding on either side\n// scss-docs-end alert-variables\n\n\n// Progress bars\n\n// scss-docs-start progress-variables\n$progress-height:                   1rem !default;\n$progress-font-size:                $font-size-base * .75 !default;\n$progress-bg:                       $gray-200 !default;\n$progress-border-radius:            $border-radius !default;\n$progress-box-shadow:               $box-shadow-inset !default;\n$progress-bar-color:                $white !default;\n$progress-bar-bg:                   $primary !default;\n$progress-bar-animation-timing:     1s linear infinite !default;\n$progress-bar-transition:           width .6s ease !default;\n// scss-docs-end progress-variables\n\n\n// List group\n\n// scss-docs-start list-group-variables\n$list-group-color:                  $gray-900 !default;\n$list-group-bg:                     $white !default;\n$list-group-border-color:           rgba($black, .125) !default;\n$list-group-border-width:           $border-width !default;\n$list-group-border-radius:          $border-radius !default;\n\n$list-group-item-padding-y:         $spacer * .5 !default;\n$list-group-item-padding-x:         $spacer !default;\n$list-group-item-bg-scale:          -80% !default;\n$list-group-item-color-scale:       40% !default;\n\n$list-group-hover-bg:               $gray-100 !default;\n$list-group-active-color:           $component-active-color !default;\n$list-group-active-bg:              $component-active-bg !default;\n$list-group-active-border-color:    $list-group-active-bg !default;\n\n$list-group-disabled-color:         $gray-600 !default;\n$list-group-disabled-bg:            $list-group-bg !default;\n\n$list-group-action-color:           $gray-700 !default;\n$list-group-action-hover-color:     $list-group-action-color !default;\n\n$list-group-action-active-color:    $body-color !default;\n$list-group-action-active-bg:       $gray-200 !default;\n// scss-docs-end list-group-variables\n\n\n// Image thumbnails\n\n// scss-docs-start thumbnail-variables\n$thumbnail-padding:                 .25rem !default;\n$thumbnail-bg:                      $body-bg !default;\n$thumbnail-border-width:            $border-width !default;\n$thumbnail-border-color:            var(--#{$prefix}border-color) !default;\n$thumbnail-border-radius:           $border-radius !default;\n$thumbnail-box-shadow:              $box-shadow-sm !default;\n// scss-docs-end thumbnail-variables\n\n\n// Figures\n\n// scss-docs-start figure-variables\n$figure-caption-font-size:          $small-font-size !default;\n$figure-caption-color:              $gray-600 !default;\n// scss-docs-end figure-variables\n\n\n// Breadcrumbs\n\n// scss-docs-start breadcrumb-variables\n$breadcrumb-font-size:              null !default;\n$breadcrumb-padding-y:              0 !default;\n$breadcrumb-padding-x:              0 !default;\n$breadcrumb-item-padding-x:         .5rem !default;\n$breadcrumb-margin-bottom:          1rem !default;\n$breadcrumb-bg:                     null !default;\n$breadcrumb-divider-color:          $gray-600 !default;\n$breadcrumb-active-color:           $gray-600 !default;\n$breadcrumb-divider:                quote(\"/\") !default;\n$breadcrumb-divider-flipped:        $breadcrumb-divider !default;\n$breadcrumb-border-radius:          null !default;\n// scss-docs-end breadcrumb-variables\n\n// Carousel\n\n// scss-docs-start carousel-variables\n$carousel-control-color:             $white !default;\n$carousel-control-width:             15% !default;\n$carousel-control-opacity:           .5 !default;\n$carousel-control-hover-opacity:     .9 !default;\n$carousel-control-transition:        opacity .15s ease !default;\n\n$carousel-indicator-width:           30px !default;\n$carousel-indicator-height:          3px !default;\n$carousel-indicator-hit-area-height: 10px !default;\n$carousel-indicator-spacer:          3px !default;\n$carousel-indicator-opacity:         .5 !default;\n$carousel-indicator-active-bg:       $white !default;\n$carousel-indicator-active-opacity:  1 !default;\n$carousel-indicator-transition:      opacity .6s ease !default;\n\n$carousel-caption-width:             70% !default;\n$carousel-caption-color:             $white !default;\n$carousel-caption-padding-y:         1.25rem !default;\n$carousel-caption-spacer:            1.25rem !default;\n\n$carousel-control-icon-width:        2rem !default;\n\n$carousel-control-prev-icon-bg:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$carousel-control-color}'><path d='M11.354 1.646a.5.5 0 0 1 0 .708L5.707 8l5.647 5.646a.5.5 0 0 1-.708.708l-6-6a.5.5 0 0 1 0-.708l6-6a.5.5 0 0 1 .708 0z'/></svg>\") !default;\n$carousel-control-next-icon-bg:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$carousel-control-color}'><path d='M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z'/></svg>\") !default;\n\n$carousel-transition-duration:       .6s !default;\n$carousel-transition:                transform $carousel-transition-duration ease-in-out !default; // Define transform transition first if using multiple transitions (e.g., `transform 2s ease, opacity .5s ease-out`)\n\n$carousel-dark-indicator-active-bg:  $black !default;\n$carousel-dark-caption-color:        $black !default;\n$carousel-dark-control-icon-filter:  invert(1) grayscale(100) !default;\n// scss-docs-end carousel-variables\n\n\n// Spinners\n\n// scss-docs-start spinner-variables\n$spinner-width:           2rem !default;\n$spinner-height:          $spinner-width !default;\n$spinner-vertical-align:  -.125em !default;\n$spinner-border-width:    .25em !default;\n$spinner-animation-speed: .75s !default;\n\n$spinner-width-sm:        1rem !default;\n$spinner-height-sm:       $spinner-width-sm !default;\n$spinner-border-width-sm: .2em !default;\n// scss-docs-end spinner-variables\n\n\n// Close\n\n// scss-docs-start close-variables\n$btn-close-width:            1em !default;\n$btn-close-height:           $btn-close-width !default;\n$btn-close-padding-x:        .25em !default;\n$btn-close-padding-y:        $btn-close-padding-x !default;\n$btn-close-color:            $black !default;\n$btn-close-bg:               url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$btn-close-color}'><path d='M.293.293a1 1 0 0 1 1.414 0L8 6.586 14.293.293a1 1 0 1 1 1.414 1.414L9.414 8l6.293 6.293a1 1 0 0 1-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 0 1-1.414-1.414L6.586 8 .293 1.707a1 1 0 0 1 0-1.414z'/></svg>\") !default;\n$btn-close-focus-shadow:     $input-btn-focus-box-shadow !default;\n$btn-close-opacity:          .5 !default;\n$btn-close-hover-opacity:    .75 !default;\n$btn-close-focus-opacity:    1 !default;\n$btn-close-disabled-opacity: .25 !default;\n$btn-close-white-filter:     invert(1) grayscale(100%) brightness(200%) !default;\n// scss-docs-end close-variables\n\n\n// Offcanvas\n\n// scss-docs-start offcanvas-variables\n$offcanvas-padding-y:               $modal-inner-padding !default;\n$offcanvas-padding-x:               $modal-inner-padding !default;\n$offcanvas-horizontal-width:        400px !default;\n$offcanvas-vertical-height:         30vh !default;\n$offcanvas-transition-duration:     .3s !default;\n$offcanvas-border-color:            $modal-content-border-color !default;\n$offcanvas-border-width:            $modal-content-border-width !default;\n$offcanvas-title-line-height:       $modal-title-line-height !default;\n$offcanvas-bg-color:                $modal-content-bg !default;\n$offcanvas-color:                   $modal-content-color !default;\n$offcanvas-box-shadow:              $modal-content-box-shadow-xs !default;\n$offcanvas-backdrop-bg:             $modal-backdrop-bg !default;\n$offcanvas-backdrop-opacity:        $modal-backdrop-opacity !default;\n// scss-docs-end offcanvas-variables\n\n// Code\n\n$code-font-size:                    $small-font-size !default;\n$code-color:                        $pink !default;\n\n$kbd-padding-y:                     .1875rem !default;\n$kbd-padding-x:                     .375rem !default;\n$kbd-font-size:                     $code-font-size !default;\n$kbd-color:                         var(--#{$prefix}body-bg) !default;\n$kbd-bg:                            var(--#{$prefix}body-color) !default;\n$nested-kbd-font-weight:            null !default; // Deprecated in v5.2.0, removing in v6\n\n$pre-color:                         null !default;\n", "// Dark theme variables\n\n\n\n// Bootstrap base colors\n$white:        #fff !default;\n$gray-100:     #f8f9fa !default;\n$gray-200:     #e9ecef !default;\n$gray-300:     #dee2e6 !default;\n$gray-400:     #cbd1db !default;\n$gray-500:     #aeb7c5 !default;\n$gray-600:     #7987a1 !default;\n$gray-700:     #41516c !default;\n$gray-800:     #212a3a !default;\n$gray-900:     #060c17 !default;\n$black:        #000 !default;\n$text-muted:   $gray-600 !default;\n\n\n// Bootstrap custom colors\n$blue:    #0d6efd !default;\n$indigo:  #6610f2 !default;\n$purple:  #6f42c1 !default;\n$pink:    #d63384 !default;\n$red:     #dc3545 !default;\n$orange:  #fd7e14 !default;\n$yellow:  #ffc107 !default;\n$green:   #198754 !default;\n$teal:    #20c997 !default;\n$cyan:    #0dcaf0 !default;\n\n\n\n// --- Bootstrap Theme Colors --- //\n//\n$primary:         #6571ff !default;\n$secondary:       $gray-600 !default;\n$success:         #05a34a !default;\n$info:            #66d1d1 !default;\n$warning:         #fbbc06 !default;\n$danger:          #ff3366 !default;\n$light:           $gray-200 !default;\n$dark:            $gray-900 !default;\n//\n// --- Bootstrap Theme Colors --- //\n\n\n\n// Social colors\n$social-colors: (\n  \"facebook\":        #3b5998,\n  \"twitter\":         #1da1f2,\n  \"google\":          #dc4e41,\n  \"youtube\":         #f00,\n  \"vimeo\":           #1ab7ea,\n  \"dribbble\":        #ea4c89,\n  \"github\":          #181717,\n  \"instagram\":       #e4405f,\n  \"pinterest\":       #bd081c,\n  \"flickr\":          #0063dc,\n  \"bitbucket\":       #0052cc,\n  \"linkedin\":        #0077b5\n) !default;\n\n\n// The contrast ratio to reach against white, to determine if color changes from \"light\" to \"dark\". Acceptable values for WCAG 2.0 are 3, 4.5 and 7.\n// See https://www.w3.org/TR/WCAG20/#visual-audio-contrast-contrast\n$min-contrast-ratio:   3 !default;\n\n\n// Options\n//\n// Quickly modify global styling by enabling or disabling optional features.\n$enable-gradients:            false !default;\n$enable-negative-margins:     true !default;\n\n\n// Spacing\n//\n// Control the default styling of most Bootstrap elements by modifying these\n// variables. Mostly focused on spacing.\n// You can add more entries to the $spacers map, should you need more variation.\n$spacer: 1rem !default;\n$spacers: (\n  0: 0,\n  1: $spacer * .25,\n  2: $spacer * .5,\n  3: $spacer,\n  4: $spacer * 1.5,\n  5: $spacer * 3,\n  6: ($spacer * 4.5),\n  7: ($spacer * 6)\n);\n\n\n// Position\n//\n// Define the edge positioning anchors of the position utilities.\n$position-values: (\n  0: 0,\n  10: 10%,\n  20: 20%,\n  25: 25%,\n  30: 30%,\n  40: 40%,\n  50: 50%,\n  60: 60%,\n  70: 70%,\n  75: 75%,\n  80: 80%,\n  90: 90%,\n  100: 100%\n) !default;\n\n\n// Body\n//\n// Settings for the `<body>` element.\n$body-bg:                      #070d19 !default;\n$body-color:                   #d0d6e1 !default;\n\n\n// Links\n//\n// Style anchor elements.\n$link-decoration:              none !default;\n// $link-hover-decoration:        underline !default;\n\n\n// Paragraphs\n//\n// Style p element.\n$paragraph-margin-bottom:      0 !default;\n\n\n// Grid columns\n$grid-gutter-width:           1.5rem !default;\n\n\n// Components\n//\n// Define common padding and border radius sizes and more.\n\n// Border\n$border-color:                #172340 !default;\n\n// Border Radiues\n$border-radius:\t\t\t\t\t.25rem !default;\n\n\n$action-transition-duration: 0.2s;\n$action-transition-timing-function: ease;\n\n\n// Typography\n//\n// Font, line-height, and color for body text, headings, and more.\n\n// Font family\n$font-family-sans-serif:           \"Roboto\", Helvetica, sans-serif !default;\n\n$font-size-base:              \t\t 0.875rem !default;   // 14px\n$font-size-lg:               \t \t\t 1rem !default;       // 16px\n$font-size-sm:                \t\t 0.812rem !default;   // 13px\n\n$font-weight-lighter:              lighter !default;\n$font-weight-light:                300 !default;\n$font-weight-normal:               400 !default;\n$font-weight-bold:                 500 !default;\n$font-weight-bolder:               700 !default;\n$font-weight-boldest:              900 !default;\n\n$font-weight-base:                 $font-weight-normal !default;\n\n\n// Heading sizes\n$h1-font-size:                     2.5rem !default;  \n$h2-font-size:                     2rem !default;   \n$h3-font-size:                     1.5rem !default;  \n$h4-font-size:                     1.25rem !default;  \n$h5-font-size:                     1rem !default;  \n$h6-font-size:                     $font-size-base !default; \n\n$headings-margin-bottom:      0 !default;\n$headings-font-weight:        500 !default;\n\n\n$hr-opacity:                  .1 !default;\n\n\n// Tables\n//\n// Customizes the `.table` component with basic values, each used across all table variations.\n\n$table-cell-padding-y:                 .85rem !default;\n$table-cell-padding-x:                 .85rem !default;\n$table-cell-padding-y-sm:              .55rem !default;\n$table-cell-padding-x-sm:              .55rem !default;\n\n$table-striped-bg:                     #080e1b !default;\n$table-active-bg:                      #050913 !default;\n$table-hover-bg:                       #080e1b !default;\n\n$table-group-separator-color:          $border-color;\n\n\n// Buttons + Forms\n//\n// Shared variables that are reassigned to `$input-` and `$btn-` specific variables.\n\n$input-btn-padding-y:                   .469rem !default;\n$input-btn-padding-x:                   .8rem !default;\n\n$input-btn-focus-box-shadow:            none !default;\n$input-btn-focus-width:                 0 !default;\n\n$input-placeholder-color:               $gray-500 !default;\n\n$input-btn-padding-y-xs:                .313rem !default;\n$input-btn-padding-x-xs:                .8rem !default;\n$input-btn-font-size-xs:                .75rem !default;\n\n$input-btn-padding-y-sm:                .391rem !default;\n$input-btn-padding-x-sm:                .8rem !default;\n$input-btn-font-size-sm:                $font-size-sm !default;\n\n$input-btn-padding-y-lg:                .5rem !default;\n$input-btn-padding-x-lg:                .8rem !default;\n$input-btn-font-size-lg:                $font-size-lg !default;\n\n\n// Buttons\n//\n// For each of Bootstrap's buttons, define text, background, and border color.\n\n$btn-font-weight:           $font-weight-normal !default;\n\n$btn-padding-y-xs:          $input-btn-padding-y-xs !default;\n$btn-padding-x-xs:          $input-btn-padding-x-xs !default;\n$btn-font-size-xs:          $input-btn-font-size-xs !default;\n\n// Allows for customizing button radius independently from global border radius\n$btn-border-radius:                     $border-radius !default;\n$btn-border-radius-sm:                  $border-radius !default;\n$btn-border-radius-lg:                  $border-radius !default;\n\n\n// Forms\n\n$input-padding-y-xs:                    $input-btn-padding-y-xs !default;\n$input-padding-x-xs:                    $input-btn-padding-x-xs !default;\n$input-font-size-xs:                    $input-btn-font-size-xs !default;\n\n$input-bg:                              #0c1427 !default;\n$input-disabled-bg:                     lighten($input-bg, 5%) !default; // dark\n$input-border-color:                    $border-color !default;\n$input-focus-border-color:              lighten($border-color, 10%) !default;\n\n$input-border-radius:                   $btn-border-radius !default;\n$input-border-radius-sm:                $btn-border-radius-sm !default;\n$input-border-radius-lg:                $btn-border-radius-lg !default;\n\n// form-check\n$form-check-input-width:                1.3em !default;\n$form-check-input-border:               1px solid lighten($input-border-color, 20%) !default; // dark\n$form-check-input-focus-border:         lighten($input-border-color, 25%) !default; // dark\n$form-check-input-border-radius:        .15em !default;\n\n// Form switch\n$form-switch-color:                     lighten($input-bg, 60%) !default; // dark\n$form-switch-focus-color:               lighten($input-bg, 65%) !default; // dark\n\n// Input-group\n$input-group-addon-padding-x:           .563rem !default;\n$input-group-addon-bg:                  lighten($input-bg, 5%) !default;\n\n// Range\n$form-range-track-bg:                   lighten($input-bg, 5%) !default; // dark\n\n\n\n// Navs\n$nav-tabs-border-color:               $border-color !default; // dark\n$nav-tabs-link-bg:                    transparent; // custom variable\n$nav-tabs-link-border-color:          $border-color $border-color $nav-tabs-border-color; // custom variable\n$nav-tabs-link-active-bg:             $input-bg !default;\n$nav-tabs-link-active-border-color:   $border-color $border-color $nav-tabs-link-active-bg !default; //dark\n\n\n\n// Dropdowns\n$dropdown-font-size:               13px !default; // dark\n$dropdown-color:                   $text-muted !default; // dark\n$dropdown-bg:                      $gray-900 !default; // dark\n$dropdown-border-color:            $border-color;\n$dropdown-box-shadow:              0 0 10px 0 #060b15;\n$dropdown-link-color:              $body-color !default; // dark\n\n\n\n// Pagination\n$pagination-padding-y:              .469rem !default;\n$pagination-padding-x:              1rem !default;\n$pagination-padding-y-sm:           .391rem !default;\n$pagination-padding-x-sm:           .75rem !default;\n$pagination-padding-y-lg:           .5rem !default;\n$pagination-padding-x-lg:           1.1rem !default;\n\n$pagination-color:                  $primary !default;\n$pagination-bg:                     $input-bg !default; // dark\n$pagination-border-color:           $border-color !default; // dark\n\n$pagination-focus-bg:               lighten($input-bg, 10%) !default; // dark\n\n$pagination-hover-bg:               lighten($input-bg, 10%); // dark\n$pagination-hover-border-color:     lighten($input-bg, 10%); // dark\n\n$pagination-active-bg:              $primary !default;\n$pagination-active-border-color:    $primary !default;\n\n$pagination-disabled-color:         $gray-700 !default; // dark\n$pagination-disabled-bg:            $input-bg !default; // dark\n$pagination-disabled-border-color:  $border-color !default; // dark\n\n\n\n// Cards\n$card-box-shadow:                   3px 0 10px 0 #060b15; // custom variable\n$card-spacer-y:                     1.5rem !default;\n$card-spacer-x:                     1.5rem !default;\n$card-title-spacer-y:               .875rem !default;\n$card-border-color:                 #172340 !default;\n$card-cap-padding-y:                .875rem !default;\n$card-cap-padding-x:                $card-spacer-x !default;\n$card-cap-bg:                       #0a1122 !default;\n$card-bg:                           #0c1427 !default; // dark\n\n\n\n// Accordion\n$accordion-bg:                            $input-bg !default;\n$accordion-border-color:                  $border-color !default;\n$accordion-icon-width:                    .875rem !default;\n$accordion-button-active-bg:              lighten($input-bg, 5%) !default; // dark\n\n\n\n// Popovers\n$popover-bg:                        $input-bg !default; // dark\n$popover-border-color:              $border-color !default;\n$popover-header-bg:                 lighten($input-bg, 5%) !default;\n\n\n\n// Badges\n$badge-font-size:           .8em !default;\n$badge-font-weight:         $font-weight-bold !default;\n\n\n\n// Modals\n$modal-content-bg:              $input-bg !default; //dark\n$modal-content-border-color:    $border-color !default;\n$modal-fade-transform:          scale(.8) !default;\n$modal-transition:              transform .4s ease !default;\n// Modals\n\n\n\n// Progress bars\n$progress-bg:                       lighten($input-bg, 5%) !default; // dark\n\n\n\n// List group\n$list-group-color:                  $body-color!default; // dark\n$list-group-bg:                     $input-bg !default; // dark\n$list-group-border-color:           $border-color !default; // dark\n\n$list-group-item-padding-y:         .75rem !default;\n$list-group-item-padding-x:         1.25rem !default;\n\n$list-group-hover-bg:               lighten($input-bg, 5%); // dark\n$list-group-action-color:           $gray-400 !default; // dark\n\n\n\n// Close\n$btn-close-width:            .8em !default;\n$btn-close-color:            $text-muted !default;\n", "// stylelint-disable property-disallowed-list\n// Single side border-radius\n\n// Helper function to replace negative values with 0\n@function valid-radius($radius) {\n  $return: ();\n  @each $value in $radius {\n    @if type-of($value) == number {\n      $return: append($return, max($value, 0));\n    } @else {\n      $return: append($return, $value);\n    }\n  }\n  @return $return;\n}\n\n// scss-docs-start border-radius-mixins\n@mixin border-radius($radius: $border-radius, $fallback-border-radius: false) {\n  @if $enable-rounded {\n    border-radius: valid-radius($radius);\n  }\n  @else if $fallback-border-radius != false {\n    border-radius: $fallback-border-radius;\n  }\n}\n\n@mixin border-top-radius($radius: $border-radius) {\n  @if $enable-rounded {\n    border-top-left-radius: valid-radius($radius);\n    border-top-right-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-end-radius($radius: $border-radius) {\n  @if $enable-rounded {\n    border-top-right-radius: valid-radius($radius);\n    border-bottom-right-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-bottom-radius($radius: $border-radius) {\n  @if $enable-rounded {\n    border-bottom-right-radius: valid-radius($radius);\n    border-bottom-left-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-start-radius($radius: $border-radius) {\n  @if $enable-rounded {\n    border-top-left-radius: valid-radius($radius);\n    border-bottom-left-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-top-start-radius($radius: $border-radius) {\n  @if $enable-rounded {\n    border-top-left-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-top-end-radius($radius: $border-radius) {\n  @if $enable-rounded {\n    border-top-right-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-bottom-end-radius($radius: $border-radius) {\n  @if $enable-rounded {\n    border-bottom-right-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-bottom-start-radius($radius: $border-radius) {\n  @if $enable-rounded {\n    border-bottom-left-radius: valid-radius($radius);\n  }\n}\n// scss-docs-end border-radius-mixins\n", "//\n// Headings\n//\n.h1 {\n  @extend h1;\n}\n\n.h2 {\n  @extend h2;\n}\n\n.h3 {\n  @extend h3;\n}\n\n.h4 {\n  @extend h4;\n}\n\n.h5 {\n  @extend h5;\n}\n\n.h6 {\n  @extend h6;\n}\n\n\n.lead {\n  @include font-size($lead-font-size);\n  font-weight: $lead-font-weight;\n}\n\n// Type display classes\n@each $display, $font-size in $display-font-sizes {\n  .display-#{$display} {\n    @include font-size($font-size);\n    font-family: $display-font-family;\n    font-style: $display-font-style;\n    font-weight: $display-font-weight;\n    line-height: $display-line-height;\n  }\n}\n\n//\n// Emphasis\n//\n.small {\n  @extend small;\n}\n\n.mark {\n  @extend mark;\n}\n\n//\n// Lists\n//\n\n.list-unstyled {\n  @include list-unstyled();\n}\n\n// Inline turns list items into inline-block\n.list-inline {\n  @include list-unstyled();\n}\n.list-inline-item {\n  display: inline-block;\n\n  &:not(:last-child) {\n    margin-right: $list-inline-padding;\n  }\n}\n\n\n//\n// Misc\n//\n\n// Builds on `abbr`\n.initialism {\n  @include font-size($initialism-font-size);\n  text-transform: uppercase;\n}\n\n// Blockquotes\n.blockquote {\n  margin-bottom: $blockquote-margin-y;\n  @include font-size($blockquote-font-size);\n\n  > :last-child {\n    margin-bottom: 0;\n  }\n}\n\n.blockquote-footer {\n  margin-top: -$blockquote-margin-y;\n  margin-bottom: $blockquote-margin-y;\n  @include font-size($blockquote-footer-font-size);\n  color: $blockquote-footer-color;\n\n  &::before {\n    content: \"\\2014\\00A0\"; // em dash, nbsp\n  }\n}\n", "// Lists\n\n// Unstyled keeps list items block level, just removes default browser padding and list-style\n@mixin list-unstyled {\n  padding-left: 0;\n  list-style: none;\n}\n", "// Responsive images (ensure images don't scale beyond their parents)\n//\n// This is purposefully opt-in via an explicit class rather than being the default for all `<img>`s.\n// We previously tried the \"images are responsive by default\" approach in Bootstrap v2,\n// and abandoned it in Bootstrap v3 because it breaks lots of third-party widgets (including Google Maps)\n// which weren't expecting the images within themselves to be involuntarily resized.\n// See also https://github.com/twbs/bootstrap/issues/18178\n.img-fluid {\n  @include img-fluid();\n}\n\n\n// Image thumbnails\n.img-thumbnail {\n  padding: $thumbnail-padding;\n  background-color: $thumbnail-bg;\n  border: $thumbnail-border-width solid $thumbnail-border-color;\n  @include border-radius($thumbnail-border-radius);\n  @include box-shadow($thumbnail-box-shadow);\n\n  // Keep them at most 100% wide\n  @include img-fluid();\n}\n\n//\n// Figures\n//\n\n.figure {\n  // Ensures the caption's text aligns with the image.\n  display: inline-block;\n}\n\n.figure-img {\n  margin-bottom: $spacer * .5;\n  line-height: 1;\n}\n\n.figure-caption {\n  @include font-size($figure-caption-font-size);\n  color: $figure-caption-color;\n}\n", "// Image Mixins\n// - Responsive image\n// - Retina image\n\n\n// Responsive image\n//\n// Keep images from scaling beyond the width of their parents.\n\n@mixin img-fluid {\n  // Part 1: Set a maximum relative to the parent\n  max-width: 100%;\n  // Part 2: Override the height to auto, otherwise images will be stretched\n  // when setting a width and height attribute on the img element.\n  height: auto;\n}\n", "// Container widths\n//\n// Set the container width, and override it for fixed navbars in media queries.\n\n@if $enable-container-classes {\n  // Single container class with breakpoint max-widths\n  .container,\n  // 100% wide container at all breakpoints\n  .container-fluid {\n    @include make-container();\n  }\n\n  // Responsive containers that are 100% wide until a breakpoint\n  @each $breakpoint, $container-max-width in $container-max-widths {\n    .container-#{$breakpoint} {\n      @extend .container-fluid;\n    }\n\n    @include media-breakpoint-up($breakpoint, $grid-breakpoints) {\n      %responsive-container-#{$breakpoint} {\n        max-width: $container-max-width;\n      }\n\n      // Extend each breakpoint which is smaller or equal to the current breakpoint\n      $extend-breakpoint: true;\n\n      @each $name, $width in $grid-breakpoints {\n        @if ($extend-breakpoint) {\n          .container#{breakpoint-infix($name, $grid-breakpoints)} {\n            @extend %responsive-container-#{$breakpoint};\n          }\n\n          // Once the current breakpoint is reached, stop extending\n          @if ($breakpoint == $name) {\n            $extend-breakpoint: false;\n          }\n        }\n      }\n    }\n  }\n}\n", "// Container mixins\n\n@mixin make-container($gutter: $container-padding-x) {\n  --#{$prefix}gutter-x: #{$gutter};\n  --#{$prefix}gutter-y: 0;\n  width: 100%;\n  padding-right: calc(var(--#{$prefix}gutter-x) * .5); // stylelint-disable-line function-disallowed-list\n  padding-left: calc(var(--#{$prefix}gutter-x) * .5); // stylelint-disable-line function-disallowed-list\n  margin-right: auto;\n  margin-left: auto;\n}\n", "// Breakpoint viewport sizes and media queries.\n//\n// Breakpoints are defined as a map of (name: minimum width), order from small to large:\n//\n//    (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px)\n//\n// The map defined in the `$grid-breakpoints` global variable is used as the `$breakpoints` argument by default.\n\n// Name of the next breakpoint, or null for the last breakpoint.\n//\n//    >> breakpoint-next(sm)\n//    md\n//    >> breakpoint-next(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\n//    md\n//    >> breakpoint-next(sm, $breakpoint-names: (xs sm md lg xl xxl))\n//    md\n@function breakpoint-next($name, $breakpoints: $grid-breakpoints, $breakpoint-names: map-keys($breakpoints)) {\n  $n: index($breakpoint-names, $name);\n  @if not $n {\n    @error \"breakpoint `#{$name}` not found in `#{$breakpoints}`\";\n  }\n  @return if($n < length($breakpoint-names), nth($breakpoint-names, $n + 1), null);\n}\n\n// Minimum breakpoint width. Null for the smallest (first) breakpoint.\n//\n//    >> breakpoint-min(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\n//    576px\n@function breakpoint-min($name, $breakpoints: $grid-breakpoints) {\n  $min: map-get($breakpoints, $name);\n  @return if($min != 0, $min, null);\n}\n\n// Maximum breakpoint width.\n// The maximum value is reduced by 0.02px to work around the limitations of\n// `min-` and `max-` prefixes and viewports with fractional widths.\n// See https://www.w3.org/TR/mediaqueries-4/#mq-min-max\n// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\n// See https://bugs.webkit.org/show_bug.cgi?id=178261\n//\n//    >> breakpoint-max(md, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\n//    767.98px\n@function breakpoint-max($name, $breakpoints: $grid-breakpoints) {\n  $max: map-get($breakpoints, $name);\n  @return if($max and $max > 0, $max - .02, null);\n}\n\n// Returns a blank string if smallest breakpoint, otherwise returns the name with a dash in front.\n// Useful for making responsive utilities.\n//\n//    >> breakpoint-infix(xs, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\n//    \"\"  (Returns a blank string)\n//    >> breakpoint-infix(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\n//    \"-sm\"\n@function breakpoint-infix($name, $breakpoints: $grid-breakpoints) {\n  @return if(breakpoint-min($name, $breakpoints) == null, \"\", \"-#{$name}\");\n}\n\n// Media of at least the minimum breakpoint width. No query for the smallest breakpoint.\n// Makes the @content apply to the given breakpoint and wider.\n@mixin media-breakpoint-up($name, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  @if $min {\n    @media (min-width: $min) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media of at most the maximum breakpoint width. No query for the largest breakpoint.\n// Makes the @content apply to the given breakpoint and narrower.\n@mixin media-breakpoint-down($name, $breakpoints: $grid-breakpoints) {\n  $max: breakpoint-max($name, $breakpoints);\n  @if $max {\n    @media (max-width: $max) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media that spans multiple breakpoint widths.\n// Makes the @content apply between the min and max breakpoints\n@mixin media-breakpoint-between($lower, $upper, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($lower, $breakpoints);\n  $max: breakpoint-max($upper, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($lower, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($upper, $breakpoints) {\n      @content;\n    }\n  }\n}\n\n// Media between the breakpoint's minimum and maximum widths.\n// No minimum for the smallest breakpoint, and no maximum for the largest one.\n// Makes the @content apply only to the given breakpoint, not viewports any wider or narrower.\n@mixin media-breakpoint-only($name, $breakpoints: $grid-breakpoints) {\n  $min:  breakpoint-min($name, $breakpoints);\n  $next: breakpoint-next($name, $breakpoints);\n  $max:  breakpoint-max($next, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($name, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($next, $breakpoints) {\n      @content;\n    }\n  }\n}\n", "// Row\n//\n// Rows contain your columns.\n\n@if $enable-grid-classes {\n  .row {\n    @include make-row();\n\n    > * {\n      @include make-col-ready();\n    }\n  }\n}\n\n@if $enable-cssgrid {\n  .grid {\n    display: grid;\n    grid-template-rows: repeat(var(--#{$prefix}rows, 1), 1fr);\n    grid-template-columns: repeat(var(--#{$prefix}columns, #{$grid-columns}), 1fr);\n    gap: var(--#{$prefix}gap, #{$grid-gutter-width});\n\n    @include make-cssgrid();\n  }\n}\n\n\n// Columns\n//\n// Common styles for small and large grid columns\n\n@if $enable-grid-classes {\n  @include make-grid-columns();\n}\n", "// Grid system\n//\n// Generate semantic grid columns with these mixins.\n\n@mixin make-row($gutter: $grid-gutter-width) {\n  --#{$prefix}gutter-x: #{$gutter};\n  --#{$prefix}gutter-y: 0;\n  display: flex;\n  flex-wrap: wrap;\n  // TODO: Revisit calc order after https://github.com/react-bootstrap/react-bootstrap/issues/6039 is fixed\n  margin-top: calc(-1 * var(--#{$prefix}gutter-y)); // stylelint-disable-line function-disallowed-list\n  margin-right: calc(-.5 * var(--#{$prefix}gutter-x)); // stylelint-disable-line function-disallowed-list\n  margin-left: calc(-.5 * var(--#{$prefix}gutter-x)); // stylelint-disable-line function-disallowed-list\n}\n\n@mixin make-col-ready() {\n  // Add box sizing if only the grid is loaded\n  box-sizing: if(variable-exists(include-column-box-sizing) and $include-column-box-sizing, border-box, null);\n  // Prevent columns from becoming too narrow when at smaller grid tiers by\n  // always setting `width: 100%;`. This works because we set the width\n  // later on to override this initial width.\n  flex-shrink: 0;\n  width: 100%;\n  max-width: 100%; // Prevent `.col-auto`, `.col` (& responsive variants) from breaking out the grid\n  padding-right: calc(var(--#{$prefix}gutter-x) * .5); // stylelint-disable-line function-disallowed-list\n  padding-left: calc(var(--#{$prefix}gutter-x) * .5); // stylelint-disable-line function-disallowed-list\n  margin-top: var(--#{$prefix}gutter-y);\n}\n\n@mixin make-col($size: false, $columns: $grid-columns) {\n  @if $size {\n    flex: 0 0 auto;\n    width: percentage(divide($size, $columns));\n\n  } @else {\n    flex: 1 1 0;\n    max-width: 100%;\n  }\n}\n\n@mixin make-col-auto() {\n  flex: 0 0 auto;\n  width: auto;\n}\n\n@mixin make-col-offset($size, $columns: $grid-columns) {\n  $num: divide($size, $columns);\n  margin-left: if($num == 0, 0, percentage($num));\n}\n\n// Row columns\n//\n// Specify on a parent element(e.g., .row) to force immediate children into NN\n// number of columns. Supports wrapping to new lines, but does not do a Masonry\n// style grid.\n@mixin row-cols($count) {\n  > * {\n    flex: 0 0 auto;\n    width: divide(100%, $count);\n  }\n}\n\n// Framework grid generation\n//\n// Used only by Bootstrap to generate the correct number of grid classes given\n// any value of `$grid-columns`.\n\n@mixin make-grid-columns($columns: $grid-columns, $gutter: $grid-gutter-width, $breakpoints: $grid-breakpoints) {\n  @each $breakpoint in map-keys($breakpoints) {\n    $infix: breakpoint-infix($breakpoint, $breakpoints);\n\n    @include media-breakpoint-up($breakpoint, $breakpoints) {\n      // Provide basic `.col-{bp}` classes for equal-width flexbox columns\n      .col#{$infix} {\n        flex: 1 0 0%; // Flexbugs #4: https://github.com/philipwalton/flexbugs#flexbug-4\n      }\n\n      .row-cols#{$infix}-auto > * {\n        @include make-col-auto();\n      }\n\n      @if $grid-row-columns > 0 {\n        @for $i from 1 through $grid-row-columns {\n          .row-cols#{$infix}-#{$i} {\n            @include row-cols($i);\n          }\n        }\n      }\n\n      .col#{$infix}-auto {\n        @include make-col-auto();\n      }\n\n      @if $columns > 0 {\n        @for $i from 1 through $columns {\n          .col#{$infix}-#{$i} {\n            @include make-col($i, $columns);\n          }\n        }\n\n        // `$columns - 1` because offsetting by the width of an entire row isn't possible\n        @for $i from 0 through ($columns - 1) {\n          @if not ($infix == \"\" and $i == 0) { // Avoid emitting useless .offset-0\n            .offset#{$infix}-#{$i} {\n              @include make-col-offset($i, $columns);\n            }\n          }\n        }\n      }\n\n      // Gutters\n      //\n      // Make use of `.g-*`, `.gx-*` or `.gy-*` utilities to change spacing between the columns.\n      @each $key, $value in $gutters {\n        .g#{$infix}-#{$key},\n        .gx#{$infix}-#{$key} {\n          --#{$prefix}gutter-x: #{$value};\n        }\n\n        .g#{$infix}-#{$key},\n        .gy#{$infix}-#{$key} {\n          --#{$prefix}gutter-y: #{$value};\n        }\n      }\n    }\n  }\n}\n\n@mixin make-cssgrid($columns: $grid-columns, $breakpoints: $grid-breakpoints) {\n  @each $breakpoint in map-keys($breakpoints) {\n    $infix: breakpoint-infix($breakpoint, $breakpoints);\n\n    @include media-breakpoint-up($breakpoint, $breakpoints) {\n      @if $columns > 0 {\n        @for $i from 1 through $columns {\n          .g-col#{$infix}-#{$i} {\n            grid-column: auto / span $i;\n          }\n        }\n\n        // Start with `1` because `0` is and invalid value.\n        // Ends with `$columns - 1` because offsetting by the width of an entire row isn't possible.\n        @for $i from 1 through ($columns - 1) {\n          .g-start#{$infix}-#{$i} {\n            grid-column-start: $i;\n          }\n        }\n      }\n    }\n  }\n}\n", "//\n// Basic Bootstrap table\n//\n\n.table {\n  --#{$prefix}table-color: #{$table-color};\n  --#{$prefix}table-bg: #{$table-bg};\n  --#{$prefix}table-border-color: #{$table-border-color};\n  --#{$prefix}table-accent-bg: #{$table-accent-bg};\n  --#{$prefix}table-striped-color: #{$table-striped-color};\n  --#{$prefix}table-striped-bg: #{$table-striped-bg};\n  --#{$prefix}table-active-color: #{$table-active-color};\n  --#{$prefix}table-active-bg: #{$table-active-bg};\n  --#{$prefix}table-hover-color: #{$table-hover-color};\n  --#{$prefix}table-hover-bg: #{$table-hover-bg};\n\n  width: 100%;\n  margin-bottom: $spacer;\n  color: var(--#{$prefix}table-color);\n  vertical-align: $table-cell-vertical-align;\n  border-color: var(--#{$prefix}table-border-color);\n\n  // Target th & td\n  // We need the child combinator to prevent styles leaking to nested tables which doesn't have a `.table` class.\n  // We use the universal selectors here to simplify the selector (else we would need 6 different selectors).\n  // Another advantage is that this generates less code and makes the selector less specific making it easier to override.\n  // stylelint-disable-next-line selector-max-universal\n  > :not(caption) > * > * {\n    padding: $table-cell-padding-y $table-cell-padding-x;\n    background-color: var(--#{$prefix}table-bg);\n    border-bottom-width: $table-border-width;\n    box-shadow: inset 0 0 0 9999px var(--#{$prefix}table-accent-bg);\n  }\n\n  > tbody {\n    vertical-align: inherit;\n  }\n\n  > thead {\n    vertical-align: bottom;\n  }\n}\n\n.table-group-divider {\n  border-top: ($table-border-width * 2) solid $table-group-separator-color;\n}\n\n//\n// Change placement of captions with a class\n//\n\n.caption-top {\n  caption-side: top;\n}\n\n\n//\n// Condensed table w/ half padding\n//\n\n.table-sm {\n  // stylelint-disable-next-line selector-max-universal\n  > :not(caption) > * > * {\n    padding: $table-cell-padding-y-sm $table-cell-padding-x-sm;\n  }\n}\n\n\n// Border versions\n//\n// Add or remove borders all around the table and between all the columns.\n//\n// When borders are added on all sides of the cells, the corners can render odd when\n// these borders do not have the same color or if they are semi-transparent.\n// Therefor we add top and border bottoms to the `tr`s and left and right borders\n// to the `td`s or `th`s\n\n.table-bordered {\n  > :not(caption) > * {\n    border-width: $table-border-width 0;\n\n    // stylelint-disable-next-line selector-max-universal\n    > * {\n      border-width: 0 $table-border-width;\n    }\n  }\n}\n\n.table-borderless {\n  // stylelint-disable-next-line selector-max-universal\n  > :not(caption) > * > * {\n    border-bottom-width: 0;\n  }\n\n  > :not(:first-child) {\n    border-top-width: 0;\n  }\n}\n\n// Zebra-striping\n//\n// Default zebra-stripe styles (alternating gray and transparent backgrounds)\n\n// For rows\n.table-striped {\n  > tbody > tr:nth-of-type(#{$table-striped-order}) > * {\n    --#{$prefix}table-accent-bg: var(--#{$prefix}table-striped-bg);\n    color: var(--#{$prefix}table-striped-color);\n  }\n}\n\n// For columns\n.table-striped-columns {\n  > :not(caption) > tr > :nth-child(#{$table-striped-columns-order}) {\n    --#{$prefix}table-accent-bg: var(--#{$prefix}table-striped-bg);\n    color: var(--#{$prefix}table-striped-color);\n  }\n}\n\n// Active table\n//\n// The `.table-active` class can be added to highlight rows or cells\n\n.table-active {\n  --#{$prefix}table-accent-bg: var(--#{$prefix}table-active-bg);\n  color: var(--#{$prefix}table-active-color);\n}\n\n// Hover effect\n//\n// Placed here since it has to come after the potential zebra striping\n\n.table-hover {\n  > tbody > tr:hover > * {\n    --#{$prefix}table-accent-bg: var(--#{$prefix}table-hover-bg);\n    color: var(--#{$prefix}table-hover-color);\n  }\n}\n\n\n// Table variants\n//\n// Table variants set the table cell backgrounds, border colors\n// and the colors of the striped, hovered & active tables\n\n@each $color, $value in $table-variants {\n  @include table-variant($color, $value);\n}\n\n// Responsive tables\n//\n// Generate series of `.table-responsive-*` classes for configuring the screen\n// size of where your table will overflow.\n\n@each $breakpoint in map-keys($grid-breakpoints) {\n  $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n  @include media-breakpoint-down($breakpoint) {\n    .table-responsive#{$infix} {\n      overflow-x: auto;\n      -webkit-overflow-scrolling: touch;\n    }\n  }\n}\n", "// scss-docs-start table-variant\n@mixin table-variant($state, $background) {\n  .table-#{$state} {\n    $color: color-contrast(opaque($body-bg, $background));\n    $hover-bg: mix($color, $background, percentage($table-hover-bg-factor));\n    $striped-bg: mix($color, $background, percentage($table-striped-bg-factor));\n    $active-bg: mix($color, $background, percentage($table-active-bg-factor));\n    $border-color: mix($color, $background, percentage($table-border-factor));\n\n    --#{$prefix}table-color: #{$color};\n    --#{$prefix}table-bg: #{$background};\n    --#{$prefix}table-border-color: #{$border-color};\n    --#{$prefix}table-striped-bg: #{$striped-bg};\n    --#{$prefix}table-striped-color: #{color-contrast($striped-bg)};\n    --#{$prefix}table-active-bg: #{$active-bg};\n    --#{$prefix}table-active-color: #{color-contrast($active-bg)};\n    --#{$prefix}table-hover-bg: #{$hover-bg};\n    --#{$prefix}table-hover-color: #{color-contrast($hover-bg)};\n\n    color: var(--#{$prefix}table-color);\n    border-color: var(--#{$prefix}table-border-color);\n  }\n}\n// scss-docs-end table-variant\n", "//\n// Labels\n//\n\n.form-label {\n  margin-bottom: $form-label-margin-bottom;\n  @include font-size($form-label-font-size);\n  font-style: $form-label-font-style;\n  font-weight: $form-label-font-weight;\n  color: $form-label-color;\n}\n\n// For use with horizontal and inline forms, when you need the label (or legend)\n// text to align with the form controls.\n.col-form-label {\n  padding-top: add($input-padding-y, $input-border-width);\n  padding-bottom: add($input-padding-y, $input-border-width);\n  margin-bottom: 0; // Override the `<legend>` default\n  @include font-size(inherit); // Override the `<legend>` default\n  font-style: $form-label-font-style;\n  font-weight: $form-label-font-weight;\n  line-height: $input-line-height;\n  color: $form-label-color;\n}\n\n.col-form-label-lg {\n  padding-top: add($input-padding-y-lg, $input-border-width);\n  padding-bottom: add($input-padding-y-lg, $input-border-width);\n  @include font-size($input-font-size-lg);\n}\n\n.col-form-label-sm {\n  padding-top: add($input-padding-y-sm, $input-border-width);\n  padding-bottom: add($input-padding-y-sm, $input-border-width);\n  @include font-size($input-font-size-sm);\n}\n", "//\n// Form text\n//\n\n.form-text {\n  margin-top: $form-text-margin-top;\n  @include font-size($form-text-font-size);\n  font-style: $form-text-font-style;\n  font-weight: $form-text-font-weight;\n  color: $form-text-color;\n}\n", "//\n// General form controls (plus a few specific high-level interventions)\n//\n\n.form-control {\n  display: block;\n  width: 100%;\n  padding: $input-padding-y $input-padding-x;\n  font-family: $input-font-family;\n  @include font-size($input-font-size);\n  font-weight: $input-font-weight;\n  line-height: $input-line-height;\n  color: $input-color;\n  background-color: $input-bg;\n  background-clip: padding-box;\n  border: $input-border-width solid $input-border-color;\n  appearance: none; // Fix appearance for date inputs in Safari\n\n  // Note: This has no effect on <select>s in some browsers, due to the limited stylability of `<select>`s in CSS.\n  @include border-radius($input-border-radius, 0);\n\n  @include box-shadow($input-box-shadow);\n  @include transition($input-transition);\n\n  &[type=\"file\"] {\n    overflow: hidden; // prevent pseudo element button overlap\n\n    &:not(:disabled):not([readonly]) {\n      cursor: pointer;\n    }\n  }\n\n  // Customize the `:focus` state to imitate native WebKit styles.\n  &:focus {\n    color: $input-focus-color;\n    background-color: $input-focus-bg;\n    border-color: $input-focus-border-color;\n    outline: 0;\n    @if $enable-shadows {\n      @include box-shadow($input-box-shadow, $input-focus-box-shadow);\n    } @else {\n      // Avoid using mixin so we can pass custom focus shadow properly\n      box-shadow: $input-focus-box-shadow;\n    }\n  }\n\n  // Add some height to date inputs on iOS\n  // https://github.com/twbs/bootstrap/issues/23307\n  // TODO: we can remove this workaround once https://bugs.webkit.org/show_bug.cgi?id=198959 is resolved\n  &::-webkit-date-and-time-value {\n    // Multiply line-height by 1em if it has no unit\n    height: if(unit($input-line-height) == \"\", $input-line-height * 1em, $input-line-height);\n  }\n\n  // Placeholder\n  &::placeholder {\n    color: $input-placeholder-color;\n    // Override Firefox's unusual default opacity; see https://github.com/twbs/bootstrap/pull/11526.\n    opacity: 1;\n  }\n\n  // Disabled inputs\n  //\n  // HTML5 says that controls under a fieldset > legend:first-child won't be\n  // disabled if the fieldset is disabled. Due to implementation difficulty, we\n  // don't honor that edge case; we style them as disabled anyway.\n  &:disabled {\n    color: $input-disabled-color;\n    background-color: $input-disabled-bg;\n    border-color: $input-disabled-border-color;\n    // iOS fix for unreadable disabled content; see https://github.com/twbs/bootstrap/issues/11655.\n    opacity: 1;\n  }\n\n  // File input buttons theming\n  &::file-selector-button {\n    padding: $input-padding-y $input-padding-x;\n    margin: (-$input-padding-y) (-$input-padding-x);\n    margin-inline-end: $input-padding-x;\n    color: $form-file-button-color;\n    @include gradient-bg($form-file-button-bg);\n    pointer-events: none;\n    border-color: inherit;\n    border-style: solid;\n    border-width: 0;\n    border-inline-end-width: $input-border-width;\n    border-radius: 0; // stylelint-disable-line property-disallowed-list\n    @include transition($btn-transition);\n  }\n\n  &:hover:not(:disabled):not([readonly])::file-selector-button {\n    background-color: $form-file-button-hover-bg;\n  }\n}\n\n// Readonly controls as plain text\n//\n// Apply class to a readonly input to make it appear like regular plain\n// text (without any border, background color, focus indicator)\n\n.form-control-plaintext {\n  display: block;\n  width: 100%;\n  padding: $input-padding-y 0;\n  margin-bottom: 0; // match inputs if this class comes on inputs with default margins\n  line-height: $input-line-height;\n  color: $input-plaintext-color;\n  background-color: transparent;\n  border: solid transparent;\n  border-width: $input-border-width 0;\n\n  &:focus {\n    outline: 0;\n  }\n\n  &.form-control-sm,\n  &.form-control-lg {\n    padding-right: 0;\n    padding-left: 0;\n  }\n}\n\n// Form control sizing\n//\n// Build on `.form-control` with modifier classes to decrease or increase the\n// height and font-size of form controls.\n//\n// Repeated in `_input_group.scss` to avoid Sass extend issues.\n\n.form-control-sm {\n  min-height: $input-height-sm;\n  padding: $input-padding-y-sm $input-padding-x-sm;\n  @include font-size($input-font-size-sm);\n  @include border-radius($input-border-radius-sm);\n\n  &::file-selector-button {\n    padding: $input-padding-y-sm $input-padding-x-sm;\n    margin: (-$input-padding-y-sm) (-$input-padding-x-sm);\n    margin-inline-end: $input-padding-x-sm;\n  }\n}\n\n.form-control-lg {\n  min-height: $input-height-lg;\n  padding: $input-padding-y-lg $input-padding-x-lg;\n  @include font-size($input-font-size-lg);\n  @include border-radius($input-border-radius-lg);\n\n  &::file-selector-button {\n    padding: $input-padding-y-lg $input-padding-x-lg;\n    margin: (-$input-padding-y-lg) (-$input-padding-x-lg);\n    margin-inline-end: $input-padding-x-lg;\n  }\n}\n\n// Make sure textareas don't shrink too much when resized\n// https://github.com/twbs/bootstrap/pull/29124\n// stylelint-disable selector-no-qualifying-type\ntextarea {\n  &.form-control {\n    min-height: $input-height;\n  }\n\n  &.form-control-sm {\n    min-height: $input-height-sm;\n  }\n\n  &.form-control-lg {\n    min-height: $input-height-lg;\n  }\n}\n// stylelint-enable selector-no-qualifying-type\n\n.form-control-color {\n  width: $form-color-width;\n  height: $input-height;\n  padding: $input-padding-y;\n\n  &:not(:disabled):not([readonly]) {\n    cursor: pointer;\n  }\n\n  &::-moz-color-swatch {\n    border: 0 !important; // stylelint-disable-line declaration-no-important\n    @include border-radius($input-border-radius);\n  }\n\n  &::-webkit-color-swatch {\n    @include border-radius($input-border-radius);\n  }\n\n  &.form-control-sm { height: $input-height-sm; }\n  &.form-control-lg { height: $input-height-lg; }\n}\n", "// stylelint-disable property-disallowed-list\n@mixin transition($transition...) {\n  @if length($transition) == 0 {\n    $transition: $transition-base;\n  }\n\n  @if length($transition) > 1 {\n    @each $value in $transition {\n      @if $value == null or $value == none {\n        @warn \"The keyword 'none' or 'null' must be used as a single argument.\";\n      }\n    }\n  }\n\n  @if $enable-transitions {\n    @if nth($transition, 1) != null {\n      transition: $transition;\n    }\n\n    @if $enable-reduced-motion and nth($transition, 1) != null and nth($transition, 1) != none {\n      @media (prefers-reduced-motion: reduce) {\n        transition: none;\n      }\n    }\n  }\n}\n", "// Gradients\n\n// scss-docs-start gradient-bg-mixin\n@mixin gradient-bg($color: null) {\n  background-color: $color;\n\n  @if $enable-gradients {\n    background-image: var(--#{$prefix}gradient);\n  }\n}\n// scss-docs-end gradient-bg-mixin\n\n// scss-docs-start gradient-mixins\n// Horizontal gradient, from left to right\n//\n// Creates two color stops, start and end, by specifying a color and position for each color stop.\n@mixin gradient-x($start-color: $gray-700, $end-color: $gray-800, $start-percent: 0%, $end-percent: 100%) {\n  background-image: linear-gradient(to right, $start-color $start-percent, $end-color $end-percent);\n}\n\n// Vertical gradient, from top to bottom\n//\n// Creates two color stops, start and end, by specifying a color and position for each color stop.\n@mixin gradient-y($start-color: $gray-700, $end-color: $gray-800, $start-percent: null, $end-percent: null) {\n  background-image: linear-gradient(to bottom, $start-color $start-percent, $end-color $end-percent);\n}\n\n@mixin gradient-directional($start-color: $gray-700, $end-color: $gray-800, $deg: 45deg) {\n  background-image: linear-gradient($deg, $start-color, $end-color);\n}\n\n@mixin gradient-x-three-colors($start-color: $blue, $mid-color: $purple, $color-stop: 50%, $end-color: $red) {\n  background-image: linear-gradient(to right, $start-color, $mid-color $color-stop, $end-color);\n}\n\n@mixin gradient-y-three-colors($start-color: $blue, $mid-color: $purple, $color-stop: 50%, $end-color: $red) {\n  background-image: linear-gradient($start-color, $mid-color $color-stop, $end-color);\n}\n\n@mixin gradient-radial($inner-color: $gray-700, $outer-color: $gray-800) {\n  background-image: radial-gradient(circle, $inner-color, $outer-color);\n}\n\n@mixin gradient-striped($color: rgba($white, .15), $angle: 45deg) {\n  background-image: linear-gradient($angle, $color 25%, transparent 25%, transparent 50%, $color 50%, $color 75%, transparent 75%, transparent);\n}\n// scss-docs-end gradient-mixins\n", "// Select\n//\n// Replaces the browser default select with a custom one, mostly pulled from\n// https://primer.github.io/.\n\n.form-select {\n  display: block;\n  width: 100%;\n  padding: $form-select-padding-y $form-select-indicator-padding $form-select-padding-y $form-select-padding-x;\n  -moz-padding-start: subtract($form-select-padding-x, 3px); // See https://github.com/twbs/bootstrap/issues/32636\n  font-family: $form-select-font-family;\n  @include font-size($form-select-font-size);\n  font-weight: $form-select-font-weight;\n  line-height: $form-select-line-height;\n  color: $form-select-color;\n  background-color: $form-select-bg;\n  background-image: escape-svg($form-select-indicator);\n  background-repeat: no-repeat;\n  background-position: $form-select-bg-position;\n  background-size: $form-select-bg-size;\n  border: $form-select-border-width solid $form-select-border-color;\n  @include border-radius($form-select-border-radius, 0);\n  @include box-shadow($form-select-box-shadow);\n  @include transition($form-select-transition);\n  appearance: none;\n\n  &:focus {\n    border-color: $form-select-focus-border-color;\n    outline: 0;\n    @if $enable-shadows {\n      @include box-shadow($form-select-box-shadow, $form-select-focus-box-shadow);\n    } @else {\n      // Avoid using mixin so we can pass custom focus shadow properly\n      box-shadow: $form-select-focus-box-shadow;\n    }\n  }\n\n  &[multiple],\n  &[size]:not([size=\"1\"]) {\n    padding-right: $form-select-padding-x;\n    background-image: none;\n  }\n\n  &:disabled {\n    color: $form-select-disabled-color;\n    background-color: $form-select-disabled-bg;\n    border-color: $form-select-disabled-border-color;\n  }\n\n  // Remove outline from select box in FF\n  &:-moz-focusring {\n    color: transparent;\n    text-shadow: 0 0 0 $form-select-color;\n  }\n}\n\n.form-select-sm {\n  padding-top: $form-select-padding-y-sm;\n  padding-bottom: $form-select-padding-y-sm;\n  padding-left: $form-select-padding-x-sm;\n  @include font-size($form-select-font-size-sm);\n  @include border-radius($form-select-border-radius-sm);\n}\n\n.form-select-lg {\n  padding-top: $form-select-padding-y-lg;\n  padding-bottom: $form-select-padding-y-lg;\n  padding-left: $form-select-padding-x-lg;\n  @include font-size($form-select-font-size-lg);\n  @include border-radius($form-select-border-radius-lg);\n}\n", "//\n// Check/radio\n//\n\n.form-check {\n  display: block;\n  min-height: $form-check-min-height;\n  padding-left: $form-check-padding-start;\n  margin-bottom: $form-check-margin-bottom;\n\n  .form-check-input {\n    float: left;\n    margin-left: $form-check-padding-start * -1;\n  }\n}\n\n.form-check-reverse {\n  padding-right: $form-check-padding-start;\n  padding-left: 0;\n  text-align: right;\n\n  .form-check-input {\n    float: right;\n    margin-right: $form-check-padding-start * -1;\n    margin-left: 0;\n  }\n}\n\n.form-check-input {\n  width: $form-check-input-width;\n  height: $form-check-input-width;\n  margin-top: ($line-height-base - $form-check-input-width) * .5; // line-height minus check height\n  vertical-align: top;\n  background-color: $form-check-input-bg;\n  background-repeat: no-repeat;\n  background-position: center;\n  background-size: contain;\n  border: $form-check-input-border;\n  appearance: none;\n  print-color-adjust: exact; // Keep themed appearance for print\n  @include transition($form-check-transition);\n\n  &[type=\"checkbox\"] {\n    @include border-radius($form-check-input-border-radius);\n  }\n\n  &[type=\"radio\"] {\n    // stylelint-disable-next-line property-disallowed-list\n    border-radius: $form-check-radio-border-radius;\n  }\n\n  &:active {\n    filter: $form-check-input-active-filter;\n  }\n\n  &:focus {\n    border-color: $form-check-input-focus-border;\n    outline: 0;\n    box-shadow: $form-check-input-focus-box-shadow;\n  }\n\n  &:checked {\n    background-color: $form-check-input-checked-bg-color;\n    border-color: $form-check-input-checked-border-color;\n\n    &[type=\"checkbox\"] {\n      @if $enable-gradients {\n        background-image: escape-svg($form-check-input-checked-bg-image), var(--#{$prefix}gradient);\n      } @else {\n        background-image: escape-svg($form-check-input-checked-bg-image);\n      }\n    }\n\n    &[type=\"radio\"] {\n      @if $enable-gradients {\n        background-image: escape-svg($form-check-radio-checked-bg-image), var(--#{$prefix}gradient);\n      } @else {\n        background-image: escape-svg($form-check-radio-checked-bg-image);\n      }\n    }\n  }\n\n  &[type=\"checkbox\"]:indeterminate {\n    background-color: $form-check-input-indeterminate-bg-color;\n    border-color: $form-check-input-indeterminate-border-color;\n\n    @if $enable-gradients {\n      background-image: escape-svg($form-check-input-indeterminate-bg-image), var(--#{$prefix}gradient);\n    } @else {\n      background-image: escape-svg($form-check-input-indeterminate-bg-image);\n    }\n  }\n\n  &:disabled {\n    pointer-events: none;\n    filter: none;\n    opacity: $form-check-input-disabled-opacity;\n  }\n\n  // Use disabled attribute in addition of :disabled pseudo-class\n  // See: https://github.com/twbs/bootstrap/issues/28247\n  &[disabled],\n  &:disabled {\n    ~ .form-check-label {\n      cursor: default;\n      opacity: $form-check-label-disabled-opacity;\n    }\n  }\n}\n\n.form-check-label {\n  color: $form-check-label-color;\n  cursor: $form-check-label-cursor;\n}\n\n//\n// Switch\n//\n\n.form-switch {\n  padding-left: $form-switch-padding-start;\n\n  .form-check-input {\n    width: $form-switch-width;\n    margin-left: $form-switch-padding-start * -1;\n    background-image: escape-svg($form-switch-bg-image);\n    background-position: left center;\n    @include border-radius($form-switch-border-radius);\n    @include transition($form-switch-transition);\n\n    &:focus {\n      background-image: escape-svg($form-switch-focus-bg-image);\n    }\n\n    &:checked {\n      background-position: $form-switch-checked-bg-position;\n\n      @if $enable-gradients {\n        background-image: escape-svg($form-switch-checked-bg-image), var(--#{$prefix}gradient);\n      } @else {\n        background-image: escape-svg($form-switch-checked-bg-image);\n      }\n    }\n  }\n\n  &.form-check-reverse {\n    padding-right: $form-switch-padding-start;\n    padding-left: 0;\n\n    .form-check-input {\n      margin-right: $form-switch-padding-start * -1;\n      margin-left: 0;\n    }\n  }\n}\n\n.form-check-inline {\n  display: inline-block;\n  margin-right: $form-check-inline-margin-end;\n}\n\n.btn-check {\n  position: absolute;\n  clip: rect(0, 0, 0, 0);\n  pointer-events: none;\n\n  &[disabled],\n  &:disabled {\n    + .btn {\n      pointer-events: none;\n      filter: none;\n      opacity: $form-check-btn-check-disabled-opacity;\n    }\n  }\n}\n", "// Range\n//\n// Style range inputs the same across browsers. Vendor-specific rules for pseudo\n// elements cannot be mixed. As such, there are no shared styles for focus or\n// active states on prefixed selectors.\n\n.form-range {\n  width: 100%;\n  height: add($form-range-thumb-height, $form-range-thumb-focus-box-shadow-width * 2);\n  padding: 0; // Need to reset padding\n  background-color: transparent;\n  appearance: none;\n\n  &:focus {\n    outline: 0;\n\n    // Pseudo-elements must be split across multiple rulesets to have an effect.\n    // No box-shadow() mixin for focus accessibility.\n    &::-webkit-slider-thumb { box-shadow: $form-range-thumb-focus-box-shadow; }\n    &::-moz-range-thumb     { box-shadow: $form-range-thumb-focus-box-shadow; }\n  }\n\n  &::-moz-focus-outer {\n    border: 0;\n  }\n\n  &::-webkit-slider-thumb {\n    width: $form-range-thumb-width;\n    height: $form-range-thumb-height;\n    margin-top: ($form-range-track-height - $form-range-thumb-height) * .5; // Webkit specific\n    @include gradient-bg($form-range-thumb-bg);\n    border: $form-range-thumb-border;\n    @include border-radius($form-range-thumb-border-radius);\n    @include box-shadow($form-range-thumb-box-shadow);\n    @include transition($form-range-thumb-transition);\n    appearance: none;\n\n    &:active {\n      @include gradient-bg($form-range-thumb-active-bg);\n    }\n  }\n\n  &::-webkit-slider-runnable-track {\n    width: $form-range-track-width;\n    height: $form-range-track-height;\n    color: transparent; // Why?\n    cursor: $form-range-track-cursor;\n    background-color: $form-range-track-bg;\n    border-color: transparent;\n    @include border-radius($form-range-track-border-radius);\n    @include box-shadow($form-range-track-box-shadow);\n  }\n\n  &::-moz-range-thumb {\n    width: $form-range-thumb-width;\n    height: $form-range-thumb-height;\n    @include gradient-bg($form-range-thumb-bg);\n    border: $form-range-thumb-border;\n    @include border-radius($form-range-thumb-border-radius);\n    @include box-shadow($form-range-thumb-box-shadow);\n    @include transition($form-range-thumb-transition);\n    appearance: none;\n\n    &:active {\n      @include gradient-bg($form-range-thumb-active-bg);\n    }\n  }\n\n  &::-moz-range-track {\n    width: $form-range-track-width;\n    height: $form-range-track-height;\n    color: transparent;\n    cursor: $form-range-track-cursor;\n    background-color: $form-range-track-bg;\n    border-color: transparent; // Firefox specific?\n    @include border-radius($form-range-track-border-radius);\n    @include box-shadow($form-range-track-box-shadow);\n  }\n\n  &:disabled {\n    pointer-events: none;\n\n    &::-webkit-slider-thumb {\n      background-color: $form-range-thumb-disabled-bg;\n    }\n\n    &::-moz-range-thumb {\n      background-color: $form-range-thumb-disabled-bg;\n    }\n  }\n}\n", ".form-floating {\n  position: relative;\n\n  > .form-control,\n  > .form-control-plaintext,\n  > .form-select {\n    height: $form-floating-height;\n    line-height: $form-floating-line-height;\n  }\n\n  > label {\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%; // allow textareas\n    padding: $form-floating-padding-y $form-floating-padding-x;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    white-space: nowrap;\n    pointer-events: none;\n    border: $input-border-width solid transparent; // Required for aligning label's text with the input as it affects inner box model\n    transform-origin: 0 0;\n    @include transition($form-floating-transition);\n  }\n\n  > .form-control,\n  > .form-control-plaintext {\n    padding: $form-floating-padding-y $form-floating-padding-x;\n\n    &::placeholder {\n      color: transparent;\n    }\n\n    &:focus,\n    &:not(:placeholder-shown) {\n      padding-top: $form-floating-input-padding-t;\n      padding-bottom: $form-floating-input-padding-b;\n    }\n    // Duplicated because `:-webkit-autofill` invalidates other selectors when grouped\n    &:-webkit-autofill {\n      padding-top: $form-floating-input-padding-t;\n      padding-bottom: $form-floating-input-padding-b;\n    }\n  }\n\n  > .form-select {\n    padding-top: $form-floating-input-padding-t;\n    padding-bottom: $form-floating-input-padding-b;\n  }\n\n  > .form-control:focus,\n  > .form-control:not(:placeholder-shown),\n  > .form-control-plaintext,\n  > .form-select {\n    ~ label {\n      opacity: $form-floating-label-opacity;\n      transform: $form-floating-label-transform;\n    }\n  }\n  // Duplicated because `:-webkit-autofill` invalidates other selectors when grouped\n  > .form-control:-webkit-autofill {\n    ~ label {\n      opacity: $form-floating-label-opacity;\n      transform: $form-floating-label-transform;\n    }\n  }\n\n  > .form-control-plaintext {\n    ~ label {\n      border-width: $input-border-width 0; // Required to properly position label text - as explained above\n    }\n  }\n}\n", "//\n// Base styles\n//\n\n.input-group {\n  position: relative;\n  display: flex;\n  flex-wrap: wrap; // For form validation feedback\n  align-items: stretch;\n  width: 100%;\n\n  > .form-control,\n  > .form-select,\n  > .form-floating {\n    position: relative; // For focus state's z-index\n    flex: 1 1 auto;\n    width: 1%;\n    min-width: 0; // https://stackoverflow.com/questions/36247140/why-dont-flex-items-shrink-past-content-size\n  }\n\n  // Bring the \"active\" form control to the top of surrounding elements\n  > .form-control:focus,\n  > .form-select:focus,\n  > .form-floating:focus-within {\n    z-index: 3;\n  }\n\n  // Ensure buttons are always above inputs for more visually pleasing borders.\n  // This isn't needed for `.input-group-text` since it shares the same border-color\n  // as our inputs.\n  .btn {\n    position: relative;\n    z-index: 2;\n\n    &:focus {\n      z-index: 3;\n    }\n  }\n}\n\n\n// Textual addons\n//\n// Serves as a catch-all element for any text or radio/checkbox input you wish\n// to prepend or append to an input.\n\n.input-group-text {\n  display: flex;\n  align-items: center;\n  padding: $input-group-addon-padding-y $input-group-addon-padding-x;\n  @include font-size($input-font-size); // Match inputs\n  font-weight: $input-group-addon-font-weight;\n  line-height: $input-line-height;\n  color: $input-group-addon-color;\n  text-align: center;\n  white-space: nowrap;\n  background-color: $input-group-addon-bg;\n  border: $input-border-width solid $input-group-addon-border-color;\n  @include border-radius($input-border-radius);\n}\n\n\n// Sizing\n//\n// Remix the default form control sizing classes into new ones for easier\n// manipulation.\n\n.input-group-lg > .form-control,\n.input-group-lg > .form-select,\n.input-group-lg > .input-group-text,\n.input-group-lg > .btn {\n  padding: $input-padding-y-lg $input-padding-x-lg;\n  @include font-size($input-font-size-lg);\n  @include border-radius($input-border-radius-lg);\n}\n\n.input-group-sm > .form-control,\n.input-group-sm > .form-select,\n.input-group-sm > .input-group-text,\n.input-group-sm > .btn {\n  padding: $input-padding-y-sm $input-padding-x-sm;\n  @include font-size($input-font-size-sm);\n  @include border-radius($input-border-radius-sm);\n}\n\n.input-group-lg > .form-select,\n.input-group-sm > .form-select {\n  padding-right: $form-select-padding-x + $form-select-indicator-padding;\n}\n\n\n// Rounded corners\n//\n// These rulesets must come after the sizing ones to properly override sm and lg\n// border-radius values when extending. They're more specific than we'd like\n// with the `.input-group >` part, but without it, we cannot override the sizing.\n\n// stylelint-disable-next-line no-duplicate-selectors\n.input-group {\n  &:not(.has-validation) {\n    > :not(:last-child):not(.dropdown-toggle):not(.dropdown-menu):not(.form-floating),\n    > .dropdown-toggle:nth-last-child(n + 3),\n    > .form-floating:not(:last-child) > .form-control,\n    > .form-floating:not(:last-child) > .form-select {\n      @include border-end-radius(0);\n    }\n  }\n\n  &.has-validation {\n    > :nth-last-child(n + 3):not(.dropdown-toggle):not(.dropdown-menu):not(.form-floating),\n    > .dropdown-toggle:nth-last-child(n + 4),\n    > .form-floating:nth-last-child(n + 3) > .form-control,\n    > .form-floating:nth-last-child(n + 3) > .form-select {\n      @include border-end-radius(0);\n    }\n  }\n\n  $validation-messages: \"\";\n  @each $state in map-keys($form-validation-states) {\n    $validation-messages: $validation-messages + \":not(.\" + unquote($state) + \"-tooltip)\" + \":not(.\" + unquote($state) + \"-feedback)\";\n  }\n\n  > :not(:first-child):not(.dropdown-menu):not(.form-floating)#{$validation-messages},\n  > .form-floating:not(:first-child) > .form-control,\n  > .form-floating:not(:first-child) > .form-select {\n    margin-left: -$input-border-width;\n    @include border-start-radius(0);\n  }\n}\n", "// This mixin uses an `if()` technique to be compatible with Dart Sass\n// See https://github.com/sass/sass/issues/1873#issuecomment-152293725 for more details\n\n// scss-docs-start form-validation-mixins\n@mixin form-validation-state-selector($state) {\n  @if ($state == \"valid\" or $state == \"invalid\") {\n    .was-validated #{if(&, \"&\", \"\")}:#{$state},\n    #{if(&, \"&\", \"\")}.is-#{$state} {\n      @content;\n    }\n  } @else {\n    #{if(&, \"&\", \"\")}.is-#{$state} {\n      @content;\n    }\n  }\n}\n\n@mixin form-validation-state(\n  $state,\n  $color,\n  $icon,\n  $tooltip-color: color-contrast($color),\n  $tooltip-bg-color: rgba($color, $form-feedback-tooltip-opacity),\n  $focus-box-shadow: 0 0 $input-btn-focus-blur $input-focus-width rgba($color, $input-btn-focus-color-opacity)\n) {\n  .#{$state}-feedback {\n    display: none;\n    width: 100%;\n    margin-top: $form-feedback-margin-top;\n    @include font-size($form-feedback-font-size);\n    font-style: $form-feedback-font-style;\n    color: $color;\n  }\n\n  .#{$state}-tooltip {\n    position: absolute;\n    top: 100%;\n    z-index: 5;\n    display: none;\n    max-width: 100%; // Contain to parent when possible\n    padding: $form-feedback-tooltip-padding-y $form-feedback-tooltip-padding-x;\n    margin-top: .1rem;\n    @include font-size($form-feedback-tooltip-font-size);\n    line-height: $form-feedback-tooltip-line-height;\n    color: $tooltip-color;\n    background-color: $tooltip-bg-color;\n    @include border-radius($form-feedback-tooltip-border-radius);\n  }\n\n  @include form-validation-state-selector($state) {\n    ~ .#{$state}-feedback,\n    ~ .#{$state}-tooltip {\n      display: block;\n    }\n  }\n\n  .form-control {\n    @include form-validation-state-selector($state) {\n      border-color: $color;\n\n      @if $enable-validation-icons {\n        padding-right: $input-height-inner;\n        background-image: escape-svg($icon);\n        background-repeat: no-repeat;\n        background-position: right $input-height-inner-quarter center;\n        background-size: $input-height-inner-half $input-height-inner-half;\n      }\n\n      &:focus {\n        border-color: $color;\n        box-shadow: $focus-box-shadow;\n      }\n    }\n  }\n\n  // stylelint-disable-next-line selector-no-qualifying-type\n  textarea.form-control {\n    @include form-validation-state-selector($state) {\n      @if $enable-validation-icons {\n        padding-right: $input-height-inner;\n        background-position: top $input-height-inner-quarter right $input-height-inner-quarter;\n      }\n    }\n  }\n\n  .form-select {\n    @include form-validation-state-selector($state) {\n      border-color: $color;\n\n      @if $enable-validation-icons {\n        &:not([multiple]):not([size]),\n        &:not([multiple])[size=\"1\"] {\n          padding-right: $form-select-feedback-icon-padding-end;\n          background-image: escape-svg($form-select-indicator), escape-svg($icon);\n          background-position: $form-select-bg-position, $form-select-feedback-icon-position;\n          background-size: $form-select-bg-size, $form-select-feedback-icon-size;\n        }\n      }\n\n      &:focus {\n        border-color: $color;\n        box-shadow: $focus-box-shadow;\n      }\n    }\n  }\n\n  .form-control-color {\n    @include form-validation-state-selector($state) {\n      @if $enable-validation-icons {\n        width: add($form-color-width, $input-height-inner);\n      }\n    }\n  }\n\n  .form-check-input {\n    @include form-validation-state-selector($state) {\n      border-color: $color;\n\n      &:checked {\n        background-color: $color;\n      }\n\n      &:focus {\n        box-shadow: $focus-box-shadow;\n      }\n\n      ~ .form-check-label {\n        color: $color;\n      }\n    }\n  }\n  .form-check-inline .form-check-input {\n    ~ .#{$state}-feedback {\n      margin-left: .5em;\n    }\n  }\n\n  .input-group .form-control,\n  .input-group .form-select {\n    @include form-validation-state-selector($state) {\n      @if $state == \"valid\" {\n        z-index: 1;\n      } @else if $state == \"invalid\" {\n        z-index: 2;\n      }\n      &:focus {\n        z-index: 3;\n      }\n    }\n  }\n}\n// scss-docs-end form-validation-mixins\n", "//\n// Base styles\n//\n\n.btn {\n  // scss-docs-start btn-css-vars\n  --#{$prefix}btn-padding-x: #{$btn-padding-x};\n  --#{$prefix}btn-padding-y: #{$btn-padding-y};\n  --#{$prefix}btn-font-family: #{$btn-font-family};\n  @include rfs($btn-font-size, --#{$prefix}btn-font-size);\n  --#{$prefix}btn-font-weight: #{$btn-font-weight};\n  --#{$prefix}btn-line-height: #{$btn-line-height};\n  --#{$prefix}btn-color: #{$body-color};\n  --#{$prefix}btn-bg: transparent;\n  --#{$prefix}btn-border-width: #{$btn-border-width};\n  --#{$prefix}btn-border-color: transparent;\n  --#{$prefix}btn-border-radius: #{$btn-border-radius};\n  --#{$prefix}btn-box-shadow: #{$btn-box-shadow};\n  --#{$prefix}btn-disabled-opacity: #{$btn-disabled-opacity};\n  --#{$prefix}btn-focus-box-shadow: 0 0 0 #{$btn-focus-width} rgba(var(--#{$prefix}btn-focus-shadow-rgb), .5);\n  // scss-docs-end btn-css-vars\n\n  display: inline-block;\n  padding: var(--#{$prefix}btn-padding-y) var(--#{$prefix}btn-padding-x);\n  font-family: var(--#{$prefix}btn-font-family);\n  @include font-size(var(--#{$prefix}btn-font-size));\n  font-weight: var(--#{$prefix}btn-font-weight);\n  line-height: var(--#{$prefix}btn-line-height);\n  color: var(--#{$prefix}btn-color);\n  text-align: center;\n  text-decoration: if($link-decoration == none, null, none);\n  white-space: $btn-white-space;\n  vertical-align: middle;\n  cursor: if($enable-button-pointers, pointer, null);\n  user-select: none;\n  border: var(--#{$prefix}btn-border-width) solid var(--#{$prefix}btn-border-color);\n  @include border-radius(var(--#{$prefix}btn-border-radius));\n  @include gradient-bg(var(--#{$prefix}btn-bg));\n  @include box-shadow(var(--#{$prefix}btn-box-shadow));\n  @include transition($btn-transition);\n\n  &:hover {\n    color: var(--#{$prefix}btn-hover-color);\n    text-decoration: if($link-hover-decoration == underline, none, null);\n    background-color: var(--#{$prefix}btn-hover-bg);\n    border-color: var(--#{$prefix}btn-hover-border-color);\n  }\n\n  .btn-check:focus + &,\n  &:focus {\n    color: var(--#{$prefix}btn-hover-color);\n    @include gradient-bg(var(--#{$prefix}btn-hover-bg));\n    border-color: var(--#{$prefix}btn-hover-border-color);\n    outline: 0;\n    // Avoid using mixin so we can pass custom focus shadow properly\n    @if $enable-shadows {\n      box-shadow: var(--#{$prefix}btn-box-shadow), var(--#{$prefix}btn-focus-box-shadow);\n    } @else {\n      box-shadow: var(--#{$prefix}btn-focus-box-shadow);\n    }\n  }\n\n  .btn-check:checked + &,\n  .btn-check:active + &,\n  &:active,\n  &.active,\n  &.show {\n    color: var(--#{$prefix}btn-active-color);\n    background-color: var(--#{$prefix}btn-active-bg);\n    // Remove CSS gradients if they're enabled\n    background-image: if($enable-gradients, none, null);\n    border-color: var(--#{$prefix}btn-active-border-color);\n    @include box-shadow(var(--#{$prefix}btn-active-shadow));\n\n    &:focus {\n      // Avoid using mixin so we can pass custom focus shadow properly\n      @if $enable-shadows {\n        box-shadow: var(--#{$prefix}btn-active-shadow), var(--#{$prefix}btn-focus-box-shadow);\n      } @else {\n        box-shadow: var(--#{$prefix}btn-focus-box-shadow);\n      }\n    }\n  }\n\n  &:disabled,\n  &.disabled,\n  fieldset:disabled & {\n    color: var(--#{$prefix}btn-disabled-color);\n    pointer-events: none;\n    background-color: var(--#{$prefix}btn-disabled-bg);\n    background-image: if($enable-gradients, none, null);\n    border-color: var(--#{$prefix}btn-disabled-border-color);\n    opacity: var(--#{$prefix}btn-disabled-opacity);\n    @include box-shadow(none);\n  }\n}\n\n\n//\n// Alternate buttons\n//\n\n// scss-docs-start btn-variant-loops\n@each $color, $value in $theme-colors {\n  .btn-#{$color} {\n    @if $color == \"light\" {\n      @include button-variant(\n        $value,\n        $value,\n        $hover-background: shade-color($value, $btn-hover-bg-shade-amount),\n        $hover-border: shade-color($value, $btn-hover-border-shade-amount),\n        $active-background: shade-color($value, $btn-active-bg-shade-amount),\n        $active-border: shade-color($value, $btn-active-border-shade-amount)\n      );\n    } @else if $color == \"dark\" {\n      @include button-variant(\n        $value,\n        $value,\n        $hover-background: tint-color($value, $btn-hover-bg-tint-amount),\n        $hover-border: tint-color($value, $btn-hover-border-tint-amount),\n        $active-background: tint-color($value, $btn-active-bg-tint-amount),\n        $active-border: tint-color($value, $btn-active-border-tint-amount)\n      );\n    } @else {\n      @include button-variant($value, $value);\n    }\n  }\n}\n\n@each $color, $value in $theme-colors {\n  .btn-outline-#{$color} {\n    @include button-outline-variant($value);\n  }\n}\n// scss-docs-end btn-variant-loops\n\n\n//\n// Link buttons\n//\n\n// Make a button look and behave like a link\n.btn-link {\n  --#{$prefix}btn-font-weight: #{$font-weight-normal};\n  --#{$prefix}btn-color: #{$btn-link-color};\n  --#{$prefix}btn-bg: transparent;\n  --#{$prefix}btn-border-color: transparent;\n  --#{$prefix}btn-hover-color: #{$btn-link-hover-color};\n  --#{$prefix}btn-hover-border-color: transparent;\n  --#{$prefix}btn-active-color: #{$btn-link-hover-color};\n  --#{$prefix}btn-active-border-color: transparent;\n  --#{$prefix}btn-disabled-color: #{$btn-link-disabled-color};\n  --#{$prefix}btn-disabled-border-color: transparent;\n  --#{$prefix}btn-box-shadow: none;\n  --#{$prefix}btn-focus-shadow-rgb: #{to-rgb(mix(color-contrast($primary), $primary, 15%))};\n\n  text-decoration: $link-decoration;\n\n  &:hover,\n  &:focus {\n    text-decoration: $link-hover-decoration;\n  }\n\n  &:focus {\n    color: var(--#{$prefix}btn-color);\n  }\n\n  &:hover {\n    color: var(--#{$prefix}btn-hover-color);\n  }\n\n  // No need for an active state here\n}\n\n\n//\n// Button Sizes\n//\n\n.btn-lg {\n  @include button-size($btn-padding-y-lg, $btn-padding-x-lg, $btn-font-size-lg, $btn-border-radius-lg);\n}\n\n.btn-sm {\n  @include button-size($btn-padding-y-sm, $btn-padding-x-sm, $btn-font-size-sm, $btn-border-radius-sm);\n}\n", "// Button variants\n//\n// Easily pump out default styles, as well as :hover, :focus, :active,\n// and disabled options for all buttons\n\n// scss-docs-start btn-variant-mixin\n@mixin button-variant(\n  $background,\n  $border,\n  $color: color-contrast($background),\n  $hover-background: if($color == $color-contrast-light, shade-color($background, $btn-hover-bg-shade-amount), tint-color($background, $btn-hover-bg-tint-amount)),\n  $hover-border: if($color == $color-contrast-light, shade-color($border, $btn-hover-border-shade-amount), tint-color($border, $btn-hover-border-tint-amount)),\n  $hover-color: color-contrast($hover-background),\n  $active-background: if($color == $color-contrast-light, shade-color($background, $btn-active-bg-shade-amount), tint-color($background, $btn-active-bg-tint-amount)),\n  $active-border: if($color == $color-contrast-light, shade-color($border, $btn-active-border-shade-amount), tint-color($border, $btn-active-border-tint-amount)),\n  $active-color: color-contrast($active-background),\n  $disabled-background: $background,\n  $disabled-border: $border,\n  $disabled-color: color-contrast($disabled-background)\n) {\n  --#{$prefix}btn-color: #{$color};\n  --#{$prefix}btn-bg: #{$background};\n  --#{$prefix}btn-border-color: #{$border};\n  --#{$prefix}btn-hover-color: #{$hover-color};\n  --#{$prefix}btn-hover-bg: #{$hover-background};\n  --#{$prefix}btn-hover-border-color: #{$hover-border};\n  --#{$prefix}btn-focus-shadow-rgb: #{to-rgb(mix($color, $border, 15%))};\n  --#{$prefix}btn-active-color: #{$active-color};\n  --#{$prefix}btn-active-bg: #{$active-background};\n  --#{$prefix}btn-active-border-color: #{$active-border};\n  --#{$prefix}btn-active-shadow: #{$btn-active-box-shadow};\n  --#{$prefix}btn-disabled-color: #{$disabled-color};\n  --#{$prefix}btn-disabled-bg: #{$disabled-background};\n  --#{$prefix}btn-disabled-border-color: #{$disabled-border};\n}\n// scss-docs-end btn-variant-mixin\n\n// scss-docs-start btn-outline-variant-mixin\n@mixin button-outline-variant(\n  $color,\n  $color-hover: color-contrast($color),\n  $active-background: $color,\n  $active-border: $color,\n  $active-color: color-contrast($active-background)\n) {\n  --#{$prefix}btn-color: #{$color};\n  --#{$prefix}btn-border-color: #{$color};\n  --#{$prefix}btn-hover-color: #{$color-hover};\n  --#{$prefix}btn-hover-bg: #{$active-background};\n  --#{$prefix}btn-hover-border-color: #{$active-border};\n  --#{$prefix}btn-focus-shadow-rgb: #{to-rgb($color)};\n  --#{$prefix}btn-active-color: #{$active-color};\n  --#{$prefix}btn-active-bg: #{$active-background};\n  --#{$prefix}btn-active-border-color: #{$active-border};\n  --#{$prefix}btn-active-shadow: #{$btn-active-box-shadow};\n  --#{$prefix}btn-disabled-color: #{$color};\n  --#{$prefix}btn-disabled-bg: transparent;\n  --#{$prefix}btn-disabled-border-color: #{$color};\n  --#{$prefix}gradient: none;\n}\n// scss-docs-end btn-outline-variant-mixin\n\n// scss-docs-start btn-size-mixin\n@mixin button-size($padding-y, $padding-x, $font-size, $border-radius) {\n  --#{$prefix}btn-padding-y: #{$padding-y};\n  --#{$prefix}btn-padding-x: #{$padding-x};\n  @include rfs($font-size, --#{$prefix}btn-font-size);\n  --#{$prefix}btn-border-radius: #{$border-radius};\n}\n// scss-docs-end btn-size-mixin\n", ".fade {\n  @include transition($transition-fade);\n\n  &:not(.show) {\n    opacity: 0;\n  }\n}\n\n// scss-docs-start collapse-classes\n.collapse {\n  &:not(.show) {\n    display: none;\n  }\n}\n\n.collapsing {\n  height: 0;\n  overflow: hidden;\n  @include transition($transition-collapse);\n\n  &.collapse-horizontal {\n    width: 0;\n    height: auto;\n    @include transition($transition-collapse-width);\n  }\n}\n// scss-docs-end collapse-classes\n", "// The dropdown wrapper (`<div>`)\n.dropup,\n.dropend,\n.dropdown,\n.dropstart,\n.dropup-center,\n.dropdown-center {\n  position: relative;\n}\n\n.dropdown-toggle {\n  white-space: nowrap;\n\n  // Generate the caret automatically\n  @include caret();\n}\n\n// The dropdown menu\n.dropdown-menu {\n  // scss-docs-start dropdown-css-vars\n  --#{$prefix}dropdown-min-width: #{$dropdown-min-width};\n  --#{$prefix}dropdown-padding-x: #{$dropdown-padding-x};\n  --#{$prefix}dropdown-padding-y: #{$dropdown-padding-y};\n  --#{$prefix}dropdown-spacer: #{$dropdown-spacer};\n  @include rfs($dropdown-font-size, --#{$prefix}dropdown-font-size);\n  --#{$prefix}dropdown-color: #{$dropdown-color};\n  --#{$prefix}dropdown-bg: #{$dropdown-bg};\n  --#{$prefix}dropdown-border-color: #{$dropdown-border-color};\n  --#{$prefix}dropdown-border-radius: #{$dropdown-border-radius};\n  --#{$prefix}dropdown-border-width: #{$dropdown-border-width};\n  --#{$prefix}dropdown-inner-border-radius: #{$dropdown-inner-border-radius};\n  --#{$prefix}dropdown-divider-bg: #{$dropdown-divider-bg};\n  --#{$prefix}dropdown-divider-margin-y: #{$dropdown-divider-margin-y};\n  --#{$prefix}dropdown-box-shadow: #{$dropdown-box-shadow};\n  --#{$prefix}dropdown-link-color: #{$dropdown-link-color};\n  --#{$prefix}dropdown-link-hover-color: #{$dropdown-link-hover-color};\n  --#{$prefix}dropdown-link-hover-bg: #{$dropdown-link-hover-bg};\n  --#{$prefix}dropdown-link-active-color: #{$dropdown-link-active-color};\n  --#{$prefix}dropdown-link-active-bg: #{$dropdown-link-active-bg};\n  --#{$prefix}dropdown-link-disabled-color: #{$dropdown-link-disabled-color};\n  --#{$prefix}dropdown-item-padding-x: #{$dropdown-item-padding-x};\n  --#{$prefix}dropdown-item-padding-y: #{$dropdown-item-padding-y};\n  --#{$prefix}dropdown-header-color: #{$dropdown-header-color};\n  --#{$prefix}dropdown-header-padding-x: #{$dropdown-header-padding-x};\n  --#{$prefix}dropdown-header-padding-y: #{$dropdown-header-padding-y};\n  // scss-docs-end dropdown-css-vars\n\n  position: absolute;\n  z-index: $zindex-dropdown;\n  display: none; // none by default, but block on \"open\" of the menu\n  min-width: var(--#{$prefix}dropdown-min-width);\n  padding: var(--#{$prefix}dropdown-padding-y) var(--#{$prefix}dropdown-padding-x);\n  margin: 0; // Override default margin of ul\n  @include font-size(var(--#{$prefix}dropdown-font-size));\n  color: var(--#{$prefix}dropdown-color);\n  text-align: left; // Ensures proper alignment if parent has it changed (e.g., modal footer)\n  list-style: none;\n  background-color: var(--#{$prefix}dropdown-bg);\n  background-clip: padding-box;\n  border: var(--#{$prefix}dropdown-border-width) solid var(--#{$prefix}dropdown-border-color);\n  @include border-radius(var(--#{$prefix}dropdown-border-radius));\n  @include box-shadow(var(--#{$prefix}dropdown-box-shadow));\n\n  &[data-bs-popper] {\n    top: 100%;\n    left: 0;\n    margin-top: var(--#{$prefix}dropdown-spacer);\n  }\n\n  @if $dropdown-padding-y == 0 {\n    > .dropdown-item:first-child,\n    > li:first-child .dropdown-item {\n      @include border-top-radius(var(--#{$prefix}dropdown-inner-border-radius));\n    }\n    > .dropdown-item:last-child,\n    > li:last-child .dropdown-item {\n      @include border-bottom-radius(var(--#{$prefix}dropdown-inner-border-radius));\n    }\n\n  }\n}\n\n// scss-docs-start responsive-breakpoints\n// We deliberately hardcode the `bs-` prefix because we check\n// this custom property in JS to determine Popper's positioning\n\n@each $breakpoint in map-keys($grid-breakpoints) {\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    .dropdown-menu#{$infix}-start {\n      --bs-position: start;\n\n      &[data-bs-popper] {\n        right: auto;\n        left: 0;\n      }\n    }\n\n    .dropdown-menu#{$infix}-end {\n      --bs-position: end;\n\n      &[data-bs-popper] {\n        right: 0;\n        left: auto;\n      }\n    }\n  }\n}\n// scss-docs-end responsive-breakpoints\n\n// Allow for dropdowns to go bottom up (aka, dropup-menu)\n// Just add .dropup after the standard .dropdown class and you're set.\n.dropup {\n  .dropdown-menu[data-bs-popper] {\n    top: auto;\n    bottom: 100%;\n    margin-top: 0;\n    margin-bottom: var(--#{$prefix}dropdown-spacer);\n  }\n\n  .dropdown-toggle {\n    @include caret(up);\n  }\n}\n\n.dropend {\n  .dropdown-menu[data-bs-popper] {\n    top: 0;\n    right: auto;\n    left: 100%;\n    margin-top: 0;\n    margin-left: var(--#{$prefix}dropdown-spacer);\n  }\n\n  .dropdown-toggle {\n    @include caret(end);\n    &::after {\n      vertical-align: 0;\n    }\n  }\n}\n\n.dropstart {\n  .dropdown-menu[data-bs-popper] {\n    top: 0;\n    right: 100%;\n    left: auto;\n    margin-top: 0;\n    margin-right: var(--#{$prefix}dropdown-spacer);\n  }\n\n  .dropdown-toggle {\n    @include caret(start);\n    &::before {\n      vertical-align: 0;\n    }\n  }\n}\n\n\n// Dividers (basically an `<hr>`) within the dropdown\n.dropdown-divider {\n  height: 0;\n  margin: var(--#{$prefix}dropdown-divider-margin-y) 0;\n  overflow: hidden;\n  border-top: 1px solid var(--#{$prefix}dropdown-divider-bg);\n  opacity: 1; // Revisit in v6 to de-dupe styles that conflict with <hr> element\n}\n\n// Links, buttons, and more within the dropdown menu\n//\n// `<button>`-specific styles are denoted with `// For <button>s`\n.dropdown-item {\n  display: block;\n  width: 100%; // For `<button>`s\n  padding: var(--#{$prefix}dropdown-item-padding-y) var(--#{$prefix}dropdown-item-padding-x);\n  clear: both;\n  font-weight: $font-weight-normal;\n  color: var(--#{$prefix}dropdown-link-color);\n  text-align: inherit; // For `<button>`s\n  text-decoration: if($link-decoration == none, null, none);\n  white-space: nowrap; // prevent links from randomly breaking onto new lines\n  background-color: transparent; // For `<button>`s\n  border: 0; // For `<button>`s\n\n  &:hover,\n  &:focus {\n    color: var(--#{$prefix}dropdown-link-hover-color);\n    text-decoration: if($link-hover-decoration == underline, none, null);\n    @include gradient-bg(var(--#{$prefix}dropdown-link-hover-bg));\n  }\n\n  &.active,\n  &:active {\n    color: var(--#{$prefix}dropdown-link-active-color);\n    text-decoration: none;\n    @include gradient-bg(var(--#{$prefix}dropdown-link-active-bg));\n  }\n\n  &.disabled,\n  &:disabled {\n    color: var(--#{$prefix}dropdown-link-disabled-color);\n    pointer-events: none;\n    background-color: transparent;\n    // Remove CSS gradients if they're enabled\n    background-image: if($enable-gradients, none, null);\n  }\n}\n\n.dropdown-menu.show {\n  display: block;\n}\n\n// Dropdown section headers\n.dropdown-header {\n  display: block;\n  padding: var(--#{$prefix}dropdown-header-padding-y) var(--#{$prefix}dropdown-header-padding-x);\n  margin-bottom: 0; // for use with heading elements\n  @include font-size($font-size-sm);\n  color: var(--#{$prefix}dropdown-header-color);\n  white-space: nowrap; // as with > li > a\n}\n\n// Dropdown text\n.dropdown-item-text {\n  display: block;\n  padding: var(--#{$prefix}dropdown-item-padding-y) var(--#{$prefix}dropdown-item-padding-x);\n  color: var(--#{$prefix}dropdown-link-color);\n}\n\n// Dark dropdowns\n.dropdown-menu-dark {\n  // scss-docs-start dropdown-dark-css-vars\n  --#{$prefix}dropdown-color: #{$dropdown-dark-color};\n  --#{$prefix}dropdown-bg: #{$dropdown-dark-bg};\n  --#{$prefix}dropdown-border-color: #{$dropdown-dark-border-color};\n  --#{$prefix}dropdown-box-shadow: #{$dropdown-dark-box-shadow};\n  --#{$prefix}dropdown-link-color: #{$dropdown-dark-link-color};\n  --#{$prefix}dropdown-link-hover-color: #{$dropdown-dark-link-hover-color};\n  --#{$prefix}dropdown-divider-bg: #{$dropdown-dark-divider-bg};\n  --#{$prefix}dropdown-link-hover-bg: #{$dropdown-dark-link-hover-bg};\n  --#{$prefix}dropdown-link-active-color: #{$dropdown-dark-link-active-color};\n  --#{$prefix}dropdown-link-active-bg: #{$dropdown-dark-link-active-bg};\n  --#{$prefix}dropdown-link-disabled-color: #{$dropdown-dark-link-disabled-color};\n  --#{$prefix}dropdown-header-color: #{$dropdown-dark-header-color};\n  // scss-docs-end dropdown-dark-css-vars\n}\n", "// scss-docs-start caret-mixins\n@mixin caret-down {\n  border-top: $caret-width solid;\n  border-right: $caret-width solid transparent;\n  border-bottom: 0;\n  border-left: $caret-width solid transparent;\n}\n\n@mixin caret-up {\n  border-top: 0;\n  border-right: $caret-width solid transparent;\n  border-bottom: $caret-width solid;\n  border-left: $caret-width solid transparent;\n}\n\n@mixin caret-end {\n  border-top: $caret-width solid transparent;\n  border-right: 0;\n  border-bottom: $caret-width solid transparent;\n  border-left: $caret-width solid;\n}\n\n@mixin caret-start {\n  border-top: $caret-width solid transparent;\n  border-right: $caret-width solid;\n  border-bottom: $caret-width solid transparent;\n}\n\n@mixin caret($direction: down) {\n  @if $enable-caret {\n    &::after {\n      display: inline-block;\n      margin-left: $caret-spacing;\n      vertical-align: $caret-vertical-align;\n      content: \"\";\n      @if $direction == down {\n        @include caret-down();\n      } @else if $direction == up {\n        @include caret-up();\n      } @else if $direction == end {\n        @include caret-end();\n      }\n    }\n\n    @if $direction == start {\n      &::after {\n        display: none;\n      }\n\n      &::before {\n        display: inline-block;\n        margin-right: $caret-spacing;\n        vertical-align: $caret-vertical-align;\n        content: \"\";\n        @include caret-start();\n      }\n    }\n\n    &:empty::after {\n      margin-left: 0;\n    }\n  }\n}\n// scss-docs-end caret-mixins\n", "// Make the div behave like a button\n.btn-group,\n.btn-group-vertical {\n  position: relative;\n  display: inline-flex;\n  vertical-align: middle; // match .btn alignment given font-size hack above\n\n  > .btn {\n    position: relative;\n    flex: 1 1 auto;\n  }\n\n  // Bring the hover, focused, and \"active\" buttons to the front to overlay\n  // the borders properly\n  > .btn-check:checked + .btn,\n  > .btn-check:focus + .btn,\n  > .btn:hover,\n  > .btn:focus,\n  > .btn:active,\n  > .btn.active {\n    z-index: 1;\n  }\n}\n\n// Optional: Group multiple button groups together for a toolbar\n.btn-toolbar {\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: flex-start;\n\n  .input-group {\n    width: auto;\n  }\n}\n\n.btn-group {\n  @include border-radius($btn-border-radius);\n\n  // Prevent double borders when buttons are next to each other\n  > .btn:not(:first-child),\n  > .btn-group:not(:first-child) {\n    margin-left: -$btn-border-width;\n  }\n\n  // Reset rounded corners\n  > .btn:not(:last-child):not(.dropdown-toggle),\n  > .btn.dropdown-toggle-split:first-child,\n  > .btn-group:not(:last-child) > .btn {\n    @include border-end-radius(0);\n  }\n\n  // The left radius should be 0 if the button is:\n  // - the \"third or more\" child\n  // - the second child and the previous element isn't `.btn-check` (making it the first child visually)\n  // - part of a btn-group which isn't the first child\n  > .btn:nth-child(n + 3),\n  > :not(.btn-check) + .btn,\n  > .btn-group:not(:first-child) > .btn {\n    @include border-start-radius(0);\n  }\n}\n\n// Sizing\n//\n// Remix the default button sizing classes into new ones for easier manipulation.\n\n.btn-group-sm > .btn { @extend .btn-sm; }\n.btn-group-lg > .btn { @extend .btn-lg; }\n\n\n//\n// Split button dropdowns\n//\n\n.dropdown-toggle-split {\n  padding-right: $btn-padding-x * .75;\n  padding-left: $btn-padding-x * .75;\n\n  &::after,\n  .dropup &::after,\n  .dropend &::after {\n    margin-left: 0;\n  }\n\n  .dropstart &::before {\n    margin-right: 0;\n  }\n}\n\n.btn-sm + .dropdown-toggle-split {\n  padding-right: $btn-padding-x-sm * .75;\n  padding-left: $btn-padding-x-sm * .75;\n}\n\n.btn-lg + .dropdown-toggle-split {\n  padding-right: $btn-padding-x-lg * .75;\n  padding-left: $btn-padding-x-lg * .75;\n}\n\n\n// The clickable button for toggling the menu\n// Set the same inset shadow as the :active state\n.btn-group.show .dropdown-toggle {\n  @include box-shadow($btn-active-box-shadow);\n\n  // Show no shadow for `.btn-link` since it has no other button styles.\n  &.btn-link {\n    @include box-shadow(none);\n  }\n}\n\n\n//\n// Vertical button groups\n//\n\n.btn-group-vertical {\n  flex-direction: column;\n  align-items: flex-start;\n  justify-content: center;\n\n  > .btn,\n  > .btn-group {\n    width: 100%;\n  }\n\n  > .btn:not(:first-child),\n  > .btn-group:not(:first-child) {\n    margin-top: -$btn-border-width;\n  }\n\n  // Reset rounded corners\n  > .btn:not(:last-child):not(.dropdown-toggle),\n  > .btn-group:not(:last-child) > .btn {\n    @include border-bottom-radius(0);\n  }\n\n  > .btn ~ .btn,\n  > .btn-group:not(:first-child) > .btn {\n    @include border-top-radius(0);\n  }\n}\n", "// Base class\n//\n// Kickstart any navigation component with a set of style resets. Works with\n// `<nav>`s, `<ul>`s or `<ol>`s.\n\n.nav {\n  // scss-docs-start nav-css-vars\n  --#{$prefix}nav-link-padding-x: #{$nav-link-padding-x};\n  --#{$prefix}nav-link-padding-y: #{$nav-link-padding-y};\n  @include rfs($nav-link-font-size, --#{$prefix}nav-link-font-size);\n  --#{$prefix}nav-link-font-weight: #{$nav-link-font-weight};\n  --#{$prefix}nav-link-color: #{$nav-link-color};\n  --#{$prefix}nav-link-hover-color: #{$nav-link-hover-color};\n  --#{$prefix}nav-link-disabled-color: #{$nav-link-disabled-color};\n  // scss-docs-end nav-css-vars\n\n  display: flex;\n  flex-wrap: wrap;\n  padding-left: 0;\n  margin-bottom: 0;\n  list-style: none;\n}\n\n.nav-link {\n  display: block;\n  padding: var(--#{$prefix}nav-link-padding-y) var(--#{$prefix}nav-link-padding-x);\n  @include font-size(var(--#{$prefix}nav-link-font-size));\n  font-weight: var(--#{$prefix}nav-link-font-weight);\n  color: var(--#{$prefix}nav-link-color);\n  text-decoration: if($link-decoration == none, null, none);\n  @include transition($nav-link-transition);\n\n  &:hover,\n  &:focus {\n    color: var(--#{$prefix}nav-link-hover-color);\n    text-decoration: if($link-hover-decoration == underline, none, null);\n  }\n\n  // Disabled state lightens text\n  &.disabled {\n    color: var(--#{$prefix}nav-link-disabled-color);\n    pointer-events: none;\n    cursor: default;\n  }\n}\n\n//\n// Tabs\n//\n\n.nav-tabs {\n  // scss-docs-start nav-tabs-css-vars\n  --#{$prefix}nav-tabs-border-width: #{$nav-tabs-border-width};\n  --#{$prefix}nav-tabs-border-color: #{$nav-tabs-border-color};\n  --#{$prefix}nav-tabs-border-radius: #{$nav-tabs-border-radius};\n  --#{$prefix}nav-tabs-link-hover-border-color: #{$nav-tabs-link-hover-border-color};\n  --#{$prefix}nav-tabs-link-active-color: #{$nav-tabs-link-active-color};\n  --#{$prefix}nav-tabs-link-active-bg: #{$nav-tabs-link-active-bg};\n  --#{$prefix}nav-tabs-link-active-border-color: #{$nav-tabs-link-active-border-color};\n  // scss-docs-end nav-tabs-css-vars\n\n  border-bottom: var(--#{$prefix}nav-tabs-border-width) solid var(--#{$prefix}nav-tabs-border-color);\n\n  .nav-link {\n    margin-bottom: calc(var(--#{$prefix}nav-tabs-border-width) * -1); // stylelint-disable-line function-disallowed-list\n    background: none;\n    border: var(--#{$prefix}nav-tabs-border-width) solid transparent;\n    @include border-top-radius(var(--#{$prefix}nav-tabs-border-radius));\n\n    &:hover,\n    &:focus {\n      // Prevents active .nav-link tab overlapping focus outline of previous/next .nav-link\n      isolation: isolate;\n      border-color: var(--#{$prefix}nav-tabs-link-hover-border-color);\n    }\n\n    &.disabled,\n    &:disabled {\n      color: var(--#{$prefix}nav-link-disabled-color);\n      background-color: transparent;\n      border-color: transparent;\n    }\n  }\n\n  .nav-link.active,\n  .nav-item.show .nav-link {\n    color: var(--#{$prefix}nav-tabs-link-active-color);\n    background-color: var(--#{$prefix}nav-tabs-link-active-bg);\n    border-color: var(--#{$prefix}nav-tabs-link-active-border-color);\n  }\n\n  .dropdown-menu {\n    // Make dropdown border overlap tab border\n    margin-top: calc(var(--#{$prefix}nav-tabs-border-width) * -1); // stylelint-disable-line function-disallowed-list\n    // Remove the top rounded corners here since there is a hard edge above the menu\n    @include border-top-radius(0);\n  }\n}\n\n\n//\n// Pills\n//\n\n.nav-pills {\n  // scss-docs-start nav-pills-css-vars\n  --#{$prefix}nav-pills-border-radius: #{$nav-pills-border-radius};\n  --#{$prefix}nav-pills-link-active-color: #{$nav-pills-link-active-color};\n  --#{$prefix}nav-pills-link-active-bg: #{$nav-pills-link-active-bg};\n  // scss-docs-end nav-pills-css-vars\n\n  .nav-link {\n    background: none;\n    border: 0;\n    @include border-radius(var(--#{$prefix}nav-pills-border-radius));\n\n    &:disabled {\n      color: var(--#{$prefix}nav-link-disabled-color);\n      background-color: transparent;\n      border-color: transparent;\n    }\n  }\n\n  .nav-link.active,\n  .show > .nav-link {\n    color: var(--#{$prefix}nav-pills-link-active-color);\n    @include gradient-bg(var(--#{$prefix}nav-pills-link-active-bg));\n  }\n}\n\n\n//\n// Justified variants\n//\n\n.nav-fill {\n  > .nav-link,\n  .nav-item {\n    flex: 1 1 auto;\n    text-align: center;\n  }\n}\n\n.nav-justified {\n  > .nav-link,\n  .nav-item {\n    flex-basis: 0;\n    flex-grow: 1;\n    text-align: center;\n  }\n}\n\n.nav-fill,\n.nav-justified {\n  .nav-item .nav-link {\n    width: 100%; // Make sure button will grow\n  }\n}\n\n\n// Tabbable tabs\n//\n// Hide tabbable panes to start, show them when `.active`\n\n.tab-content {\n  > .tab-pane {\n    display: none;\n  }\n  > .active {\n    display: block;\n  }\n}\n", "// Navbar\n//\n// Provide a static navbar from which we expand to create full-width, fixed, and\n// other navbar variations.\n\n.navbar {\n  // scss-docs-start navbar-css-vars\n  --#{$prefix}navbar-padding-x: #{if($navbar-padding-x == null, 0, $navbar-padding-x)};\n  --#{$prefix}navbar-padding-y: #{$navbar-padding-y};\n  --#{$prefix}navbar-color: #{$navbar-light-color};\n  --#{$prefix}navbar-hover-color: #{$navbar-light-hover-color};\n  --#{$prefix}navbar-disabled-color: #{$navbar-light-disabled-color};\n  --#{$prefix}navbar-active-color: #{$navbar-light-active-color};\n  --#{$prefix}navbar-brand-padding-y: #{$navbar-brand-padding-y};\n  --#{$prefix}navbar-brand-margin-end: #{$navbar-brand-margin-end};\n  --#{$prefix}navbar-brand-font-size: #{$navbar-brand-font-size};\n  --#{$prefix}navbar-brand-color: #{$navbar-light-brand-color};\n  --#{$prefix}navbar-brand-hover-color: #{$navbar-light-brand-hover-color};\n  --#{$prefix}navbar-nav-link-padding-x: #{$navbar-nav-link-padding-x};\n  --#{$prefix}navbar-toggler-padding-y: #{$navbar-toggler-padding-y};\n  --#{$prefix}navbar-toggler-padding-x: #{$navbar-toggler-padding-x};\n  --#{$prefix}navbar-toggler-font-size: #{$navbar-toggler-font-size};\n  --#{$prefix}navbar-toggler-icon-bg: #{escape-svg($navbar-light-toggler-icon-bg)};\n  --#{$prefix}navbar-toggler-border-color: #{$navbar-light-toggler-border-color};\n  --#{$prefix}navbar-toggler-border-radius: #{$navbar-toggler-border-radius};\n  --#{$prefix}navbar-toggler-focus-width: #{$navbar-toggler-focus-width};\n  --#{$prefix}navbar-toggler-transition: #{$navbar-toggler-transition};\n  // scss-docs-end navbar-css-vars\n\n  position: relative;\n  display: flex;\n  flex-wrap: wrap; // allow us to do the line break for collapsing content\n  align-items: center;\n  justify-content: space-between; // space out brand from logo\n  padding: var(--#{$prefix}navbar-padding-y) var(--#{$prefix}navbar-padding-x);\n  @include gradient-bg();\n\n  // Because flex properties aren't inherited, we need to redeclare these first\n  // few properties so that content nested within behave properly.\n  // The `flex-wrap` property is inherited to simplify the expanded navbars\n  %container-flex-properties {\n    display: flex;\n    flex-wrap: inherit;\n    align-items: center;\n    justify-content: space-between;\n  }\n\n  > .container,\n  > .container-fluid {\n    @extend %container-flex-properties;\n  }\n\n  @each $breakpoint, $container-max-width in $container-max-widths {\n    > .container#{breakpoint-infix($breakpoint, $container-max-widths)} {\n      @extend %container-flex-properties;\n    }\n  }\n}\n\n\n// Navbar brand\n//\n// Used for brand, project, or site names.\n\n.navbar-brand {\n  padding-top: var(--#{$prefix}navbar-brand-padding-y);\n  padding-bottom: var(--#{$prefix}navbar-brand-padding-y);\n  margin-right: var(--#{$prefix}navbar-brand-margin-end);\n  @include font-size(var(--#{$prefix}navbar-brand-font-size));\n  color: var(--#{$prefix}navbar-brand-color);\n  text-decoration: if($link-decoration == none, null, none);\n  white-space: nowrap;\n\n  &:hover,\n  &:focus {\n    color: var(--#{$prefix}navbar-brand-hover-color);\n    text-decoration: if($link-hover-decoration == underline, none, null);\n  }\n}\n\n\n// Navbar nav\n//\n// Custom navbar navigation (doesn't require `.nav`, but does make use of `.nav-link`).\n\n.navbar-nav {\n  // scss-docs-start navbar-nav-css-vars\n  --#{$prefix}nav-link-padding-x: 0;\n  --#{$prefix}nav-link-padding-y: #{$nav-link-padding-y};\n  @include rfs($nav-link-font-size, --#{$prefix}nav-link-font-size);\n  --#{$prefix}nav-link-font-weight: #{$nav-link-font-weight};\n  --#{$prefix}nav-link-color: var(--#{$prefix}navbar-color);\n  --#{$prefix}nav-link-hover-color: var(--#{$prefix}navbar-hover-color);\n  --#{$prefix}nav-link-disabled-color: var(--#{$prefix}navbar-disabled-color);\n  // scss-docs-end navbar-nav-css-vars\n\n  display: flex;\n  flex-direction: column; // cannot use `inherit` to get the `.navbar`s value\n  padding-left: 0;\n  margin-bottom: 0;\n  list-style: none;\n\n  .show > .nav-link,\n  .nav-link.active {\n    color: var(--#{$prefix}navbar-active-color);\n  }\n\n  .dropdown-menu {\n    position: static;\n  }\n}\n\n\n// Navbar text\n//\n//\n\n.navbar-text {\n  padding-top: $nav-link-padding-y;\n  padding-bottom: $nav-link-padding-y;\n  color: var(--#{$prefix}navbar-color);\n\n  a,\n  a:hover,\n  a:focus  {\n    color: var(--#{$prefix}navbar-active-color);\n  }\n}\n\n\n// Responsive navbar\n//\n// Custom styles for responsive collapsing and toggling of navbar contents.\n// Powered by the collapse Bootstrap JavaScript plugin.\n\n// When collapsed, prevent the toggleable navbar contents from appearing in\n// the default flexbox row orientation. Requires the use of `flex-wrap: wrap`\n// on the `.navbar` parent.\n.navbar-collapse {\n  flex-basis: 100%;\n  flex-grow: 1;\n  // For always expanded or extra full navbars, ensure content aligns itself\n  // properly vertically. Can be easily overridden with flex utilities.\n  align-items: center;\n}\n\n// Button for toggling the navbar when in its collapsed state\n.navbar-toggler {\n  padding: var(--#{$prefix}navbar-toggler-padding-y) var(--#{$prefix}navbar-toggler-padding-x);\n  @include font-size(var(--#{$prefix}navbar-toggler-font-size));\n  line-height: 1;\n  color: var(--#{$prefix}navbar-color);\n  background-color: transparent; // remove default button style\n  border: var(--#{$prefix}border-width) solid var(--#{$prefix}navbar-toggler-border-color); // remove default button style\n  @include border-radius(var(--#{$prefix}navbar-toggler-border-radius));\n  @include transition(var(--#{$prefix}navbar-toggler-transition));\n\n  &:hover {\n    text-decoration: none;\n  }\n\n  &:focus {\n    text-decoration: none;\n    outline: 0;\n    box-shadow: 0 0 0 var(--#{$prefix}navbar-toggler-focus-width);\n  }\n}\n\n// Keep as a separate element so folks can easily override it with another icon\n// or image file as needed.\n.navbar-toggler-icon {\n  display: inline-block;\n  width: 1.5em;\n  height: 1.5em;\n  vertical-align: middle;\n  background-image: var(--#{$prefix}navbar-toggler-icon-bg);\n  background-repeat: no-repeat;\n  background-position: center;\n  background-size: 100%;\n}\n\n.navbar-nav-scroll {\n  max-height: var(--#{$prefix}scroll-height, 75vh);\n  overflow-y: auto;\n}\n\n// scss-docs-start navbar-expand-loop\n// Generate series of `.navbar-expand-*` responsive classes for configuring\n// where your navbar collapses.\n.navbar-expand {\n  @each $breakpoint in map-keys($grid-breakpoints) {\n    $next: breakpoint-next($breakpoint, $grid-breakpoints);\n    $infix: breakpoint-infix($next, $grid-breakpoints);\n\n    // stylelint-disable-next-line scss/selector-no-union-class-name\n    &#{$infix} {\n      @include media-breakpoint-up($next) {\n        flex-wrap: nowrap;\n        justify-content: flex-start;\n\n        .navbar-nav {\n          flex-direction: row;\n\n          .dropdown-menu {\n            position: absolute;\n          }\n\n          .nav-link {\n            padding-right: var(--#{$prefix}navbar-nav-link-padding-x);\n            padding-left: var(--#{$prefix}navbar-nav-link-padding-x);\n          }\n        }\n\n        .navbar-nav-scroll {\n          overflow: visible;\n        }\n\n        .navbar-collapse {\n          display: flex !important; // stylelint-disable-line declaration-no-important\n          flex-basis: auto;\n        }\n\n        .navbar-toggler {\n          display: none;\n        }\n\n        .offcanvas {\n          // stylelint-disable declaration-no-important\n          position: static;\n          z-index: auto;\n          flex-grow: 1;\n          width: auto !important;\n          height: auto !important;\n          visibility: visible !important;\n          background-color: transparent !important;\n          border: 0 !important;\n          transform: none !important;\n          @include box-shadow(none);\n          @include transition(none);\n          // stylelint-enable declaration-no-important\n\n          .offcanvas-header {\n            display: none;\n          }\n\n          .offcanvas-body {\n            display: flex;\n            flex-grow: 0;\n            padding: 0;\n            overflow-y: visible;\n          }\n        }\n      }\n    }\n  }\n}\n// scss-docs-end navbar-expand-loop\n\n// Navbar themes\n//\n// Styles for switching between navbars with light or dark background.\n\n.navbar-light {\n  @include deprecate(\"`.navbar-light`\", \"v5.2.0\", \"v6.0.0\", true);\n}\n\n.navbar-dark {\n  --#{$prefix}navbar-color: #{$navbar-dark-color};\n  --#{$prefix}navbar-hover-color: #{$navbar-dark-hover-color};\n  --#{$prefix}navbar-disabled-color: #{$navbar-dark-disabled-color};\n  --#{$prefix}navbar-active-color: #{$navbar-dark-active-color};\n  --#{$prefix}navbar-brand-color: #{$navbar-dark-brand-color};\n  --#{$prefix}navbar-brand-hover-color: #{$navbar-dark-brand-hover-color};\n  --#{$prefix}navbar-toggler-border-color: #{$navbar-dark-toggler-border-color};\n  --#{$prefix}navbar-toggler-icon-bg: #{escape-svg($navbar-dark-toggler-icon-bg)};\n}\n", "//\n// Base styles\n//\n\n.card {\n  // scss-docs-start card-css-vars\n  --#{$prefix}card-spacer-y: #{$card-spacer-y};\n  --#{$prefix}card-spacer-x: #{$card-spacer-x};\n  --#{$prefix}card-title-spacer-y: #{$card-title-spacer-y};\n  --#{$prefix}card-border-width: #{$card-border-width};\n  --#{$prefix}card-border-color: #{$card-border-color};\n  --#{$prefix}card-border-radius: #{$card-border-radius};\n  --#{$prefix}card-box-shadow: #{$card-box-shadow};\n  --#{$prefix}card-inner-border-radius: #{$card-inner-border-radius};\n  --#{$prefix}card-cap-padding-y: #{$card-cap-padding-y};\n  --#{$prefix}card-cap-padding-x: #{$card-cap-padding-x};\n  --#{$prefix}card-cap-bg: #{$card-cap-bg};\n  --#{$prefix}card-cap-color: #{$card-cap-color};\n  --#{$prefix}card-height: #{$card-height};\n  --#{$prefix}card-color: #{$card-color};\n  --#{$prefix}card-bg: #{$card-bg};\n  --#{$prefix}card-img-overlay-padding: #{$card-img-overlay-padding};\n  --#{$prefix}card-group-margin: #{$card-group-margin};\n  // scss-docs-end card-css-vars\n\n  position: relative;\n  display: flex;\n  flex-direction: column;\n  min-width: 0; // See https://github.com/twbs/bootstrap/pull/22740#issuecomment-305868106\n  height: var(--#{$prefix}card-height);\n  word-wrap: break-word;\n  background-color: var(--#{$prefix}card-bg);\n  background-clip: border-box;\n  border: var(--#{$prefix}card-border-width) solid var(--#{$prefix}card-border-color);\n  @include border-radius(var(--#{$prefix}card-border-radius));\n  @include box-shadow(var(--#{$prefix}card-box-shadow));\n\n  > hr {\n    margin-right: 0;\n    margin-left: 0;\n  }\n\n  > .list-group {\n    border-top: inherit;\n    border-bottom: inherit;\n\n    &:first-child {\n      border-top-width: 0;\n      @include border-top-radius(var(--#{$prefix}card-inner-border-radius));\n    }\n\n    &:last-child  {\n      border-bottom-width: 0;\n      @include border-bottom-radius(var(--#{$prefix}card-inner-border-radius));\n    }\n  }\n\n  // Due to specificity of the above selector (`.card > .list-group`), we must\n  // use a child selector here to prevent double borders.\n  > .card-header + .list-group,\n  > .list-group + .card-footer {\n    border-top: 0;\n  }\n}\n\n.card-body {\n  // Enable `flex-grow: 1` for decks and groups so that card blocks take up\n  // as much space as possible, ensuring footers are aligned to the bottom.\n  flex: 1 1 auto;\n  padding: var(--#{$prefix}card-spacer-y) var(--#{$prefix}card-spacer-x);\n  color: var(--#{$prefix}card-color);\n}\n\n.card-title {\n  margin-bottom: var(--#{$prefix}card-title-spacer-y);\n}\n\n.card-subtitle {\n  margin-top: calc(-.5 * var(--#{$prefix}card-title-spacer-y)); // stylelint-disable-line function-disallowed-list\n  margin-bottom: 0;\n}\n\n.card-text:last-child {\n  margin-bottom: 0;\n}\n\n.card-link {\n  &:hover {\n    text-decoration: if($link-hover-decoration == underline, none, null);\n  }\n\n  + .card-link {\n    margin-left: var(--#{$prefix}card-spacer-x);\n  }\n}\n\n//\n// Optional textual caps\n//\n\n.card-header {\n  padding: var(--#{$prefix}card-cap-padding-y) var(--#{$prefix}card-cap-padding-x);\n  margin-bottom: 0; // Removes the default margin-bottom of <hN>\n  color: var(--#{$prefix}card-cap-color);\n  background-color: var(--#{$prefix}card-cap-bg);\n  border-bottom: var(--#{$prefix}card-border-width) solid var(--#{$prefix}card-border-color);\n\n  &:first-child {\n    @include border-radius(var(--#{$prefix}card-inner-border-radius) var(--#{$prefix}card-inner-border-radius) 0 0);\n  }\n}\n\n.card-footer {\n  padding: var(--#{$prefix}card-cap-padding-y) var(--#{$prefix}card-cap-padding-x);\n  color: var(--#{$prefix}card-cap-color);\n  background-color: var(--#{$prefix}card-cap-bg);\n  border-top: var(--#{$prefix}card-border-width) solid var(--#{$prefix}card-border-color);\n\n  &:last-child {\n    @include border-radius(0 0 var(--#{$prefix}card-inner-border-radius) var(--#{$prefix}card-inner-border-radius));\n  }\n}\n\n\n//\n// Header navs\n//\n\n.card-header-tabs {\n  margin-right: calc(-.5 * var(--#{$prefix}card-cap-padding-x)); // stylelint-disable-line function-disallowed-list\n  margin-bottom: calc(-1 * var(--#{$prefix}card-cap-padding-y)); // stylelint-disable-line function-disallowed-list\n  margin-left: calc(-.5 * var(--#{$prefix}card-cap-padding-x)); // stylelint-disable-line function-disallowed-list\n  border-bottom: 0;\n\n  .nav-link.active {\n    background-color: var(--#{$prefix}card-bg);\n    border-bottom-color: var(--#{$prefix}card-bg);\n  }\n}\n\n.card-header-pills {\n  margin-right: calc(-.5 * var(--#{$prefix}card-cap-padding-x)); // stylelint-disable-line function-disallowed-list\n  margin-left: calc(-.5 * var(--#{$prefix}card-cap-padding-x)); // stylelint-disable-line function-disallowed-list\n}\n\n// Card image\n.card-img-overlay {\n  position: absolute;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  padding: var(--#{$prefix}card-img-overlay-padding);\n  @include border-radius(var(--#{$prefix}card-inner-border-radius));\n}\n\n.card-img,\n.card-img-top,\n.card-img-bottom {\n  width: 100%; // Required because we use flexbox and this inherently applies align-self: stretch\n}\n\n.card-img,\n.card-img-top {\n  @include border-top-radius(var(--#{$prefix}card-inner-border-radius));\n}\n\n.card-img,\n.card-img-bottom {\n  @include border-bottom-radius(var(--#{$prefix}card-inner-border-radius));\n}\n\n\n//\n// Card groups\n//\n\n.card-group {\n  // The child selector allows nested `.card` within `.card-group`\n  // to display properly.\n  > .card {\n    margin-bottom: var(--#{$prefix}card-group-margin);\n  }\n\n  @include media-breakpoint-up(sm) {\n    display: flex;\n    flex-flow: row wrap;\n    // The child selector allows nested `.card` within `.card-group`\n    // to display properly.\n    > .card {\n      // Flexbugs #4: https://github.com/philipwalton/flexbugs#flexbug-4\n      flex: 1 0 0%;\n      margin-bottom: 0;\n\n      + .card {\n        margin-left: 0;\n        border-left: 0;\n      }\n\n      // Handle rounded corners\n      @if $enable-rounded {\n        &:not(:last-child) {\n          @include border-end-radius(0);\n\n          .card-img-top,\n          .card-header {\n            // stylelint-disable-next-line property-disallowed-list\n            border-top-right-radius: 0;\n          }\n          .card-img-bottom,\n          .card-footer {\n            // stylelint-disable-next-line property-disallowed-list\n            border-bottom-right-radius: 0;\n          }\n        }\n\n        &:not(:first-child) {\n          @include border-start-radius(0);\n\n          .card-img-top,\n          .card-header {\n            // stylelint-disable-next-line property-disallowed-list\n            border-top-left-radius: 0;\n          }\n          .card-img-bottom,\n          .card-footer {\n            // stylelint-disable-next-line property-disallowed-list\n            border-bottom-left-radius: 0;\n          }\n        }\n      }\n    }\n  }\n}\n", "//\n// Base styles\n//\n\n.accordion {\n  // scss-docs-start accordion-css-vars\n  --#{$prefix}accordion-color: #{color-contrast($accordion-bg)};\n  --#{$prefix}accordion-bg: #{$accordion-bg};\n  --#{$prefix}accordion-transition: #{$accordion-transition};\n  --#{$prefix}accordion-border-color: #{$accordion-border-color};\n  --#{$prefix}accordion-border-width: #{$accordion-border-width};\n  --#{$prefix}accordion-border-radius: #{$accordion-border-radius};\n  --#{$prefix}accordion-inner-border-radius: #{$accordion-inner-border-radius};\n  --#{$prefix}accordion-btn-padding-x: #{$accordion-button-padding-x};\n  --#{$prefix}accordion-btn-padding-y: #{$accordion-button-padding-y};\n  --#{$prefix}accordion-btn-color: #{$accordion-color};\n  --#{$prefix}accordion-btn-bg: #{$accordion-button-bg};\n  --#{$prefix}accordion-btn-icon: #{escape-svg($accordion-button-icon)};\n  --#{$prefix}accordion-btn-icon-width: #{$accordion-icon-width};\n  --#{$prefix}accordion-btn-icon-transform: #{$accordion-icon-transform};\n  --#{$prefix}accordion-btn-icon-transition: #{$accordion-icon-transition};\n  --#{$prefix}accordion-btn-active-icon: #{escape-svg($accordion-button-active-icon)};\n  --#{$prefix}accordion-btn-focus-border-color: #{$accordion-button-focus-border-color};\n  --#{$prefix}accordion-btn-focus-box-shadow: #{$accordion-button-focus-box-shadow};\n  --#{$prefix}accordion-body-padding-x: #{$accordion-body-padding-x};\n  --#{$prefix}accordion-body-padding-y: #{$accordion-body-padding-y};\n  --#{$prefix}accordion-active-color: #{$accordion-button-active-color};\n  --#{$prefix}accordion-active-bg: #{$accordion-button-active-bg};\n  // scss-docs-end accordion-css-vars\n}\n\n.accordion-button {\n  position: relative;\n  display: flex;\n  align-items: center;\n  width: 100%;\n  padding: var(--#{$prefix}accordion-btn-padding-y) var(--#{$prefix}accordion-btn-padding-x);\n  @include font-size($font-size-base);\n  color: var(--#{$prefix}accordion-btn-color);\n  text-align: left; // Reset button style\n  background-color: var(--#{$prefix}accordion-btn-bg);\n  border: 0;\n  @include border-radius(0);\n  overflow-anchor: none;\n  @include transition(var(--#{$prefix}accordion-transition));\n\n  &:not(.collapsed) {\n    color: var(--#{$prefix}accordion-active-color);\n    background-color: var(--#{$prefix}accordion-active-bg);\n    box-shadow: inset 0 calc(var(--#{$prefix}accordion-border-width) * -1) 0 var(--#{$prefix}accordion-border-color); // stylelint-disable-line function-disallowed-list\n\n    &::after {\n      background-image: var(--#{$prefix}accordion-btn-active-icon);\n      transform: var(--#{$prefix}accordion-btn-icon-transform);\n    }\n  }\n\n  // Accordion icon\n  &::after {\n    flex-shrink: 0;\n    width: var(--#{$prefix}accordion-btn-icon-width);\n    height: var(--#{$prefix}accordion-btn-icon-width);\n    margin-left: auto;\n    content: \"\";\n    background-image: var(--#{$prefix}accordion-btn-icon);\n    background-repeat: no-repeat;\n    background-size: var(--#{$prefix}accordion-btn-icon-width);\n    @include transition(var(--#{$prefix}accordion-btn-icon-transition));\n  }\n\n  &:hover {\n    z-index: 2;\n  }\n\n  &:focus {\n    z-index: 3;\n    border-color: var(--#{$prefix}accordion-btn-focus-border-color);\n    outline: 0;\n    box-shadow: var(--#{$prefix}accordion-btn-focus-box-shadow);\n  }\n}\n\n.accordion-header {\n  margin-bottom: 0;\n}\n\n.accordion-item {\n  color: var(--#{$prefix}accordion-color);\n  background-color: var(--#{$prefix}accordion-bg);\n  border: var(--#{$prefix}accordion-border-width) solid var(--#{$prefix}accordion-border-color);\n\n  &:first-of-type {\n    @include border-top-radius(var(--#{$prefix}accordion-border-radius));\n\n    .accordion-button {\n      @include border-top-radius(var(--#{$prefix}accordion-inner-border-radius));\n    }\n  }\n\n  &:not(:first-of-type) {\n    border-top: 0;\n  }\n\n  // Only set a border-radius on the last item if the accordion is collapsed\n  &:last-of-type {\n    @include border-bottom-radius(var(--#{$prefix}accordion-border-radius));\n\n    .accordion-button {\n      &.collapsed {\n        @include border-bottom-radius(var(--#{$prefix}accordion-inner-border-radius));\n      }\n    }\n\n    .accordion-collapse {\n      @include border-bottom-radius(var(--#{$prefix}accordion-border-radius));\n    }\n  }\n}\n\n.accordion-body {\n  padding: var(--#{$prefix}accordion-body-padding-y) var(--#{$prefix}accordion-body-padding-x);\n}\n\n\n// Flush accordion items\n//\n// Remove borders and border-radius to keep accordion items edge-to-edge.\n\n.accordion-flush {\n  .accordion-collapse {\n    border-width: 0;\n  }\n\n  .accordion-item {\n    border-right: 0;\n    border-left: 0;\n    @include border-radius(0);\n\n    &:first-child { border-top: 0; }\n    &:last-child { border-bottom: 0; }\n\n    .accordion-button {\n      @include border-radius(0);\n    }\n  }\n}\n", ".breadcrumb {\n  // scss-docs-start breadcrumb-css-vars\n  --#{$prefix}breadcrumb-padding-x: #{$breadcrumb-padding-x};\n  --#{$prefix}breadcrumb-padding-y: #{$breadcrumb-padding-y};\n  --#{$prefix}breadcrumb-margin-bottom: #{$breadcrumb-margin-bottom};\n  @include rfs($breadcrumb-font-size, --#{$prefix}breadcrumb-font-size);\n  --#{$prefix}breadcrumb-bg: #{$breadcrumb-bg};\n  --#{$prefix}breadcrumb-border-radius: #{$breadcrumb-border-radius};\n  --#{$prefix}breadcrumb-divider-color: #{$breadcrumb-divider-color};\n  --#{$prefix}breadcrumb-item-padding-x: #{$breadcrumb-item-padding-x};\n  --#{$prefix}breadcrumb-item-active-color: #{$breadcrumb-active-color};\n  // scss-docs-end breadcrumb-css-vars\n\n  display: flex;\n  flex-wrap: wrap;\n  padding: var(--#{$prefix}breadcrumb-padding-y) var(--#{$prefix}breadcrumb-padding-x);\n  margin-bottom: var(--#{$prefix}breadcrumb-margin-bottom);\n  @include font-size(var(--#{$prefix}breadcrumb-font-size));\n  list-style: none;\n  background-color: var(--#{$prefix}breadcrumb-bg);\n  @include border-radius(var(--#{$prefix}breadcrumb-border-radius));\n}\n\n.breadcrumb-item {\n  // The separator between breadcrumbs (by default, a forward-slash: \"/\")\n  + .breadcrumb-item {\n    padding-left: var(--#{$prefix}breadcrumb-item-padding-x);\n\n    &::before {\n      float: left; // Suppress inline spacings and underlining of the separator\n      padding-right: var(--#{$prefix}breadcrumb-item-padding-x);\n      color: var(--#{$prefix}breadcrumb-divider-color);\n      content: var(--#{$prefix}breadcrumb-divider, escape-svg($breadcrumb-divider)) #{\"/* rtl:\"} var(--#{$prefix}breadcrumb-divider, escape-svg($breadcrumb-divider-flipped)) #{\"*/\"};\n    }\n  }\n\n  &.active {\n    color: var(--#{$prefix}breadcrumb-item-active-color);\n  }\n}\n", ".pagination {\n  // scss-docs-start pagination-css-vars\n  --#{$prefix}pagination-padding-x: #{$pagination-padding-x};\n  --#{$prefix}pagination-padding-y: #{$pagination-padding-y};\n  @include rfs($pagination-font-size, --#{$prefix}pagination-font-size);\n  --#{$prefix}pagination-color: #{$pagination-color};\n  --#{$prefix}pagination-bg: #{$pagination-bg};\n  --#{$prefix}pagination-border-width: #{$pagination-border-width};\n  --#{$prefix}pagination-border-color: #{$pagination-border-color};\n  --#{$prefix}pagination-border-radius: #{$pagination-border-radius};\n  --#{$prefix}pagination-hover-color: #{$pagination-hover-color};\n  --#{$prefix}pagination-hover-bg: #{$pagination-hover-bg};\n  --#{$prefix}pagination-hover-border-color: #{$pagination-hover-border-color};\n  --#{$prefix}pagination-focus-color: #{$pagination-focus-color};\n  --#{$prefix}pagination-focus-bg: #{$pagination-focus-bg};\n  --#{$prefix}pagination-focus-box-shadow: #{$pagination-focus-box-shadow};\n  --#{$prefix}pagination-active-color: #{$pagination-active-color};\n  --#{$prefix}pagination-active-bg: #{$pagination-active-bg};\n  --#{$prefix}pagination-active-border-color: #{$pagination-active-border-color};\n  --#{$prefix}pagination-disabled-color: #{$pagination-disabled-color};\n  --#{$prefix}pagination-disabled-bg: #{$pagination-disabled-bg};\n  --#{$prefix}pagination-disabled-border-color: #{$pagination-disabled-border-color};\n  // scss-docs-end pagination-css-vars\n\n  display: flex;\n  @include list-unstyled();\n}\n\n.page-link {\n  position: relative;\n  display: block;\n  padding: var(--#{$prefix}pagination-padding-y) var(--#{$prefix}pagination-padding-x);\n  @include font-size(var(--#{$prefix}pagination-font-size));\n  color: var(--#{$prefix}pagination-color);\n  text-decoration: if($link-decoration == none, null, none);\n  background-color: var(--#{$prefix}pagination-bg);\n  border: var(--#{$prefix}pagination-border-width) solid var(--#{$prefix}pagination-border-color);\n  @include transition($pagination-transition);\n\n  &:hover {\n    z-index: 2;\n    color: var(--#{$prefix}pagination-hover-color);\n    text-decoration: if($link-hover-decoration == underline, none, null);\n    background-color: var(--#{$prefix}pagination-hover-bg);\n    border-color: var(--#{$prefix}pagination-hover-border-color);\n  }\n\n  &:focus {\n    z-index: 3;\n    color: var(--#{$prefix}pagination-focus-color);\n    background-color: var(--#{$prefix}pagination-focus-bg);\n    outline: $pagination-focus-outline;\n    box-shadow: var(--#{$prefix}pagination-focus-box-shadow);\n  }\n\n  &.active,\n  .active > & {\n    z-index: 3;\n    color: var(--#{$prefix}pagination-active-color);\n    @include gradient-bg(var(--#{$prefix}pagination-active-bg));\n    border-color: var(--#{$prefix}pagination-active-border-color);\n  }\n\n  &.disabled,\n  .disabled > & {\n    color: var(--#{$prefix}pagination-disabled-color);\n    pointer-events: none;\n    background-color: var(--#{$prefix}pagination-disabled-bg);\n    border-color: var(--#{$prefix}pagination-disabled-border-color);\n  }\n}\n\n.page-item {\n  &:not(:first-child) .page-link {\n    margin-left: $pagination-margin-start;\n  }\n\n  @if $pagination-margin-start == (calc($pagination-border-width * -1)) {\n    &:first-child {\n      .page-link {\n        @include border-start-radius(var(--#{$prefix}pagination-border-radius));\n      }\n    }\n\n    &:last-child {\n      .page-link {\n        @include border-end-radius(var(--#{$prefix}pagination-border-radius));\n      }\n    }\n  } @else {\n    // Add border-radius to all pageLinks in case they have left margin\n    .page-link {\n      @include border-radius(var(--#{$prefix}pagination-border-radius));\n    }\n  }\n}\n\n\n//\n// Sizing\n//\n\n.pagination-lg {\n  @include pagination-size($pagination-padding-y-lg, $pagination-padding-x-lg, $font-size-lg, $pagination-border-radius-lg);\n}\n\n.pagination-sm {\n  @include pagination-size($pagination-padding-y-sm, $pagination-padding-x-sm, $font-size-sm, $pagination-border-radius-sm);\n}\n", "// Pagination\n\n// scss-docs-start pagination-mixin\n@mixin pagination-size($padding-y, $padding-x, $font-size, $border-radius) {\n  --#{$prefix}pagination-padding-x: #{$padding-x};\n  --#{$prefix}pagination-padding-y: #{$padding-y};\n  @include rfs($font-size, --#{$prefix}pagination-font-size);\n  --#{$prefix}pagination-border-radius: #{$border-radius};\n}\n// scss-docs-end pagination-mixin\n", "// Base class\n//\n// Requires one of the contextual, color modifier classes for `color` and\n// `background-color`.\n\n.badge {\n  // scss-docs-start badge-css-vars\n  --#{$prefix}badge-padding-x: #{$badge-padding-x};\n  --#{$prefix}badge-padding-y: #{$badge-padding-y};\n  @include rfs($badge-font-size, --#{$prefix}badge-font-size);\n  --#{$prefix}badge-font-weight: #{$badge-font-weight};\n  --#{$prefix}badge-color: #{$badge-color};\n  --#{$prefix}badge-border-radius: #{$badge-border-radius};\n  // scss-docs-end badge-css-vars\n\n  display: inline-block;\n  padding: var(--#{$prefix}badge-padding-y) var(--#{$prefix}badge-padding-x);\n  @include font-size(var(--#{$prefix}badge-font-size));\n  font-weight: var(--#{$prefix}badge-font-weight);\n  line-height: 1;\n  color: var(--#{$prefix}badge-color);\n  text-align: center;\n  white-space: nowrap;\n  vertical-align: baseline;\n  @include border-radius(var(--#{$prefix}badge-border-radius));\n  @include gradient-bg();\n\n  // Empty badges collapse automatically\n  &:empty {\n    display: none;\n  }\n}\n\n// Quick fix for badges in buttons\n.btn .badge {\n  position: relative;\n  top: -1px;\n}\n", "//\n// Base styles\n//\n\n.alert {\n  // scss-docs-start alert-css-vars\n  --#{$prefix}alert-bg: transparent;\n  --#{$prefix}alert-padding-x: #{$alert-padding-x};\n  --#{$prefix}alert-padding-y: #{$alert-padding-y};\n  --#{$prefix}alert-margin-bottom: #{$alert-margin-bottom};\n  --#{$prefix}alert-color: inherit;\n  --#{$prefix}alert-border-color: transparent;\n  --#{$prefix}alert-border: #{$alert-border-width} solid var(--#{$prefix}alert-border-color);\n  --#{$prefix}alert-border-radius: #{$alert-border-radius};\n  // scss-docs-end alert-css-vars\n\n  position: relative;\n  padding: var(--#{$prefix}alert-padding-y) var(--#{$prefix}alert-padding-x);\n  margin-bottom: var(--#{$prefix}alert-margin-bottom);\n  color: var(--#{$prefix}alert-color);\n  background-color: var(--#{$prefix}alert-bg);\n  border: var(--#{$prefix}alert-border);\n  @include border-radius(var(--#{$prefix}alert-border-radius));\n}\n\n// Headings for larger alerts\n.alert-heading {\n  // Specified to prevent conflicts of changing $headings-color\n  color: inherit;\n}\n\n// Provide class for links that match alerts\n.alert-link {\n  font-weight: $alert-link-font-weight;\n}\n\n\n// Dismissible alerts\n//\n// Expand the right padding and account for the close button's positioning.\n\n.alert-dismissible {\n  padding-right: $alert-dismissible-padding-r;\n\n  // Adjust close link position\n  .btn-close {\n    position: absolute;\n    top: 0;\n    right: 0;\n    z-index: $stretched-link-z-index + 1;\n    padding: $alert-padding-y * 1.25 $alert-padding-x;\n  }\n}\n\n\n// scss-docs-start alert-modifiers\n// Generate contextual modifier classes for colorizing the alert.\n\n@each $state, $value in $theme-colors {\n  $alert-background: shift-color($value, $alert-bg-scale);\n  $alert-border: shift-color($value, $alert-border-scale);\n  $alert-color: shift-color($value, $alert-color-scale);\n\n  @if (contrast-ratio($alert-background, $alert-color) < $min-contrast-ratio) {\n    $alert-color: mix($value, color-contrast($alert-background), abs($alert-color-scale));\n  }\n  .alert-#{$state} {\n    @include alert-variant($alert-background, $alert-border, $alert-color);\n  }\n}\n// scss-docs-end alert-modifiers\n", "// scss-docs-start alert-variant-mixin\n@mixin alert-variant($background, $border, $color) {\n  --#{$prefix}alert-color: #{$color};\n  --#{$prefix}alert-bg: #{$background};\n  --#{$prefix}alert-border-color: #{$border};\n\n  @if $enable-gradients {\n    background-image: var(--#{$prefix}gradient);\n  }\n\n  .alert-link {\n    color: shade-color($color, 20%);\n  }\n}\n// scss-docs-end alert-variant-mixin\n", "// Disable animation if transitions are disabled\n\n// scss-docs-start progress-keyframes\n@if $enable-transitions {\n  @keyframes progress-bar-stripes {\n    0% { background-position-x: $progress-height; }\n  }\n}\n// scss-docs-end progress-keyframes\n\n.progress {\n  // scss-docs-start progress-css-vars\n  --#{$prefix}progress-height: #{$progress-height};\n  @include rfs($progress-font-size, --#{$prefix}progress-font-size);\n  --#{$prefix}progress-bg: #{$progress-bg};\n  --#{$prefix}progress-border-radius: #{$progress-border-radius};\n  --#{$prefix}progress-box-shadow: #{$progress-box-shadow};\n  --#{$prefix}progress-bar-color: #{$progress-bar-color};\n  --#{$prefix}progress-bar-bg: #{$progress-bar-bg};\n  --#{$prefix}progress-bar-transition: #{$progress-bar-transition};\n  // scss-docs-end progress-css-vars\n\n  display: flex;\n  height: var(--#{$prefix}progress-height);\n  overflow: hidden; // force rounded corners by cropping it\n  @include font-size(var(--#{$prefix}progress-font-size));\n  background-color: var(--#{$prefix}progress-bg);\n  @include border-radius(var(--#{$prefix}progress-border-radius));\n  @include box-shadow(var(--#{$prefix}progress-box-shadow));\n}\n\n.progress-bar {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  overflow: hidden;\n  color: var(--#{$prefix}progress-bar-color);\n  text-align: center;\n  white-space: nowrap;\n  background-color: var(--#{$prefix}progress-bar-bg);\n  @include transition(var(--#{$prefix}progress-bar-transition));\n}\n\n.progress-bar-striped {\n  @include gradient-striped();\n  background-size: var(--#{$prefix}progress-height) var(--#{$prefix}progress-height);\n}\n\n@if $enable-transitions {\n  .progress-bar-animated {\n    animation: $progress-bar-animation-timing progress-bar-stripes;\n\n    @if $enable-reduced-motion {\n      @media (prefers-reduced-motion: reduce) {\n        animation: none;\n      }\n    }\n  }\n}\n", "// Base class\n//\n// Easily usable on <ul>, <ol>, or <div>.\n\n.list-group {\n  // scss-docs-start list-group-css-vars\n  --#{$prefix}list-group-color: #{$list-group-color};\n  --#{$prefix}list-group-bg: #{$list-group-bg};\n  --#{$prefix}list-group-border-color: #{$list-group-border-color};\n  --#{$prefix}list-group-border-width: #{$list-group-border-width};\n  --#{$prefix}list-group-border-radius: #{$list-group-border-radius};\n  --#{$prefix}list-group-item-padding-x: #{$list-group-item-padding-x};\n  --#{$prefix}list-group-item-padding-y: #{$list-group-item-padding-y};\n  --#{$prefix}list-group-action-color: #{$list-group-action-color};\n  --#{$prefix}list-group-action-hover-color: #{$list-group-action-hover-color};\n  --#{$prefix}list-group-action-hover-bg: #{$list-group-hover-bg};\n  --#{$prefix}list-group-action-active-color: #{$list-group-action-active-color};\n  --#{$prefix}list-group-action-active-bg: #{$list-group-action-active-bg};\n  --#{$prefix}list-group-disabled-color: #{$list-group-disabled-color};\n  --#{$prefix}list-group-disabled-bg: #{$list-group-disabled-bg};\n  --#{$prefix}list-group-active-color: #{$list-group-active-color};\n  --#{$prefix}list-group-active-bg: #{$list-group-active-bg};\n  --#{$prefix}list-group-active-border-color: #{$list-group-active-border-color};\n  // scss-docs-end list-group-css-vars\n\n  display: flex;\n  flex-direction: column;\n\n  // No need to set list-style: none; since .list-group-item is block level\n  padding-left: 0; // reset padding because ul and ol\n  margin-bottom: 0;\n  @include border-radius(var(--#{$prefix}list-group-border-radius));\n}\n\n.list-group-numbered {\n  list-style-type: none;\n  counter-reset: section;\n\n  > .list-group-item::before {\n    // Increments only this instance of the section counter\n    content: counters(section, \".\") \". \";\n    counter-increment: section;\n  }\n}\n\n// Interactive list items\n//\n// Use anchor or button elements instead of `li`s or `div`s to create interactive\n// list items. Includes an extra `.active` modifier class for selected items.\n\n.list-group-item-action {\n  width: 100%; // For `<button>`s (anchors become 100% by default though)\n  color: var(--#{$prefix}list-group-action-color);\n  text-align: inherit; // For `<button>`s (anchors inherit)\n\n  // Hover state\n  &:hover,\n  &:focus {\n    z-index: 1; // Place hover/focus items above their siblings for proper border styling\n    color: var(--#{$prefix}list-group-action-hover-color);\n    text-decoration: none;\n    background-color: var(--#{$prefix}list-group-action-hover-bg);\n  }\n\n  &:active {\n    color: var(--#{$prefix}list-group-action-active-color);\n    background-color: var(--#{$prefix}list-group-action-active-bg);\n  }\n}\n\n// Individual list items\n//\n// Use on `li`s or `div`s within the `.list-group` parent.\n\n.list-group-item {\n  position: relative;\n  display: block;\n  padding: var(--#{$prefix}list-group-item-padding-y) var(--#{$prefix}list-group-item-padding-x);\n  color: var(--#{$prefix}list-group-color);\n  text-decoration: if($link-decoration == none, null, none);\n  background-color: var(--#{$prefix}list-group-bg);\n  border: var(--#{$prefix}list-group-border-width) solid var(--#{$prefix}list-group-border-color);\n\n  &:first-child {\n    @include border-top-radius(inherit);\n  }\n\n  &:last-child {\n    @include border-bottom-radius(inherit);\n  }\n\n  &.disabled,\n  &:disabled {\n    color: var(--#{$prefix}list-group-disabled-color);\n    pointer-events: none;\n    background-color: var(--#{$prefix}list-group-disabled-bg);\n  }\n\n  // Include both here for `<a>`s and `<button>`s\n  &.active {\n    z-index: 2; // Place active items above their siblings for proper border styling\n    color: var(--#{$prefix}list-group-active-color);\n    background-color: var(--#{$prefix}list-group-active-bg);\n    border-color: var(--#{$prefix}list-group-active-border-color);\n  }\n\n  & + & {\n    border-top-width: 0;\n\n    &.active {\n      margin-top: calc(var(--#{$prefix}list-group-border-width) * -1); // stylelint-disable-line function-disallowed-list\n      border-top-width: var(--#{$prefix}list-group-border-width);\n    }\n  }\n}\n\n// Horizontal\n//\n// Change the layout of list group items from vertical (default) to horizontal.\n\n@each $breakpoint in map-keys($grid-breakpoints) {\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    .list-group-horizontal#{$infix} {\n      flex-direction: row;\n\n      > .list-group-item {\n        &:first-child {\n          @include border-bottom-start-radius(var(--#{$prefix}list-group-border-radius));\n          @include border-top-end-radius(0);\n        }\n\n        &:last-child {\n          @include border-top-end-radius(var(--#{$prefix}list-group-border-radius));\n          @include border-bottom-start-radius(0);\n        }\n\n        &.active {\n          margin-top: 0;\n        }\n\n        + .list-group-item {\n          border-top-width: var(--#{$prefix}list-group-border-width);\n          border-left-width: 0;\n\n          &.active {\n            margin-left: calc(var(--#{$prefix}list-group-border-width) * -1); // stylelint-disable-line function-disallowed-list\n            border-left-width: var(--#{$prefix}list-group-border-width);\n          }\n        }\n      }\n    }\n  }\n}\n\n\n// Flush list items\n//\n// Remove borders and border-radius to keep list group items edge-to-edge. Most\n// useful within other components (e.g., cards).\n\n.list-group-flush {\n  @include border-radius(0);\n\n  > .list-group-item {\n    border-width: 0 0 var(--#{$prefix}list-group-border-width);\n\n    &:last-child {\n      border-bottom-width: 0;\n    }\n  }\n}\n\n\n// scss-docs-start list-group-modifiers\n// List group contextual variants\n//\n// Add modifier classes to change text and background color on individual items.\n// Organizationally, this must come after the `:hover` states.\n\n@each $state, $value in $theme-colors {\n  $list-group-variant-bg: shift-color($value, $list-group-item-bg-scale);\n  $list-group-variant-color: shift-color($value, $list-group-item-color-scale);\n  @if (contrast-ratio($list-group-variant-bg, $list-group-variant-color) < $min-contrast-ratio) {\n    $list-group-variant-color: mix($value, color-contrast($list-group-variant-bg), abs($list-group-item-color-scale));\n  }\n\n  @include list-group-item-variant($state, $list-group-variant-bg, $list-group-variant-color);\n}\n// scss-docs-end list-group-modifiers\n", "// List Groups\n\n// scss-docs-start list-group-mixin\n@mixin list-group-item-variant($state, $background, $color) {\n  .list-group-item-#{$state} {\n    color: $color;\n    background-color: $background;\n\n    &.list-group-item-action {\n      &:hover,\n      &:focus {\n        color: $color;\n        background-color: shade-color($background, 10%);\n      }\n\n      &.active {\n        color: $white;\n        background-color: $color;\n        border-color: $color;\n      }\n    }\n  }\n}\n// scss-docs-end list-group-mixin\n", "// Transparent background and border properties included for button version.\n// iOS requires the button element instead of an anchor tag.\n// If you want the anchor version, it requires `href=\"#\"`.\n// See https://developer.mozilla.org/en-US/docs/Web/Events/click#Safari_Mobile\n\n.btn-close {\n  box-sizing: content-box;\n  width: $btn-close-width;\n  height: $btn-close-height;\n  padding: $btn-close-padding-y $btn-close-padding-x;\n  color: $btn-close-color;\n  background: transparent escape-svg($btn-close-bg) center / $btn-close-width auto no-repeat; // include transparent for button elements\n  border: 0; // for button elements\n  @include border-radius();\n  opacity: $btn-close-opacity;\n\n  // Override <a>'s hover style\n  &:hover {\n    color: $btn-close-color;\n    text-decoration: none;\n    opacity: $btn-close-hover-opacity;\n  }\n\n  &:focus {\n    outline: 0;\n    box-shadow: $btn-close-focus-shadow;\n    opacity: $btn-close-focus-opacity;\n  }\n\n  &:disabled,\n  &.disabled {\n    pointer-events: none;\n    user-select: none;\n    opacity: $btn-close-disabled-opacity;\n  }\n}\n\n.btn-close-white {\n  filter: $btn-close-white-filter;\n}\n", ".toast {\n  // scss-docs-start toast-css-vars\n  --#{$prefix}toast-padding-x: #{$toast-padding-x};\n  --#{$prefix}toast-padding-y: #{$toast-padding-y};\n  --#{$prefix}toast-spacing: #{$toast-spacing};\n  --#{$prefix}toast-max-width: #{$toast-max-width};\n  @include rfs($toast-font-size, --#{$prefix}toast-font-size);\n  --#{$prefix}toast-color: #{$toast-color};\n  --#{$prefix}toast-bg: #{$toast-background-color};\n  --#{$prefix}toast-border-width: #{$toast-border-width};\n  --#{$prefix}toast-border-color: #{$toast-border-color};\n  --#{$prefix}toast-border-radius: #{$toast-border-radius};\n  --#{$prefix}toast-box-shadow: #{$toast-box-shadow};\n  --#{$prefix}toast-header-color: #{$toast-header-color};\n  --#{$prefix}toast-header-bg: #{$toast-header-background-color};\n  --#{$prefix}toast-header-border-color: #{$toast-header-border-color};\n  // scss-docs-end toast-css-vars\n\n  width: var(--#{$prefix}toast-max-width);\n  max-width: 100%;\n  @include font-size(var(--#{$prefix}toast-font-size));\n  color: var(--#{$prefix}toast-color);\n  pointer-events: auto;\n  background-color: var(--#{$prefix}toast-bg);\n  background-clip: padding-box;\n  border: var(--#{$prefix}toast-border-width) solid var(--#{$prefix}toast-border-color);\n  box-shadow: var(--#{$prefix}toast-box-shadow);\n  @include border-radius(var(--#{$prefix}toast-border-radius));\n\n  &.showing {\n    opacity: 0;\n  }\n\n  &:not(.show) {\n    display: none;\n  }\n}\n\n.toast-container {\n  position: absolute;\n  z-index: $zindex-toast;\n  width: max-content;\n  max-width: 100%;\n  pointer-events: none;\n\n  > :not(:last-child) {\n    margin-bottom: var(--#{$prefix}toast-spacing);\n  }\n}\n\n.toast-header {\n  display: flex;\n  align-items: center;\n  padding: var(--#{$prefix}toast-padding-y) var(--#{$prefix}toast-padding-x);\n  color: var(--#{$prefix}toast-header-color);\n  background-color: var(--#{$prefix}toast-header-bg);\n  background-clip: padding-box;\n  border-bottom: var(--#{$prefix}toast-border-width) solid var(--#{$prefix}toast-header-border-color);\n  @include border-top-radius(calc(var(--#{$prefix}toast-border-radius) - var(--#{$prefix}toast-border-width)));\n\n  .btn-close {\n    margin-right: calc(var(--#{$prefix}toast-padding-x) * -.5); // stylelint-disable-line function-disallowed-list\n    margin-left: var(--#{$prefix}toast-padding-x);\n  }\n}\n\n.toast-body {\n  padding: var(--#{$prefix}toast-padding-x);\n  word-wrap: break-word;\n}\n", "// stylelint-disable function-disallowed-list\n\n// .modal-open      - body class for killing the scroll\n// .modal           - container to scroll within\n// .modal-dialog    - positioning shell for the actual modal\n// .modal-content   - actual modal w/ bg and corners and stuff\n\n\n// Container that the modal scrolls within\n.modal {\n  // scss-docs-start modal-css-vars\n  --#{$prefix}modal-zindex: #{$zindex-modal};\n  --#{$prefix}modal-width: #{$modal-md};\n  --#{$prefix}modal-padding: #{$modal-inner-padding};\n  --#{$prefix}modal-margin: #{$modal-dialog-margin};\n  --#{$prefix}modal-color: #{$modal-content-color};\n  --#{$prefix}modal-bg: #{$modal-content-bg};\n  --#{$prefix}modal-border-color: #{$modal-content-border-color};\n  --#{$prefix}modal-border-width: #{$modal-content-border-width};\n  --#{$prefix}modal-border-radius: #{$modal-content-border-radius};\n  --#{$prefix}modal-box-shadow: #{$modal-content-box-shadow-xs};\n  --#{$prefix}modal-inner-border-radius: #{$modal-content-inner-border-radius};\n  --#{$prefix}modal-header-padding-x: #{$modal-header-padding-x};\n  --#{$prefix}modal-header-padding-y: #{$modal-header-padding-y};\n  --#{$prefix}modal-header-padding: #{$modal-header-padding}; // Todo in v6: Split this padding into x and y\n  --#{$prefix}modal-header-border-color: #{$modal-header-border-color};\n  --#{$prefix}modal-header-border-width: #{$modal-header-border-width};\n  --#{$prefix}modal-title-line-height: #{$modal-title-line-height};\n  --#{$prefix}modal-footer-gap: #{$modal-footer-margin-between};\n  --#{$prefix}modal-footer-bg: #{$modal-footer-bg};\n  --#{$prefix}modal-footer-border-color: #{$modal-footer-border-color};\n  --#{$prefix}modal-footer-border-width: #{$modal-footer-border-width};\n  // scss-docs-end modal-css-vars\n\n  position: fixed;\n  top: 0;\n  left: 0;\n  z-index: var(--#{$prefix}modal-zindex);\n  display: none;\n  width: 100%;\n  height: 100%;\n  overflow-x: hidden;\n  overflow-y: auto;\n  // Prevent Chrome on Windows from adding a focus outline. For details, see\n  // https://github.com/twbs/bootstrap/pull/10951.\n  outline: 0;\n  // We deliberately don't use `-webkit-overflow-scrolling: touch;` due to a\n  // gnarly iOS Safari bug: https://bugs.webkit.org/show_bug.cgi?id=158342\n  // See also https://github.com/twbs/bootstrap/issues/17695\n}\n\n// Shell div to position the modal with bottom padding\n.modal-dialog {\n  position: relative;\n  width: auto;\n  margin: var(--#{$prefix}modal-margin);\n  // allow clicks to pass through for custom click handling to close modal\n  pointer-events: none;\n\n  // When fading in the modal, animate it to slide down\n  .modal.fade & {\n    @include transition($modal-transition);\n    transform: $modal-fade-transform;\n  }\n  .modal.show & {\n    transform: $modal-show-transform;\n  }\n\n  // When trying to close, animate focus to scale\n  .modal.modal-static & {\n    transform: $modal-scale-transform;\n  }\n}\n\n.modal-dialog-scrollable {\n  height: calc(100% - var(--#{$prefix}modal-margin) * 2);\n\n  .modal-content {\n    max-height: 100%;\n    overflow: hidden;\n  }\n\n  .modal-body {\n    overflow-y: auto;\n  }\n}\n\n.modal-dialog-centered {\n  display: flex;\n  align-items: center;\n  min-height: calc(100% - var(--#{$prefix}modal-margin) * 2);\n}\n\n// Actual modal\n.modal-content {\n  position: relative;\n  display: flex;\n  flex-direction: column;\n  width: 100%; // Ensure `.modal-content` extends the full width of the parent `.modal-dialog`\n  // counteract the pointer-events: none; in the .modal-dialog\n  color: var(--#{$prefix}modal-color);\n  pointer-events: auto;\n  background-color: var(--#{$prefix}modal-bg);\n  background-clip: padding-box;\n  border: var(--#{$prefix}modal-border-width) solid var(--#{$prefix}modal-border-color);\n  @include border-radius(var(--#{$prefix}modal-border-radius));\n  @include box-shadow(var(--#{$prefix}modal-box-shadow));\n  // Remove focus outline from opened modal\n  outline: 0;\n}\n\n// Modal background\n.modal-backdrop {\n  // scss-docs-start modal-backdrop-css-vars\n  --#{$prefix}backdrop-zindex: #{$zindex-modal-backdrop};\n  --#{$prefix}backdrop-bg: #{$modal-backdrop-bg};\n  --#{$prefix}backdrop-opacity: #{$modal-backdrop-opacity};\n  // scss-docs-end modal-backdrop-css-vars\n\n  @include overlay-backdrop(var(--#{$prefix}backdrop-zindex), var(--#{$prefix}backdrop-bg), var(--#{$prefix}backdrop-opacity));\n}\n\n// Modal header\n// Top section of the modal w/ title and dismiss\n.modal-header {\n  display: flex;\n  flex-shrink: 0;\n  align-items: center;\n  justify-content: space-between; // Put modal header elements (title and dismiss) on opposite ends\n  padding: var(--#{$prefix}modal-header-padding);\n  border-bottom: var(--#{$prefix}modal-header-border-width) solid var(--#{$prefix}modal-header-border-color);\n  @include border-top-radius(var(--#{$prefix}modal-inner-border-radius));\n\n  .btn-close {\n    padding: calc(var(--#{$prefix}modal-header-padding-y) * .5) calc(var(--#{$prefix}modal-header-padding-x) * .5);\n    margin: calc(var(--#{$prefix}modal-header-padding-y) * -.5) calc(var(--#{$prefix}modal-header-padding-x) * -.5) calc(var(--#{$prefix}modal-header-padding-y) * -.5) auto;\n  }\n}\n\n// Title text within header\n.modal-title {\n  margin-bottom: 0;\n  line-height: var(--#{$prefix}modal-title-line-height);\n}\n\n// Modal body\n// Where all modal content resides (sibling of .modal-header and .modal-footer)\n.modal-body {\n  position: relative;\n  // Enable `flex-grow: 1` so that the body take up as much space as possible\n  // when there should be a fixed height on `.modal-dialog`.\n  flex: 1 1 auto;\n  padding: var(--#{$prefix}modal-padding);\n}\n\n// Footer (for actions)\n.modal-footer {\n  display: flex;\n  flex-shrink: 0;\n  flex-wrap: wrap;\n  align-items: center; // vertically center\n  justify-content: flex-end; // Right align buttons with flex property because text-align doesn't work on flex items\n  padding: calc(var(--#{$prefix}modal-padding) - var(--#{$prefix}modal-footer-gap) * .5);\n  background-color: var(--#{$prefix}modal-footer-bg);\n  border-top: var(--#{$prefix}modal-footer-border-width) solid var(--#{$prefix}modal-footer-border-color);\n  @include border-bottom-radius(var(--#{$prefix}modal-inner-border-radius));\n\n  // Place margin between footer elements\n  // This solution is far from ideal because of the universal selector usage,\n  // but is needed to fix https://github.com/twbs/bootstrap/issues/24800\n  > * {\n    margin: calc(var(--#{$prefix}modal-footer-gap) * .5); // Todo in v6: replace with gap on parent class\n  }\n}\n\n// Scale up the modal\n@include media-breakpoint-up(sm) {\n  .modal {\n    --#{$prefix}modal-margin: #{$modal-dialog-margin-y-sm-up};\n    --#{$prefix}modal-box-shadow: #{$modal-content-box-shadow-sm-up};\n  }\n\n  // Automatically set modal's width for larger viewports\n  .modal-dialog {\n    max-width: var(--#{$prefix}modal-width);\n    margin-right: auto;\n    margin-left: auto;\n  }\n\n  .modal-sm {\n    --#{$prefix}modal-width: #{$modal-sm};\n  }\n}\n\n@include media-breakpoint-up(lg) {\n  .modal-lg,\n  .modal-xl {\n    --#{$prefix}modal-width: #{$modal-lg};\n  }\n}\n\n@include media-breakpoint-up(xl) {\n  .modal-xl {\n    --#{$prefix}modal-width: #{$modal-xl};\n  }\n}\n\n// scss-docs-start modal-fullscreen-loop\n@each $breakpoint in map-keys($grid-breakpoints) {\n  $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n  $postfix: if($infix != \"\", $infix + \"-down\", \"\");\n\n  @include media-breakpoint-down($breakpoint) {\n    .modal-fullscreen#{$postfix} {\n      width: 100vw;\n      max-width: none;\n      height: 100%;\n      margin: 0;\n\n      .modal-content {\n        height: 100%;\n        border: 0;\n        @include border-radius(0);\n      }\n\n      .modal-header,\n      .modal-footer {\n        @include border-radius(0);\n      }\n\n      .modal-body {\n        overflow-y: auto;\n      }\n    }\n  }\n}\n// scss-docs-end modal-fullscreen-loop\n", "// Shared between modals and offcanvases\n@mixin overlay-backdrop($zindex, $backdrop-bg, $backdrop-opacity) {\n  position: fixed;\n  top: 0;\n  left: 0;\n  z-index: $zindex;\n  width: 100vw;\n  height: 100vh;\n  background-color: $backdrop-bg;\n\n  // Fade for backdrop\n  &.fade { opacity: 0; }\n  &.show { opacity: $backdrop-opacity; }\n}\n", "// Base class\n.tooltip {\n  // scss-docs-start tooltip-css-vars\n  --#{$prefix}tooltip-zindex: #{$zindex-tooltip};\n  --#{$prefix}tooltip-max-width: #{$tooltip-max-width};\n  --#{$prefix}tooltip-padding-x: #{$tooltip-padding-x};\n  --#{$prefix}tooltip-padding-y: #{$tooltip-padding-y};\n  --#{$prefix}tooltip-margin: #{$tooltip-margin};\n  @include rfs($tooltip-font-size, --#{$prefix}tooltip-font-size);\n  --#{$prefix}tooltip-color: #{$tooltip-color};\n  --#{$prefix}tooltip-bg: #{$tooltip-bg};\n  --#{$prefix}tooltip-border-radius: #{$tooltip-border-radius};\n  --#{$prefix}tooltip-opacity: #{$tooltip-opacity};\n  --#{$prefix}tooltip-arrow-width: #{$tooltip-arrow-width};\n  --#{$prefix}tooltip-arrow-height: #{$tooltip-arrow-height};\n  // scss-docs-end tooltip-css-vars\n\n  z-index: var(--#{$prefix}tooltip-zindex);\n  display: block;\n  padding: var(--#{$prefix}tooltip-arrow-height);\n  margin: var(--#{$prefix}tooltip-margin);\n  @include deprecate(\"`$tooltip-margin`\", \"v5\", \"v5.x\", true);\n  // Our parent element can be arbitrary since tooltips are by default inserted as a sibling of their target element.\n  // So reset our font and text properties to avoid inheriting weird values.\n  @include reset-text();\n  @include font-size(var(--#{$prefix}tooltip-font-size));\n  // Allow breaking very long words so they don't overflow the tooltip's bounds\n  word-wrap: break-word;\n  opacity: 0;\n\n  &.show { opacity: var(--#{$prefix}tooltip-opacity); }\n\n  .tooltip-arrow {\n    display: block;\n    width: var(--#{$prefix}tooltip-arrow-width);\n    height: var(--#{$prefix}tooltip-arrow-height);\n\n    &::before {\n      position: absolute;\n      content: \"\";\n      border-color: transparent;\n      border-style: solid;\n    }\n  }\n}\n\n.bs-tooltip-top .tooltip-arrow {\n  bottom: 0;\n\n  &::before {\n    top: -1px;\n    border-width: var(--#{$prefix}tooltip-arrow-height) calc(var(--#{$prefix}tooltip-arrow-width) * .5) 0; // stylelint-disable-line function-disallowed-list\n    border-top-color: var(--#{$prefix}tooltip-bg);\n  }\n}\n\n/* rtl:begin:ignore */\n.bs-tooltip-end .tooltip-arrow {\n  left: 0;\n  width: var(--#{$prefix}tooltip-arrow-height);\n  height: var(--#{$prefix}tooltip-arrow-width);\n\n  &::before {\n    right: -1px;\n    border-width: calc(var(--#{$prefix}tooltip-arrow-width) * .5) var(--#{$prefix}tooltip-arrow-height) calc(var(--#{$prefix}tooltip-arrow-width) * .5) 0; // stylelint-disable-line function-disallowed-list\n    border-right-color: var(--#{$prefix}tooltip-bg);\n  }\n}\n\n/* rtl:end:ignore */\n\n.bs-tooltip-bottom .tooltip-arrow {\n  top: 0;\n\n  &::before {\n    bottom: -1px;\n    border-width: 0 calc(var(--#{$prefix}tooltip-arrow-width) * .5) var(--#{$prefix}tooltip-arrow-height); // stylelint-disable-line function-disallowed-list\n    border-bottom-color: var(--#{$prefix}tooltip-bg);\n  }\n}\n\n/* rtl:begin:ignore */\n.bs-tooltip-start .tooltip-arrow {\n  right: 0;\n  width: var(--#{$prefix}tooltip-arrow-height);\n  height: var(--#{$prefix}tooltip-arrow-width);\n\n  &::before {\n    left: -1px;\n    border-width: calc(var(--#{$prefix}tooltip-arrow-width) * .5) 0 calc(var(--#{$prefix}tooltip-arrow-width) * .5) var(--#{$prefix}tooltip-arrow-height); // stylelint-disable-line function-disallowed-list\n    border-left-color: var(--#{$prefix}tooltip-bg);\n  }\n}\n\n/* rtl:end:ignore */\n\n.bs-tooltip-auto {\n  &[data-popper-placement^=\"top\"] {\n    @extend .bs-tooltip-top;\n  }\n  &[data-popper-placement^=\"right\"] {\n    @extend .bs-tooltip-end;\n  }\n  &[data-popper-placement^=\"bottom\"] {\n    @extend .bs-tooltip-bottom;\n  }\n  &[data-popper-placement^=\"left\"] {\n    @extend .bs-tooltip-start;\n  }\n}\n\n// Wrapper for the tooltip content\n.tooltip-inner {\n  max-width: var(--#{$prefix}tooltip-max-width);\n  padding: var(--#{$prefix}tooltip-padding-y) var(--#{$prefix}tooltip-padding-x);\n  color: var(--#{$prefix}tooltip-color);\n  text-align: center;\n  background-color: var(--#{$prefix}tooltip-bg);\n  @include border-radius(var(--#{$prefix}tooltip-border-radius));\n}\n", "@mixin reset-text {\n  font-family: $font-family-base;\n  // We deliberately do NOT reset font-size or overflow-wrap / word-wrap.\n  font-style: normal;\n  font-weight: $font-weight-normal;\n  line-height: $line-height-base;\n  text-align: left; // Fallback for where `start` is not supported\n  text-align: start;\n  text-decoration: none;\n  text-shadow: none;\n  text-transform: none;\n  letter-spacing: normal;\n  word-break: normal;\n  white-space: normal;\n  word-spacing: normal;\n  line-break: auto;\n}\n", ".popover {\n  // scss-docs-start popover-css-vars\n  --#{$prefix}popover-zindex: #{$zindex-popover};\n  --#{$prefix}popover-max-width: #{$popover-max-width};\n  @include rfs($popover-font-size, --#{$prefix}popover-font-size);\n  --#{$prefix}popover-bg: #{$popover-bg};\n  --#{$prefix}popover-border-width: #{$popover-border-width};\n  --#{$prefix}popover-border-color: #{$popover-border-color};\n  --#{$prefix}popover-border-radius: #{$popover-border-radius};\n  --#{$prefix}popover-inner-border-radius: #{$popover-inner-border-radius};\n  --#{$prefix}popover-box-shadow: #{$popover-box-shadow};\n  --#{$prefix}popover-header-padding-x: #{$popover-header-padding-x};\n  --#{$prefix}popover-header-padding-y: #{$popover-header-padding-y};\n  @include rfs($popover-header-font-size, --#{$prefix}popover-header-font-size);\n  --#{$prefix}popover-header-color: #{$popover-header-color};\n  --#{$prefix}popover-header-bg: #{$popover-header-bg};\n  --#{$prefix}popover-body-padding-x: #{$popover-body-padding-x};\n  --#{$prefix}popover-body-padding-y: #{$popover-body-padding-y};\n  --#{$prefix}popover-body-color: #{$popover-body-color};\n  --#{$prefix}popover-arrow-width: #{$popover-arrow-width};\n  --#{$prefix}popover-arrow-height: #{$popover-arrow-height};\n  --#{$prefix}popover-arrow-border: var(--#{$prefix}popover-border-color);\n  // scss-docs-end popover-css-vars\n\n  z-index: var(--#{$prefix}popover-zindex);\n  display: block;\n  max-width: var(--#{$prefix}popover-max-width);\n  // Our parent element can be arbitrary since tooltips are by default inserted as a sibling of their target element.\n  // So reset our font and text properties to avoid inheriting weird values.\n  @include reset-text();\n  @include font-size(var(--#{$prefix}popover-font-size));\n  // Allow breaking very long words so they don't overflow the popover's bounds\n  word-wrap: break-word;\n  background-color: var(--#{$prefix}popover-bg);\n  background-clip: padding-box;\n  border: var(--#{$prefix}popover-border-width) solid var(--#{$prefix}popover-border-color);\n  @include border-radius(var(--#{$prefix}popover-border-radius));\n  @include box-shadow(var(--#{$prefix}popover-box-shadow));\n\n  .popover-arrow {\n    display: block;\n    width: var(--#{$prefix}popover-arrow-width);\n    height: var(--#{$prefix}popover-arrow-height);\n\n    &::before,\n    &::after {\n      position: absolute;\n      display: block;\n      content: \"\";\n      border-color: transparent;\n      border-style: solid;\n      border-width: 0;\n    }\n  }\n}\n\n.bs-popover-top {\n  > .popover-arrow {\n    bottom: calc((var(--#{$prefix}popover-arrow-height) * -1) - var(--#{$prefix}popover-border-width)); // stylelint-disable-line function-disallowed-list\n\n    &::before,\n    &::after {\n      border-width: var(--#{$prefix}popover-arrow-height) calc(var(--#{$prefix}popover-arrow-width) * .5) 0; // stylelint-disable-line function-disallowed-list\n    }\n\n    &::before {\n      bottom: 0;\n      border-top-color: var(--#{$prefix}popover-arrow-border);\n    }\n\n    &::after {\n      bottom: var(--#{$prefix}popover-border-width);\n      border-top-color: var(--#{$prefix}popover-bg);\n    }\n  }\n}\n\n/* rtl:begin:ignore */\n.bs-popover-end {\n  > .popover-arrow {\n    left: calc((var(--#{$prefix}popover-arrow-height) * -1) - var(--#{$prefix}popover-border-width)); // stylelint-disable-line function-disallowed-list\n    width: var(--#{$prefix}popover-arrow-height);\n    height: var(--#{$prefix}popover-arrow-width);\n\n    &::before,\n    &::after {\n      border-width: calc(var(--#{$prefix}popover-arrow-width) * .5) var(--#{$prefix}popover-arrow-height) calc(var(--#{$prefix}popover-arrow-width) * .5) 0; // stylelint-disable-line function-disallowed-list\n    }\n\n    &::before {\n      left: 0;\n      border-right-color: var(--#{$prefix}popover-arrow-border);\n    }\n\n    &::after {\n      left: var(--#{$prefix}popover-border-width);\n      border-right-color: var(--#{$prefix}popover-bg);\n    }\n  }\n}\n\n/* rtl:end:ignore */\n\n.bs-popover-bottom {\n  > .popover-arrow {\n    top: calc((var(--#{$prefix}popover-arrow-height) * -1) - var(--#{$prefix}popover-border-width)); // stylelint-disable-line function-disallowed-list\n\n    &::before,\n    &::after {\n      border-width: 0 calc(var(--#{$prefix}popover-arrow-width) * .5) var(--#{$prefix}popover-arrow-height); // stylelint-disable-line function-disallowed-list\n    }\n\n    &::before {\n      top: 0;\n      border-bottom-color: var(--#{$prefix}popover-arrow-border);\n    }\n\n    &::after {\n      top: var(--#{$prefix}popover-border-width);\n      border-bottom-color: var(--#{$prefix}popover-bg);\n    }\n  }\n\n  // This will remove the popover-header's border just below the arrow\n  .popover-header::before {\n    position: absolute;\n    top: 0;\n    left: 50%;\n    display: block;\n    width: var(--#{$prefix}popover-arrow-width);\n    margin-left: calc(var(--#{$prefix}popover-arrow-width) * -.5); // stylelint-disable-line function-disallowed-list\n    content: \"\";\n    border-bottom: var(--#{$prefix}popover-border-width) solid var(--#{$prefix}popover-header-bg);\n  }\n}\n\n/* rtl:begin:ignore */\n.bs-popover-start {\n  > .popover-arrow {\n    right: calc((var(--#{$prefix}popover-arrow-height) * -1) - var(--#{$prefix}popover-border-width)); // stylelint-disable-line function-disallowed-list\n    width: var(--#{$prefix}popover-arrow-height);\n    height: var(--#{$prefix}popover-arrow-width);\n\n    &::before,\n    &::after {\n      border-width: calc(var(--#{$prefix}popover-arrow-width) * .5) 0 calc(var(--#{$prefix}popover-arrow-width) * .5) var(--#{$prefix}popover-arrow-height); // stylelint-disable-line function-disallowed-list\n    }\n\n    &::before {\n      right: 0;\n      border-left-color: var(--#{$prefix}popover-arrow-border);\n    }\n\n    &::after {\n      right: var(--#{$prefix}popover-border-width);\n      border-left-color: var(--#{$prefix}popover-bg);\n    }\n  }\n}\n\n/* rtl:end:ignore */\n\n.bs-popover-auto {\n  &[data-popper-placement^=\"top\"] {\n    @extend .bs-popover-top;\n  }\n  &[data-popper-placement^=\"right\"] {\n    @extend .bs-popover-end;\n  }\n  &[data-popper-placement^=\"bottom\"] {\n    @extend .bs-popover-bottom;\n  }\n  &[data-popper-placement^=\"left\"] {\n    @extend .bs-popover-start;\n  }\n}\n\n// Offset the popover to account for the popover arrow\n.popover-header {\n  padding: var(--#{$prefix}popover-header-padding-y) var(--#{$prefix}popover-header-padding-x);\n  margin-bottom: 0; // Reset the default from Reboot\n  @include font-size(var(--#{$prefix}popover-header-font-size));\n  color: var(--#{$prefix}popover-header-color);\n  background-color: var(--#{$prefix}popover-header-bg);\n  border-bottom: var(--#{$prefix}popover-border-width) solid var(--#{$prefix}popover-border-color);\n  @include border-top-radius(var(--#{$prefix}popover-inner-border-radius));\n\n  &:empty {\n    display: none;\n  }\n}\n\n.popover-body {\n  padding: var(--#{$prefix}popover-body-padding-y) var(--#{$prefix}popover-body-padding-x);\n  color: var(--#{$prefix}popover-body-color);\n}\n", "// Notes on the classes:\n//\n// 1. .carousel.pointer-event should ideally be pan-y (to allow for users to scroll vertically)\n//    even when their scroll action started on a carousel, but for compatibility (with Firefox)\n//    we're preventing all actions instead\n// 2. The .carousel-item-start and .carousel-item-end is used to indicate where\n//    the active slide is heading.\n// 3. .active.carousel-item is the current slide.\n// 4. .active.carousel-item-start and .active.carousel-item-end is the current\n//    slide in its in-transition state. Only one of these occurs at a time.\n// 5. .carousel-item-next.carousel-item-start and .carousel-item-prev.carousel-item-end\n//    is the upcoming slide in transition.\n\n.carousel {\n  position: relative;\n}\n\n.carousel.pointer-event {\n  touch-action: pan-y;\n}\n\n.carousel-inner {\n  position: relative;\n  width: 100%;\n  overflow: hidden;\n  @include clearfix();\n}\n\n.carousel-item {\n  position: relative;\n  display: none;\n  float: left;\n  width: 100%;\n  margin-right: -100%;\n  backface-visibility: hidden;\n  @include transition($carousel-transition);\n}\n\n.carousel-item.active,\n.carousel-item-next,\n.carousel-item-prev {\n  display: block;\n}\n\n/* rtl:begin:ignore */\n.carousel-item-next:not(.carousel-item-start),\n.active.carousel-item-end {\n  transform: translateX(100%);\n}\n\n.carousel-item-prev:not(.carousel-item-end),\n.active.carousel-item-start {\n  transform: translateX(-100%);\n}\n\n/* rtl:end:ignore */\n\n\n//\n// Alternate transitions\n//\n\n.carousel-fade {\n  .carousel-item {\n    opacity: 0;\n    transition-property: opacity;\n    transform: none;\n  }\n\n  .carousel-item.active,\n  .carousel-item-next.carousel-item-start,\n  .carousel-item-prev.carousel-item-end {\n    z-index: 1;\n    opacity: 1;\n  }\n\n  .active.carousel-item-start,\n  .active.carousel-item-end {\n    z-index: 0;\n    opacity: 0;\n    @include transition(opacity 0s $carousel-transition-duration);\n  }\n}\n\n\n//\n// Left/right controls for nav\n//\n\n.carousel-control-prev,\n.carousel-control-next {\n  position: absolute;\n  top: 0;\n  bottom: 0;\n  z-index: 1;\n  // Use flex for alignment (1-3)\n  display: flex; // 1. allow flex styles\n  align-items: center; // 2. vertically center contents\n  justify-content: center; // 3. horizontally center contents\n  width: $carousel-control-width;\n  padding: 0;\n  color: $carousel-control-color;\n  text-align: center;\n  background: none;\n  border: 0;\n  opacity: $carousel-control-opacity;\n  @include transition($carousel-control-transition);\n\n  // Hover/focus state\n  &:hover,\n  &:focus {\n    color: $carousel-control-color;\n    text-decoration: none;\n    outline: 0;\n    opacity: $carousel-control-hover-opacity;\n  }\n}\n.carousel-control-prev {\n  left: 0;\n  background-image: if($enable-gradients, linear-gradient(90deg, rgba($black, .25), rgba($black, .001)), null);\n}\n.carousel-control-next {\n  right: 0;\n  background-image: if($enable-gradients, linear-gradient(270deg, rgba($black, .25), rgba($black, .001)), null);\n}\n\n// Icons for within\n.carousel-control-prev-icon,\n.carousel-control-next-icon {\n  display: inline-block;\n  width: $carousel-control-icon-width;\n  height: $carousel-control-icon-width;\n  background-repeat: no-repeat;\n  background-position: 50%;\n  background-size: 100% 100%;\n}\n\n/* rtl:options: {\n  \"autoRename\": true,\n  \"stringMap\":[ {\n    \"name\"    : \"prev-next\",\n    \"search\"  : \"prev\",\n    \"replace\" : \"next\"\n  } ]\n} */\n.carousel-control-prev-icon {\n  background-image: escape-svg($carousel-control-prev-icon-bg);\n}\n.carousel-control-next-icon {\n  background-image: escape-svg($carousel-control-next-icon-bg);\n}\n\n// Optional indicator pips/controls\n//\n// Add a container (such as a list) with the following class and add an item (ideally a focusable control,\n// like a button) with data-bs-target for each slide your carousel holds.\n\n.carousel-indicators {\n  position: absolute;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  z-index: 2;\n  display: flex;\n  justify-content: center;\n  padding: 0;\n  // Use the .carousel-control's width as margin so we don't overlay those\n  margin-right: $carousel-control-width;\n  margin-bottom: 1rem;\n  margin-left: $carousel-control-width;\n  list-style: none;\n\n  [data-bs-target] {\n    box-sizing: content-box;\n    flex: 0 1 auto;\n    width: $carousel-indicator-width;\n    height: $carousel-indicator-height;\n    padding: 0;\n    margin-right: $carousel-indicator-spacer;\n    margin-left: $carousel-indicator-spacer;\n    text-indent: -999px;\n    cursor: pointer;\n    background-color: $carousel-indicator-active-bg;\n    background-clip: padding-box;\n    border: 0;\n    // Use transparent borders to increase the hit area by 10px on top and bottom.\n    border-top: $carousel-indicator-hit-area-height solid transparent;\n    border-bottom: $carousel-indicator-hit-area-height solid transparent;\n    opacity: $carousel-indicator-opacity;\n    @include transition($carousel-indicator-transition);\n  }\n\n  .active {\n    opacity: $carousel-indicator-active-opacity;\n  }\n}\n\n\n// Optional captions\n//\n//\n\n.carousel-caption {\n  position: absolute;\n  right: (100% - $carousel-caption-width) * .5;\n  bottom: $carousel-caption-spacer;\n  left: (100% - $carousel-caption-width) * .5;\n  padding-top: $carousel-caption-padding-y;\n  padding-bottom: $carousel-caption-padding-y;\n  color: $carousel-caption-color;\n  text-align: center;\n}\n\n// Dark mode carousel\n\n.carousel-dark {\n  .carousel-control-prev-icon,\n  .carousel-control-next-icon {\n    filter: $carousel-dark-control-icon-filter;\n  }\n\n  .carousel-indicators [data-bs-target] {\n    background-color: $carousel-dark-indicator-active-bg;\n  }\n\n  .carousel-caption {\n    color: $carousel-dark-caption-color;\n  }\n}\n", "// scss-docs-start clearfix\n@mixin clearfix() {\n  &::after {\n    display: block;\n    clear: both;\n    content: \"\";\n  }\n}\n// scss-docs-end clearfix\n", "//\n// Rotating border\n//\n\n.spinner-grow,\n.spinner-border {\n  display: inline-block;\n  width: var(--#{$prefix}spinner-width);\n  height: var(--#{$prefix}spinner-height);\n  vertical-align: var(--#{$prefix}spinner-vertical-align);\n  // stylelint-disable-next-line property-disallowed-list\n  border-radius: 50%;\n  animation: var(--#{$prefix}spinner-animation-speed) linear infinite var(--#{$prefix}spinner-animation-name);\n}\n\n// scss-docs-start spinner-border-keyframes\n@keyframes spinner-border {\n  to { transform: rotate(360deg) #{\"/* rtl:ignore */\"}; }\n}\n// scss-docs-end spinner-border-keyframes\n\n.spinner-border {\n  // scss-docs-start spinner-border-css-vars\n  --#{$prefix}spinner-width: #{$spinner-width};\n  --#{$prefix}spinner-height: #{$spinner-height};\n  --#{$prefix}spinner-vertical-align: #{$spinner-vertical-align};\n  --#{$prefix}spinner-border-width: #{$spinner-border-width};\n  --#{$prefix}spinner-animation-speed: #{$spinner-animation-speed};\n  --#{$prefix}spinner-animation-name: spinner-border;\n  // scss-docs-end spinner-border-css-vars\n\n  border: var(--#{$prefix}spinner-border-width) solid currentcolor;\n  border-right-color: transparent;\n}\n\n.spinner-border-sm {\n  // scss-docs-start spinner-border-sm-css-vars\n  --#{$prefix}spinner-width: #{$spinner-width-sm};\n  --#{$prefix}spinner-height: #{$spinner-height-sm};\n  --#{$prefix}spinner-border-width: #{$spinner-border-width-sm};\n  // scss-docs-end spinner-border-sm-css-vars\n}\n\n//\n// Growing circle\n//\n\n// scss-docs-start spinner-grow-keyframes\n@keyframes spinner-grow {\n  0% {\n    transform: scale(0);\n  }\n  50% {\n    opacity: 1;\n    transform: none;\n  }\n}\n// scss-docs-end spinner-grow-keyframes\n\n.spinner-grow {\n  // scss-docs-start spinner-grow-css-vars\n  --#{$prefix}spinner-width: #{$spinner-width};\n  --#{$prefix}spinner-height: #{$spinner-height};\n  --#{$prefix}spinner-vertical-align: #{$spinner-vertical-align};\n  --#{$prefix}spinner-animation-speed: #{$spinner-animation-speed};\n  --#{$prefix}spinner-animation-name: spinner-grow;\n  // scss-docs-end spinner-grow-css-vars\n\n  background-color: currentcolor;\n  opacity: 0;\n}\n\n.spinner-grow-sm {\n  --#{$prefix}spinner-width: #{$spinner-width-sm};\n  --#{$prefix}spinner-height: #{$spinner-height-sm};\n}\n\n@if $enable-reduced-motion {\n  @media (prefers-reduced-motion: reduce) {\n    .spinner-border,\n    .spinner-grow {\n      --#{$prefix}spinner-animation-speed: #{$spinner-animation-speed * 2};\n    }\n  }\n}\n", "// stylelint-disable function-name-case\n\n// All-caps `RGBA()` function used because of this Sass bug: https://github.com/sass/node-sass/issues/2251\n@each $color, $value in $theme-colors {\n  $color-rgb: to-rgb($value);\n  .text-bg-#{$color} {\n    color: color-contrast($value) if($enable-important-utilities, !important, null);\n    background-color: RGBA($color-rgb, var(--#{$prefix}bg-opacity, 1)) if($enable-important-utilities, !important, null);\n  }\n}\n", "@each $color, $value in $theme-colors {\n  .link-#{$color} {\n    color: $value !important; // stylelint-disable-line declaration-no-important\n\n    @if $link-shade-percentage != 0 {\n      &:hover,\n      &:focus {\n        color: if(color-contrast($value) == $color-contrast-light, shade-color($value, $link-shade-percentage), tint-color($value, $link-shade-percentage)) !important; // stylelint-disable-line declaration-no-important\n      }\n    }\n  }\n}\n", "// Credit: <PERSON> and <PERSON><PERSON><PERSON> CSS.\n\n.ratio {\n  position: relative;\n  width: 100%;\n\n  &::before {\n    display: block;\n    padding-top: var(--#{$prefix}aspect-ratio);\n    content: \"\";\n  }\n\n  > * {\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n  }\n}\n\n@each $key, $ratio in $aspect-ratios {\n  .ratio-#{$key} {\n    --#{$prefix}aspect-ratio: #{$ratio};\n  }\n}\n", "// Shorthand\n\n.fixed-top {\n  position: fixed;\n  top: 0;\n  right: 0;\n  left: 0;\n  z-index: $zindex-fixed;\n}\n\n.fixed-bottom {\n  position: fixed;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  z-index: $zindex-fixed;\n}\n\n// Responsive sticky top and bottom\n@each $breakpoint in map-keys($grid-breakpoints) {\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    .sticky#{$infix}-top {\n      position: sticky;\n      top: 0;\n      z-index: $zindex-sticky;\n    }\n\n    .sticky#{$infix}-bottom {\n      position: sticky;\n      bottom: 0;\n      z-index: $zindex-sticky;\n    }\n  }\n}\n", "// scss-docs-start stacks\n.hstack {\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  align-self: stretch;\n}\n\n.vstack {\n  display: flex;\n  flex: 1 1 auto;\n  flex-direction: column;\n  align-self: stretch;\n}\n// scss-docs-end stacks\n", "//\n// Visually hidden\n//\n\n.visually-hidden,\n.visually-hidden-focusable:not(:focus):not(:focus-within) {\n  @include visually-hidden();\n}\n", "// stylelint-disable declaration-no-important\n\n// Hide content visually while keeping it accessible to assistive technologies\n//\n// See: https://www.a11yproject.com/posts/2013-01-11-how-to-hide-content/\n// See: https://kittygiraudel.com/2016/10/13/css-hide-and-seek/\n\n@mixin visually-hidden() {\n  position: absolute !important;\n  width: 1px !important;\n  height: 1px !important;\n  padding: 0 !important;\n  margin: -1px !important; // Fix for https://github.com/twbs/bootstrap/issues/25686\n  overflow: hidden !important;\n  clip: rect(0, 0, 0, 0) !important;\n  white-space: nowrap !important;\n  border: 0 !important;\n}\n\n// Use to only display content when it's focused, or one of its child elements is focused\n// (i.e. when focus is within the element/container that the class was applied to)\n//\n// Useful for \"Skip to main content\" links; see https://www.w3.org/TR/2013/NOTE-WCAG20-TECHS-20130905/G1\n\n@mixin visually-hidden-focusable() {\n  &:not(:focus):not(:focus-within) {\n    @include visually-hidden();\n  }\n}\n", "//\n// Stretched link\n//\n\n.stretched-link {\n  &::#{$stretched-link-pseudo-element} {\n    position: absolute;\n    top: 0;\n    right: 0;\n    bottom: 0;\n    left: 0;\n    z-index: $stretched-link-z-index;\n    content: \"\";\n  }\n}\n", "//\n// Text truncation\n//\n\n.text-truncate {\n  @include text-truncate();\n}\n", "// Text truncate\n// Requires inline-block or block for proper styling\n\n@mixin text-truncate() {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n", ".vr {\n  display: inline-block;\n  align-self: stretch;\n  width: 1px;\n  min-height: 1em;\n  background-color: currentcolor;\n  opacity: $hr-opacity;\n}\n", "// Utility generator\n// Used to generate utilities & print utilities\n@mixin generate-utility($utility, $infix, $is-rfs-media-query: false) {\n  $values: map-get($utility, values);\n\n  // If the values are a list or string, convert it into a map\n  @if type-of($values) == \"string\" or type-of(nth($values, 1)) != \"list\" {\n    $values: zip($values, $values);\n  }\n\n  @each $key, $value in $values {\n    $properties: map-get($utility, property);\n\n    // Multiple properties are possible, for example with vertical or horizontal margins or paddings\n    @if type-of($properties) == \"string\" {\n      $properties: append((), $properties);\n    }\n\n    // Use custom class if present\n    $property-class: if(map-has-key($utility, class), map-get($utility, class), nth($properties, 1));\n    $property-class: if($property-class == null, \"\", $property-class);\n\n    // Use custom CSS variable name if present, otherwise default to `class`\n    $css-variable-name: if(map-has-key($utility, css-variable-name), map-get($utility, css-variable-name), map-get($utility, class));\n\n    // State params to generate pseudo-classes\n    $state: if(map-has-key($utility, state), map-get($utility, state), ());\n\n    $infix: if($property-class == \"\" and str-slice($infix, 1, 1) == \"-\", str-slice($infix, 2), $infix);\n\n    // Don't prefix if value key is null (eg. with shadow class)\n    $property-class-modifier: if($key, if($property-class == \"\" and $infix == \"\", \"\", \"-\") + $key, \"\");\n\n    @if map-get($utility, rfs) {\n      // Inside the media query\n      @if $is-rfs-media-query {\n        $val: rfs-value($value);\n\n        // Do not render anything if fluid and non fluid values are the same\n        $value: if($val == rfs-fluid-value($value), null, $val);\n      }\n      @else {\n        $value: rfs-fluid-value($value);\n      }\n    }\n\n    $is-css-var: map-get($utility, css-var);\n    $is-local-vars: map-get($utility, local-vars);\n    $is-rtl: map-get($utility, rtl);\n\n    @if $value != null {\n      @if $is-rtl == false {\n        /* rtl:begin:remove */\n      }\n\n      @if $is-css-var {\n        .#{$property-class + $infix + $property-class-modifier} {\n          --#{$prefix}#{$css-variable-name}: #{$value};\n        }\n\n        @each $pseudo in $state {\n          .#{$property-class + $infix + $property-class-modifier}-#{$pseudo}:#{$pseudo} {\n            --#{$prefix}#{$css-variable-name}: #{$value};\n          }\n        }\n      } @else {\n        .#{$property-class + $infix + $property-class-modifier} {\n          @each $property in $properties {\n            @if $is-local-vars {\n              @each $local-var, $variable in $is-local-vars {\n                --#{$prefix}#{$local-var}: #{$variable};\n              }\n            }\n            #{$property}: $value if($enable-important-utilities, !important, null);\n          }\n        }\n\n        @each $pseudo in $state {\n          .#{$property-class + $infix + $property-class-modifier}-#{$pseudo}:#{$pseudo} {\n            @each $property in $properties {\n              @if $is-local-vars {\n                @each $local-var, $variable in $is-local-vars {\n                  --#{$prefix}#{$local-var}: #{$variable};\n                }\n              }\n              #{$property}: $value if($enable-important-utilities, !important, null);\n            }\n          }\n        }\n      }\n\n      @if $is-rtl == false {\n        /* rtl:end:remove */\n      }\n    }\n  }\n}\n", "// Loop over each breakpoint\n@each $breakpoint in map-keys($grid-breakpoints) {\n\n  // Generate media query if needed\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    // Loop over each utility property\n    @each $key, $utility in $utilities {\n      // The utility can be disabled with `false`, thus check if the utility is a map first\n      // Only proceed if responsive media queries are enabled or if it's the base media query\n      @if type-of($utility) == \"map\" and (map-get($utility, responsive) or $infix == \"\") {\n        @include generate-utility($utility, $infix);\n      }\n    }\n  }\n}\n\n// RFS rescaling\n@media (min-width: $rfs-mq-value) {\n  @each $breakpoint in map-keys($grid-breakpoints) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    @if (map-get($grid-breakpoints, $breakpoint) < $rfs-breakpoint) {\n      // Loop over each utility property\n      @each $key, $utility in $utilities {\n        // The utility can be disabled with `false`, thus check if the utility is a map first\n        // Only proceed if responsive media queries are enabled or if it's the base media query\n        @if type-of($utility) == \"map\" and map-get($utility, rfs) and (map-get($utility, responsive) or $infix == \"\") {\n          @include generate-utility($utility, $infix, true);\n        }\n      }\n    }\n  }\n}\n\n\n// Print utilities\n@media print {\n  @each $key, $utility in $utilities {\n    // The utility can be disabled with `false`, thus check if the utility is a map first\n    // Then check if the utility needs print styles\n    @if type-of($utility) == \"map\" and map-get($utility, print) == true {\n      @include generate-utility($utility, \"-print\");\n    }\n  }\n}\n", "// Animation mixins \n\n@keyframes dropdownAnimation {\n  from {\n    opacity: 0;\n    transform: translate3d(0, 20px, 0);\n  }\n  to {\n    opacity: 1;\n    transform: none;\n    transform: translate3d(0, 0px , 0);\n  }\n}\n\n.dropdownAnimation {\n  -webkit-animation-name: dropdownAnimation;\n          animation-name: dropdownAnimation;\n  -webkit-animation-duration: $action-transition-duration;\n  animation-duration: $action-transition-duration;\n  -webkit-animation-fill-mode: both;\n  animation-fill-mode: both;\n}\n\n@keyframes fadeOut {\n  from {\n    opacity: 1;\n  }\n  to {\n    opacity: 0;\n  }\n}\n\n.fadeOut {\n  animation-name: fadeOUt;\n}\n\n@keyframes fadeInUp {\n  from {\n    opacity: 0;\n    transform: translate3d(0, 100%, 0);\n  }\n  to {\n    opacity: 1;\n    transform: none;\n  }\n}\n\n.fadeInUp {\n  animation-name: fadeInUp;\n}\n\n.infinite-spin {\n  @keyframes spin {\n    from {\n      transform: rotate(0deg);\n    }\n    to {\n      transform: rotate(360deg);\n    }\n  }\n  animation-name: spin;\n  animation-duration: 3s;\n  animation-iteration-count: infinite;\n  animation-timing-function: linear;\n}\n\n@mixin transition($settings) {\n  -webkit-transition: $settings;\n  -moz-transition: $settings;\n  -ms-transition: $settings;\n  -o-transition: $settings;\n  transition: $settings;\n}\n\n.pulse {\n  $dim : 7px;\n  $mult : 4;\n  @keyframes pulse{\n    0% {\n      opacity: 1;\n      width: $dim;\n      height: $dim;\n      left: 0;\n      top: 0;\n    }\n    \n    95% {\n      opacity: 0.1;\n      left: -(($dim * $mult) - $dim) * .5;\n      top: -(($dim * $mult) - $dim) * .5;\n      width: $dim * $mult;\n      height: $dim * $mult;\n    }\n    \n    100% {\n      opacity: 0;\n      width: $dim;\n      height: $dim;\n      left: 0;\n      top: 0;\n    }\n  }\n  animation-name: pulse;\n  animation-duration: .9s;\n  animation-iteration-count: infinite;\n  animation-timing-function: ease-out;\n}", "//width mixin\n@mixin make-width($num, $viewport: \"\") {\n  $p: $num + \"%\";\n\n  @if $viewport == \"\" {\n    $viewport: \"-\";\n  } @else {\n    $viewport: \"-\" + $viewport + \"-\";\n  }\n\n  .wd#{$viewport}#{$num} { width: #{$num}px; }\n  .wd#{$viewport}#{$num}p { width: #{$p}; }\n  .mx-wd#{$viewport}#{$num}p { max-width: #{$p}; }\n  .mn-wd#{$viewport}#{$num}p { min-width: #{$p}; }\n\n  .wd#{$viewport}#{$num}-f { width: #{$num}px !important; }\n  .wd#{$viewport}#{$num}p-f { width: #{$p} !important; }\n  .mx-wd#{$viewport}#{$num}p-f { max-width: #{$p} !important; }\n  .mn-wd#{$viewport}#{$num}p-f { min-width: #{$p} !important; }\n}\n\n$num: 5;\n@while $num <= 100 {\n  @include make-width($num);\n  $num: $num + 5;\n}\n\n$num: 150;\n@while $num <= 1000 {\n  .wd-#{$num} { width: #{$num}px; }\n  .wd-#{$num}-f { width: #{$num}px !important; }\n  $num: $num + 50;\n}\n\n@mixin do-make-width($viewport, $num, $max, $plus) {\n  @while $num <= $max {\n    @include make-width($num,$viewport);\n    $num: $num + $plus;\n  }\n}\n\n@media (min-width: 480px) {\n  @include do-make-width(\"xs\",5,100,5);\n  @include do-make-width(\"xs\",150,1000,50);\n\n  .wd-xs-auto { width: auto; }\n  .wd-xs-auto-f { width: auto !important; }\n}\n\n@include media-breakpoint-up(sm) {\n  @include do-make-width(\"sm\",5,100,5);\n  @include do-make-width(\"sm\",150,1000,50);\n\n  .wd-sm-auto { width: auto; }\n  .wd-sm-auto-f { width: auto !important; }\n}\n\n@include media-breakpoint-up(md) {\n  @include do-make-width(\"md\",5,100,5);\n  @include do-make-width(\"md\",150,1000,50);\n\n  .wd-md-auto { width: auto; }\n  .wd-md-auto-f { width: auto !important; }\n\n  //Custom\n  .wd-md-120 { width: 120px; }\n}\n\n@include media-breakpoint-up(lg) {\n  @include do-make-width(\"lg\",5,100,5);\n  @include do-make-width(\"lg\",150,1000,50);\n\n  .wd-lg-auto { width: auto; }\n  .wd-lg-auto-f { width: auto !important; }\n}\n\n@include media-breakpoint-up(xl) {\n  @include do-make-width(\"xl\",5,100,5);\n  @include do-make-width(\"xl\",150,1000,50);\n\n  .wd-xl-auto { width: auto; }\n  .wd-xl-auto { width: auto !important; }\n}\n\n@include media-breakpoint-up(xxl) {\n  @include do-make-width(\"xxl\",5,100,5);\n  @include do-make-width(\"xxl\",150,1000,50);\n\n  .wd-xxl-auto { width: auto; }\n  .wd-xxl-auto { width: auto !important; }\n}", "@each $color, $value in $social-colors {\n  .bg-#{$color} {\n    background: social-color($color);\n  }\n}", "// Reste styles\n\nbody {\n  margin: 0;\n  padding: 0;\n}\n\n.btn,\n.btn-group.open .dropdown-toggle,\n.btn:active,\n.btn:focus,\n.btn:hover,\n.btn:visited,\na,\na:active,\na:checked,\na:focus,\na:hover,\na:visited,\nbody,\nbutton,\nbutton:active,\nbutton:hover,\nbutton:visited,\ndiv,\ninput,\ninput:active,\ninput:hover,\ninput:focus,\ninput:visited,\nselect,\nselect:active,\nselect:focus,\nselect:visited,\ntextarea,\ntextarea:active,\ntextarea:focus,\ntextarea:visited {\n  -webkit-box-shadow: none;\n  -moz-box-shadow: none;\n  box-shadow: none;\n}\n\nselect, \n.form-check-input {\n  -webkit-appearance: none;\n  -moz-appearance: none;\n}\n\ninput:-webkit-autofill,\ninput:-webkit-autofill:hover, \ninput:-webkit-autofill:focus, \ninput:-webkit-autofill:active  {\n  -webkit-box-shadow: 0 0 0 30px $input-bg inset;\n  -webkit-text-fill-color: $body-color;\n}\n", "// Miscellaneous\n\n*:-moz-full-screen,\n*:-webkit-full-screen,\n*:fullscreen *:-ms-full-screen {\n  overflow: auto;\n}\n\n\npre {\n  background-color: color(gray-lighter);\n  padding: 15px;\n  font-size: 14px;\n}\n\ncode {\n  padding: 5px;\n  font-family: $font-family-sans-serif;\n  font-weight: 400;\n  font-size: $font-size-base;\n  border-radius: 4px;\n}", "// Utilities \n\n.grid-margin {\n  margin-bottom: 1.5rem;\n}\n.grid-margin-sm-0 {\n  @media (min-width: 576px) {\n    margin-bottom: 0;\n  }\n}\n.grid-margin-md-0 {\n  @media (min-width: 768px) {\n    margin-bottom: 0;\n  }\n}\n.grid-margin-lg-0 {\n  @media (min-width: 992px) {\n    margin-bottom: 0;\n  }\n}\n.grid-margin-xl-0 {\n  @media (min-width: 1200px) {\n    margin-bottom: 0;\n  }\n}\n\n\n\n.stretch-card {\n  display: flex;\n  align-items: stretch;\n  justify-content: stretch;\n  >.card{\n    width: 100%;\n    min-width: 100%;\n  }\n}\n\n\n\n.img-lg {\n  width: 92px;\n  height: 92px;\n}\n.img-md {\n  width: 75px;\n  height: 92px;\n}\n.img-sm {\n  width: 43px;\n  height: 43px;\n}\n.img-xs {\n  width: 36px;\n  height: 36px;\n}\n.img-ss {\n  width: 26px;\n  height: 26px;\n}\n\n\n\n\n.fw-boldest {\n  font-weight: 900;\n}\n\n\n\n.tx-10 { font-size: 10px; }\n.tx-11 { font-size: 11px; }\n.tx-12 { font-size: 12px; }\n.tx-13 { font-size: 13px; }\n.tx-14 { font-size: 14px; }\n.tx-16 { font-size: 16px; }\n.tx-80 { font-size: 80px; }\n\n\n\nsvg.icon-xs {\n  width: 12px;\n  height: 12px;\n}\nsvg.icon-sm {\n  width: 14px;\n  height: 14px;\n}\nsvg.icon-md {\n  width: 16px;\n  height: 16px;\n}\nsvg.icon-lg {\n  width: 20px;\n  height: 20px;\n}\nsvg.icon-xl {\n  width: 26px;\n  height: 26px;\n}\nsvg.icon-xxl {\n  width: 40px;\n  height: 40px;\n}\n\n\n\n.icon-xs {\n  font-size: 14px;\n}\n.icon-sm {\n  font-size: 16px;\n}\n.icon-md {\n  font-size: 18px;\n}\n.icon-lg {\n  font-size: 20px;\n}\n.icon-xl {\n  font-size: 24px;\n}\n.icon-xxl {\n  font-size: 30px;\n}\n\n\n\n.cursor-pointer {\n  cursor: pointer;\n}\n.cursor-default {\n  cursor: default;\n}\n\n\n\n// Small paddings and margins\n.pt-1px { padding-top: 1px }\n.pt-2px { padding-top: 2px }\n.pt-3px { padding-top: 3px }\n\n.pb-1px { padding-bottom: 1px }\n.pb-2px { padding-bottom: 2px }\n.pb-3px { padding-bottom: 3px }\n\n.mt-1px { margin-top: 1px }\n.mt-2px { margin-top: 2px }\n.mt-3px { margin-top: 3px }\n\n.mb-1px { margin-bottom: 1px }\n.mb-2px { margin-bottom: 2px }\n.mb-3px { margin-bottom: 3px }\n\n\n\n// Height\n.ht-5 { height: 5px; }\n.ht-10 { height: 10px; }\n.ht-15 { height: 15px; }\n.ht-20 { height: 20px; }\n.ht-30 { height: 30px; }\n.ht-40 { height: 40px; }\n.ht-50 { height: 50px; }\n.ht-60 { height: 60px; }\n.ht-70 { height: 70px; }\n.ht-80 { height: 80px; }\n.ht-90 { height: 90px; }\n.ht-100 { height: 100px; }", "// Typography\n\nbody {\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\n// Social colors\n@each $color, $value in $social-colors {\n  .text-#{$color} {\n    color: $value;\n  }\n}", "// Demo Styles \n\n.main-content {\n  color: $body-color;\n  font-size: 16px;\n  > .page-title {\n    margin-bottom: 1rem;\n    font-weight: 400;\n  }\n  > h4 {\n    margin-top: 1.5rem;\n    margin-bottom: .875rem;\n    &::before {\n      display: block;\n      height: 5.4rem;\n      margin-top: -6rem;\n      content: \"\";\n    }\n  }\n  > hr {\n    margin-top: 40px;\n    margin-bottom: 40px;\n  }\n  .example {\n    font-size: 0.875rem;\n    letter-spacing: normal;\n    padding: 10px;\n    background-color: $card-bg;\n    border: 4px solid $border-color;\n    position: relative;\n    @media(min-width: 576px) {\n      padding: 25px;\n    }\n  }\n  .highlight {\n    position: relative;\n    background-color: $card-bg;\n    padding: 15px;\n    pre {\n      padding: 15px;\n      font-size: .875rem;\n      font-family: $font-family-sans-serif;\n      background: transparent;\n      line-height: 1.4;\n      margin: 0;\n      code {\n        font-family: $font-family-sans-serif;\n        padding: 0;\n        tab-size: 8;\n        color: $body-color;\n        text-shadow: none;\n        .token {\n          &.url,\n          &.string,\n          &.entity,\n          &.operator {\n            background: none;\n          }\n        }\n      }\n    }\n    .btn-clipboard {\n      position: absolute;\n      top: 6px;\n      right: 6px;\n      font-size: 12px;\n      padding: 1px 6px;\n      background: rgba($primary, .2);\n      &:hover,\n      &:focus {\n        background: rgba($primary, .3);\n        border-color: transparent;\n        transition: background .3s ease-in-out;\n      }\n    }\n  }\n}\n\n.example {\n  .btn-toolbar {\n    + .btn-toolbar {\n      margin-top: .5rem;\n    }\n  }\n  .btn-group {\n    @extend .mb-1;\n    @extend .mb-md-0;\n  }\n  .modal {\n    &.static {\n      position: static;\n      display: block;\n    }\n  }\n  .navbar {\n    position: relative;\n    padding: .5rem 1rem;\n    left: auto;\n    width: 100%;\n    height: auto;\n    z-index: 9;\n    border-bottom: 0;\n    box-shadow: none;\n    .navbar-brand {\n      font-size: 1.25rem;\n    }\n  }\n  .progress {\n    + .progress {\n      margin-top: 10px;\n    }\n  }\n  .perfect-scrollbar-example {\n    position: relative;\n    max-height: 250px;\n    background: $card-bg;\n  }\n  .scrollspy-example {\n    position: relative;\n    height: 200px;\n    margin-top: .5rem;\n    overflow: auto;\n  }\n  .scrollspy-example-2 {\n    position: relative;\n    height: 350px;\n    overflow: auto;\n  }\n  nav {\n    .breadcrumb {\n      margin-bottom: .75rem;\n    }\n\n    &:last-child {\n      .breadcrumb {\n        margin-bottom: 0;\n      }\n    }\n  }\n}\n\n.page-breadcrumb {\n  margin-bottom: 15px;\n  .breadcrumb {\n    padding: 0;\n    background: $body-bg;\n  }\n}\n\n.noble-ui-logo {\n  font-weight: 700;\n  font-size: 25px;\n  color: darken($primary, 50%);\n  span {\n    color: $primary;\n    font-weight: 300;\n  }\n  &:hover {\n    color: darken($primary, 50%);\n  }\n  &.logo-light {\n    color: $body-color;\n  }\n}\n\n.buy-now-wrapper {\n  position: fixed;\n  bottom: 30px;\n  right: 35px;\n  z-index: 99999;\n  .rtl & {\n    right: auto;\n    left: 35px;\n  }\n  .btn {\n    svg {\n      width: 19px !important;\n      height: 19px !important;\n    }\n  }\n}", ".page-wrapper {\n  &.full-page {\n    .page-content {\n      min-height: 100vh;\n      max-width: 100%;\n    }\n  }\n  @media (max-width: 991px) {\n    margin-top: $navbar-height;\n  }\n .page-content {\n    padding: 2rem 0;\n    @include make-container();\n    @extend .container;\n    min-height: calc(100vh - #{$navbar-height} - 50px);\n    flex-grow: 1;\n\n    .content-nav-wrapper {\n      padding: 0;\n      position: sticky;\n      top: 80px;\n      height: calc(100vh - 6rem);\n      overflow-y: auto;\n      border-left: 1px solid $border-color;\n      display: none;\n      @media(min-width: 1200px) {\n        display: block;\n      }\n      .content-nav {\n        padding: 0px 25px;\n        .nav-item {\n          .nav-link {\n            padding: 0;\n            height: 30px;\n            white-space: nowrap;\n            color: $text-muted;\n            font-size: $font-size-base;\n            display: flex;\n            align-items: center;\n          }\n        }\n      }\n    }\n  }\n}", "// Navbar\n$navbar-height: 60px;", ".horizontal-menu {\n  .navbar {\n    width: 100%;\n    height: $navbar-height;\n    background: $card-bg;\n    border-bottom: 1px solid $border-color;\n    display: flex;\n    align-items: stretch;\n    padding: 0;\n    position: relative;\n    z-index: 978;\n    -webkit-transition: width .1s ease, left .1s ease;\n    transition: width .1s ease, left .1s ease;\n\n    @media(max-width: 991px) {\n      width: 100%;\n      left: 0;\n      .navbar-content {\n        width: 100%;\n      }\n    }\n\n    .navbar-content {\n      display: flex;\n      width: 100%;\n      height: 100%;\n      @media(max-width: 991px) {\n        width: 100%;\n      }\n      .navbar-brand {\n        opacity: 1;\n        visibility: visible;\n        -webkit-transition: opacity .5s ease;\n        transition: opacity .5s ease;\n        font-weight: 700;\n        font-size: 25px;\n        color: $body-color;\n        display: flex;\n        align-items: center;\n        direction: ltr#{'/*rtl:ignore*/'};\n        span {\n          color: $primary;\n          font-weight: 300;\n        }\n      }\n  \n      .search-form {\n        @extend .d-none;\n        @extend .d-md-flex;\n        @extend .align-items-center;\n        width: 100%;\n        margin-right: 60px;\n        margin-left: 20px;\n        .input-group {\n          .input-group-text {\n            padding: 0;\n            border: 0;\n            color: $text-muted;\n            background: $input-bg;\n            svg {\n              width: 20px;\n              height: 20px;\n              cursor: pointer;\n            }\n          }\n          .form-control {\n            border: 0;\n            margin-top: 3px;\n            &::-webkit-input-placeholder {\n              color: $text-muted\n            }\n            &:-ms-input-placeholder {\n              color: $text-muted\n            }\n            &::-ms-input-placeholder {\n              color: $text-muted\n            }\n            &::placeholder {\n              color: $text-muted\n            }\n          }\n        }\n      }\n      .navbar-nav {\n        display: flex;\n        flex-direction: row;\n        margin-left: auto;\n        .nav-item {\n          position: relative;\n          margin-left: 5px;\n          margin-right: 5px;\n          min-width: 30px;\n          display: flex;\n          align-items: center;\n          @media(max-width: 767px) {\n            min-width: 21px;\n          }\n          .nav-link {\n            color: $text-muted;\n            padding: 0;\n            position: relative;\n            margin-left: auto;\n            margin-right: auto;\n            &:hover,\n            &[aria-expanded=\"true\"] {\n              color: $primary;\n            }\n            &::after {\n              display: none;\n            }\n            svg {\n              width: 20px;\n              height: 20px;\n            }\n            .indicator {\n              position: absolute;\n              top: 0px;\n              right: 2px;\n              .circle {\n                  background: $primary;\n                  width: 7px;\n                  height: 7px;\n                  border-radius: 50%;\n                  &::before {\n                      background-color: $primary;\n                      content: \"\";\n                      display: table;\n                      border-radius: 50%;\n                      position: absolute;\n                      @extend .pulse;\n                  }\n              }\n            }\n          }\n          &.dropdown {\n            @media(max-width: 767px) {\n              position: static;\n            }\n            .dropdown-menu {\n              width: max-content;\n              position: absolute;\n              right: -20px;\n              left: auto;           \n              @extend .dropdownAnimation;\n              font-size: .875rem;\n              &::before {\n                content: '';\n                width: 13px;\n                height: 13px;\n                background: $dropdown-bg;\n                position: absolute;\n                top: -7px;\n                right: 28px;\n                -webkit-transform: rotate(45deg);\n                        transform: rotate(45deg);\n                border-top: 1px solid $dropdown-border-color;\n                border-left: 1px solid $dropdown-border-color;\n              }\n              @media(max-width: 767px) {\n                right: 20px;\n                width: calc(100% - 40px);\n                &::before{\n                  display: none;\n                }\n              }\n            }\n          }\n        }\n      }\n      .navbar-toggler {\n        &:focus {\n          box-shadow: none;\n        }\n        svg {\n          width: 20px;\n          height: 20px;\n          color: $text-muted;\n        }\n      }\n    }\n  }\n  .bottom-navbar {\n    background: $card-bg;\t\t\n    position: relative;\n    width: 100%;\n    display: flex;\n    align-items: center;\n    transition-duration: 3s;\n    transition-property: position,left,right,top,z-index;\n    box-shadow: 3px 0 10px 0 #03060b;\n    -webkit-box-shadow: 3px 0 10px 0 #03060b;\n    -moz-box-shadow: 3px 0 10px 0 #03060b;\n    @include media-breakpoint-down(lg) {\n      display: none;\n      &.header-toggled {\n        display: block;\n      }\n    }\n    @include media-breakpoint-down(md) {\n      &.header-toggled {\n        max-height: calc(100vh - #{$navbar-height});\n        overflow: auto;\n      }\n    }\n\n    .page-navigation {\n      position: relative;\n      width: 100%;\n      z-index: 99;\n      justify-content: space-between;\n      transition-duration: 0.2s;\n      transition-property: background, box-shadow;\n      @include media-breakpoint-down(md) {\n        border: none;\n      }\n\n      > .nav-item {\n        line-height: 1;\n        text-align: left;\n        @include media-breakpoint-down(md) {\n          display: block;\n          width: 100%;\n          border-right: none;\n        }\n\n        &:first-child {\n          @include media-breakpoint-up(lg) {\n            >.nav-link {\n              padding-left: 0;\n            }\n          }\n        }\n\n        &:last-child {\n          border-right: none;\n          @include media-breakpoint-up(lg) {\n            >.nav-link {\n              padding-right: 0;\n            }\n          }\n        }\n\n        .category-heading {\n          font-size: .875rem;\n          font-weight: 500;\n          text-align: left;\n          color: $black;\n          padding: 1rem 0 .3rem 0;\n          margin-bottom: 0;\n          @extend .text-primary;\t\t\t\t\t\t\t\t\n        }\n\n        > .nav-link {\n          color: $text-muted;\n          padding: 22px 10px;\n          line-height: 1;\n          font-weight: 400;\n          @extend .d-flex;\n          @extend .align-items-center;\n          .menu-title {\n            font-size: 14px;\n          }\n          .link-icon {\n            margin-right: 10px;\n            font-weight: 400;\n            width: 18px;\n            height: 18px;\n            fill: none;\n            color: inherit;\n          }\n\n          .link-arrow {\n            margin-left: 5px;\n            display: inline-block;\n            @include transform(rotate(0deg));\n            @include transform-origin(center);\n            transition-duration: $action-transition-duration;\n\n            &:before {\n              content: \"\\e845\";\n              font-family: \"feather\";\n              font-style: normal;\n              display: block;\n              font-size: 12px;\n              line-height: 10px;\n            }\n          }\n        }\n\n        &:hover {\n          .submenu {\n            display: block;\n          }\n        \n          > .nav-link {\n            background: transparent;\n            .link-arrow,\n            .link-icon,\n            .menu-title {\n              color: $primary;\n              -webkit-transition: color .3s ease;\n              transition: color .3s ease;\n            }\n            .link-arrow {\n              @include transform(rotate(-180deg));\n              @include transform-origin(center);\n              transition-duration: $action-transition-duration;\n            }\n          }\n        }\n\n        @include media-breakpoint-down(md) {\n          .submenu {\n            display: block;\n          }\n        \n          > .nav-link {\n            background: transparent;\n            .link-arrow {\n              @include transform(rotate(-180deg));\n              @include transform-origin(center);\n              transition-duration: $action-transition-duration;\n            }\n          }\n        }\n\n        &.active {\n          > .nav-link {\n            position: relative;\n            &:before {\n              position: absolute;\n              content: \"\";\n              left: 0;\n              bottom: -2px;\n              width: 100%;\n              height: 5px;\n              @media (max-width: 991px) {\n                left: -15px;\n                top: 0;\n                bottom: 0;\n                height: 100%;\n                width: 5px;\n              }\n            }\n            .menu-title,\n            .link-icon,\n            .link-arrow {\n              color: $primary;\t\t\t\t\t\t\t\n            }\n          }\n        }\n\n        .submenu {\n          display: none;\n          @extend .dropdownAnimation;\n          border: 1px solid $border-color;\n          ul {\n            list-style-type: none;\n            padding-left: 0;\n          \n            li {\n              display: block;\n              line-height: 20px;\n\n              a {\n                display: block;\n                padding: 3px 10px;\n                color: $text-muted;\n                text-decoration: none;\n                text-align: left;\n                margin: 4px 0;\n                white-space: nowrap;\n                \n                &:hover {\n                  color: $primary;\n                  @include transition (color .1s linear);\n                  &:before {\n                    background: $primary;\n                  }\n                }\n                &.active {\n                  color: $primary;\n                }\n              }\n\n              &.active {\n                a {\n                  color: $primary;\n                  &:before {\n                    background: $primary;\n                  }\n                }\n              }\n            }\n          }\n        }\n\n        &:not(.mega-menu) {\n          position: relative;\n          .submenu {\n            left: 0;\n            width: -moz-max-content;\n            width: -webkit-max-content;\n            width: -o-max-content;\n            width: -ms-max-content;\n            min-width: 180px;\t\n            @include media-breakpoint-up(md) {\n              position: absolute;\n              top: 61px;\n              z-index: 999;\n              background: $card-bg;\n              border-top: none;\n              border-radius: $border-radius;\n              box-shadow: 1px 0px 20px rgba(0, 0, 0, 0.08);\n              -webkit-box-shadow: 1px 0px 20px rgba(0, 0, 0, 0.08);\n              li {\n                a {\n                  position: relative;\n                  padding-left: 20px;\t\n                  &::before {\n                    position: absolute;\n                    content: \"\";\n                    width: 4px;\n                    height: 4px;\n                    border-radius: 100%;\n                    background: $secondary;\n                    top: 12px;\n                    left: 0;\n                  }\t\t\t\n                }\n              }\n            }\n            @include media-breakpoint-down(md) {\n              position: relative;\n              top:0;\n              -webkit-box-shadow: none;\n                      box-shadow: none;\n              width: 100%;\n            }\t\t\t\t\t\t\t\t\t\t\t\t\n            ul {\n              width: auto;\n              padding: 15px 30px;\n              @include media-breakpoint-down(md) {\n                padding: 0 35px;\n              }\n            }\n          }\n        }\n\n        &.mega-menu {\n          .submenu {\n            @include media-breakpoint-up(md) {\n              position: absolute;\n              top: 61px;\n              z-index: 999;\n              background: $card-bg;\n              border-top: none;\n              border-radius: 4px;\n              box-shadow: 1px 0px 20px rgba(0, 0, 0, 0.08);\n              -webkit-box-shadow: 1px 0px 20px rgba(0, 0, 0, 0.08);\n              li {\n                a {\n                  position: relative;\n                  padding-left: 20px;\t\n                  &::before {\n                    position: absolute;\n                    content: \"\";\n                    width: 4px;\n                    height: 4px;\n                    border-radius: 100%;\n                    background: $secondary;\n                    top: 12px;\n                    left: 0;\n                  }\t\t\t\n                }\n              }\n            }\n            @include media-breakpoint-down(md) {\n              position: relative;\n              top:0;\n              -webkit-box-shadow: none;\n                      box-shadow: none;\n            }\n            width: 100%;\t\t\t\t\t\t\n            left: 0;\n            right: 0;\n            padding: 15px 25px;\n\n            .col-group-wrapper {\n              padding: 0 1rem;\t\t\t\t\t\t\t\t\n            }\n            @include media-breakpoint-down(md) {\n              padding: 0 32px;\n\n              .col-group-wrapper {\n                margin-left: 0;\n                margin-right: 0;\n                padding: 0;\n\n                .col-group {\n                  padding-left: 0;\n                  padding-right: 0;\n                  margin-bottom: 20px;\n\n                  .category-heading {\n                    &:after {\n                      display: none;\n                    }\n                  }\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n  &.fixed-on-scroll {\n    + .page-wrapper {\n      padding-top: $navbar-height;\n    }\n    .bottom-navbar {\n      @extend .fixed-top;\n      border-bottom: 1px solid $border-color;\n    }\n  }\n  @media (max-width: 991px) {\n    position: fixed;\n    z-index: 1030;\n    top: 0;\n    left: 0;\n    right: 0;\n  }\n}", "// Miscellaneous Mixins \n\n// general transform\n@mixin transform($transforms) {\n    -moz-transform: $transforms;\n      -o-transform: $transforms;\n     -ms-transform: $transforms;\n -webkit-transform: $transforms;\n       transform: $transforms;\n}\n\n// rotate\n@mixin rotate ($deg) {\n@include transform(rotate(#{$deg}deg));\n}\n\n// scale\n@mixin scale($scale) {\n  @include transform(scale($scale));\n}\n// translate\n@mixin translate ($x, $y) {\n@include transform(translate($x, $y));\n}\n// skew\n@mixin skew ($x, $y) {\n@include transform(skew(#{$x}deg, #{$y}deg));\n}\n//transform origin\n@mixin transform-origin ($origin) {\n moz-transform-origin: $origin;\n      -o-transform-origin: $origin;\n     -ms-transform-origin: $origin;\n -webkit-transform-origin: $origin;\n       transform-origin: $origin;\n}\n//Ellipsis\n%ellipsor{\n text-overflow: ellipsis;\n overflow: hidden;\n max-width:100%;\n white-space: nowrap;\n}\n\n// Placeholder\n@mixin placeholder {\n &::-webkit-input-placeholder {\n     @content\n }\n &:-moz-placeholder {\n     @content\n }\n &::-moz-placeholder {\n    @content\n }\n &:-ms-input-placeholder {\n    @content\n }\n}\n\n%ellipsor {\n  text-overflow: ellipsis;\n  overflow: hidden;\n  max-width: 100%;\n  white-space: nowrap;\n  }\n  @mixin ellipsor {\n  text-overflow: ellipsis;\n  overflow: hidden;\n  max-width: 100%;\n  white-space: nowrap;\n}", "// Badges\n\na.badge { // for link badges\n  &:hover {\n    color: $white;\n  }\n}", "// Bootstrap Alert\n\n@each $state, $value in $theme-colors {\n  // Basic alerts\n  .alert-#{$state} {\n    background-color: rgba($value, .1);\n    color: darken($value, 5%);\n    border-color: rgba($value, .2);\n    .alert-link {\n      color: darken($value, 5%);\n    }\n    svg {\n      width: 19px;\n      height: 19px;\n      margin-right: .5rem;\n    }\n    i {\n      font-size: 19px;\n      margin-right: .5rem;\n    }\n  }\n\n  // Fill alerts\n  .alert-fill-#{$state} {\n    @include alert-variant($value, $value, $white);\n    &.alert-fill-light {\n      color: $text-muted;\n    }\n  }\n}\n\n", ".breadcrumb {\n  &.breadcrumb-line {\n    .breadcrumb-item {\n      + .breadcrumb-item {\n        &::before {\n          content: \"-\" !important;\n        }\n      }\n    }\n  }\n  &.breadcrumb-dot {\n    .breadcrumb-item {\n      + .breadcrumb-item {\n        &::before {\n          content: \"•\" !important;\n        }\n      }\n    }\n  }\n  &.breadcrumb-arrow {\n    .breadcrumb-item {\n      + .breadcrumb-item {\n        &::before {\n          content: \">\" !important;\n        }\n      }\n    }\n  }\n}", "/* Buttons */\n\n.btn {\n  i {\n    font-size: 1rem;\n  }\n  &.btn-rounded {\n    @include border-radius(50px);\n  }\n  &.btn-xs {\n    padding: $btn-padding-y-xs $btn-padding-x-xs;\n    font-size: $btn-font-size-xs;\n  }\n\n  // Buttons with only icons \n  &.btn-icon {\n    width: 38px;\n    height: 38px;\n    padding: 0;\n    display: inline-flex;\n    align-items: center;\n    justify-content: center;\n    svg {\n      height: 18px;\n    }\n    &.btn-xs {\n      width: 30px;\n      height: 30px;\n      svg {\n        height: 14px;\n      }\n    }\n    &.btn-sm {\n      width: 36px;\n      height: 36px;\n      svg {\n        height: 15px;\n      }\n    }\n    &.btn-lg {\n      width: 42px;\n      height: 42px;\n      svg {\n        height: 18px;\n      }\n    }\n  }\n\n  // Buttons with icon and text \n  &.btn-icon-text {\n    .btn-icon-prepend {\n      margin-right: .5rem;\n    }\n    .btn-icon-append {\n      margin-left: .5rem;\n    }\n    .btn-icon-prepend,\n    .btn-icon-append {\n      width: 18px;\n      height: 18px;\n    }\n    &.btn-xs {\n      .btn-icon-prepend,\n      .btn-icon-append {\n        width: 14px;\n        height: 14px;\n      }\n    }\n    &.btn-sm {\n      .btn-icon-prepend,\n      .btn-icon-append {\n        width: 15px;\n        height: 15px;\n      }\n    }\n    &.btn-lg {\n      .btn-icon-prepend,\n      .btn-icon-append {\n        width: 18px;\n        height: 18px;\n      }\n    }\n  }\n}\n\n// Social buttons\n@each $color, $value in $social-colors {\n  .btn-#{$color} {\n    @include social-button(social-color($color));\n  }\n  .btn-outline-#{$color} {\n    @include social-outline-button(social-color($color));\n  }\n}\n\n// Inverse buttons \n@each $color, $value in $theme-colors {\n  .btn-inverse-#{$color} {\n    @include button-inverse-variant($value);\n  }\n}\n", "@mixin social-button($color) {\n  background: $color;\n  color: $white;\n\n  &:hover,\n  &:focus {\n    background: darken($color, 10%);\n    color: $white;\n  }\n  &.btn-social-icon-text {\n    padding: 0 1.5rem 0 0;\n    background: lighten($color, 10%);\n    i {\n      background: $color;\n      padding: .75rem;\n      display: inline-block;\n      margin-right: 1.5rem;\n    }\n  }\n}\n\n@mixin social-outline-button($color) {\nborder: 1px solid $color;\ncolor: $color;\n  &:hover {\n    background: $color;\n    color: $white;\n  }\n}\n\n@mixin button-inverse-variant($color, $color-hover: $white) {\n  background-color: rgba($color, 0.2);\n  background-image: none;\n  border-color: rgba($color, 0);\n  &:not(.btn-inverse-light) {\n    color: $color;      \n  }\n  &:hover,\n  &.active,\n  &:active,\n  .show > &.dropdown-toggle {\n    background-color: rgba($color, 0.3);\n    border-color: rgba($color, 0);\n  }\n\n  &.focus,\n  &:focus {\n    // box-shadow: 0 0 0 3px rgba($color, .5);\n    background-color: rgba($color, 0.3);\n    border-color: transparent;\n  }\n\n  &.disabled,\n  &:disabled {\n    color: $color;\n    background-color: transparent;\n  }\n}\n", "// Cards \n\n.card {\n  box-shadow: $card-box-shadow;\n  -webkit-box-shadow: $card-box-shadow;\n  -moz-box-shadow: $card-box-shadow;\n  -ms-box-shadow: $card-box-shadow;\n  .card-body {\n    + .card-body {\n      padding-top: 1rem;\n    }\n  }\n  .card-title {\n    text-transform: uppercase;\n    font-size: .875rem;\n    font-weight: 500;\n  }\n}\n\n.card-group {\n  box-shadow: $card-box-shadow;\n  .card {\n    box-shadow: none;\n  }\n}\n", "// Dropdowns \n\n.dropdown,\n.btn-group {\n  .dropdown-toggle {\n    &:after {\n      border-top: 0;\n      border-right: 0;\n      border-left: 0;\n      border-bottom: 0;\n      font: normal normal normal 24px/1 \"feather\";\n      content: \"\\e842\";  \n      width: auto;\n      height: auto;\n      vertical-align: middle;\n      line-height: .625rem;\n      font-size: .875rem;\n    }\n  }\n  &.dropup {\n    .dropdown-toggle {\n      &::after {\n        content: \"\\e845\";\n      }\n    }\n  }\n  &.dropstart {\n    .dropdown-toggle {\n      &::before {\n        border: 0;\n        font: normal normal normal 24px/1 \"feather\";\n        content: \"\\e843\";\n        width: auto;\n        height: auto;\n        vertical-align: middle;\n        line-height: .625rem;\n        font-size: .875rem;\n      }\n    }\n  }\n  &.dropend {\n    .dropdown-toggle {\n      &::after {\n        content: \"\\e844\";\n      }\n    }\n  }\n}\n\n.dropdown-menu {\n  padding: .35rem;\n  margin-top: 0;\n  box-shadow: $dropdown-box-shadow; \n}\n\n.dropdown-item {\n  font-size: .812rem;\n  padding: .25rem .875rem;\n  border-radius: 2px;\n  i, svg {\n    color: $text-muted;\n  }\n  &:not(&:active, &.active):hover {\n    background-color: rgba($primary, .1);\n    &, i, svg {\n      color: $primary;\n    }        \n  }\n  &:active,\n  &.active {\n    i, svg {\n      color: $white;\n    }\n  }\n}", "// Forms \n\n.form-control-xs,\n.form-select-xs {\n  padding: $input-padding-y-xs $input-padding-x-xs;\n  font-size: $input-font-size-xs;\n}\n\n.form-control-xs {\n  + .input-group-text {\n    padding-top: $input-padding-y-xs;\n    padding-bottom: $input-padding-y-xs;\n  }\n}\n\n.form-check-input {\n  margin-top: .13em; // height adjustment\n}\n\n.input-group-text {\n  svg {\n    width: 18px;\n    height: 18px;\n  }\n}\n\n// For RTL\n[type=\"tel\"], \n[type=\"url\"], \n[type=\"email\"], \n[type=\"number\"] {\n  direction: ltr;\n}", "// Icons \n\n.icons-list {\n  border-left: 1px solid $border-color;\n  border-top: 1px solid $border-color;\n  > div {\n    border-bottom: 1px solid $border-color;\n    border-right: 1px solid $border-color;\n    background: $body-bg;\n    display: flex;\n    align-items: center;\n    padding:15px 20px;\n    font-weight: 400;\n    transition: all .3s ease-in-out;\n\n    i {\n      display: inline-block;\n      font-size: 20px;\n      text-align: left;\n      margin-right: 12px;\n      color: $secondary;\n      transition: all .3s ease-in-out;\n    }\n\n    svg {\n      width: 20px;\n      margin-right: 12px;\n      color: $secondary;\n      transition: all .3s ease-in-out;\n    }\n    \n    &:hover {\n      cursor: text;\n      i,\n      svg {\n        transform: scale(1.3);\n        color: $primary;\n      }\n    }\n  }\n}", ".nav {\n  &.nav-tabs {\n    .nav-item {\n      .nav-link {\n        border-color: $nav-tabs-link-border-color;\n        color: $body-color;\n        background-color: $nav-tabs-link-bg;\n        cursor: pointer;\n        &.active {\n          border-color: $nav-tabs-link-active-border-color;\n          color: $primary;\n          background: $nav-tabs-link-active-bg;\n        }\n        &.disabled {\n          background-color: transparent;\n          color: $text-muted;\n          border-color: rgba($border-color, .7) rgba($border-color, .7) transparent;\n        }\n      }\n    }\n    &.nav-tabs-vertical {\n      border-bottom: 0;\n      .nav-link {\n        width: 100%;\n        border: 1px solid transparent;\n        border-radius: 0;\n        border-color: $nav-tabs-link-border-color;\n        color: $body-color;\n        background-color: $nav-tabs-link-bg;\n        &:first-child {\n          border-radius: $border-radius 0 0 0;\n        }\n        &:last-child {\n          border-radius: 0 0 0 $border-radius;\n        }\n        &.active {\n          background-color: $nav-tabs-link-active-bg;\n          color: $primary;\n          border-right-color: transparent;\n        }\n      }\n    }\n    &.nav-tabs-line {\n      .nav-link {\n        border: 0;\n        background-color: transparent;\n        &.active {\n          border-bottom: 2px solid $primary;\n        }\n      }\n    }\n  }\n}\n.tab-content {\n  &.tab-content-vertical {\n    height: 100%;\n  }\n}\n", ".pagination {\n  .page-item {\n    .page-link {\n      svg {\n        width: 18px;\n        height: 18px;\n      }\n    }\n  }\n  &.pagination-separated {\n    .page-item {\n      margin-left: 2px;\n      margin-right: 2px;\n      &:first-child {\n        margin-left: 0;\n      }\n      &:last-child  {\n        margin-right: 0;\n      }\n    }\n  }\n  &.pagination-rounded {\n    .page-item {\n      margin-right: 2px;\n      margin-left: 2px;\n      .page-link {\n        @include border-radius(50px);\n      }\n    }\n  }\n}\n", "// Tables \n\n.table {\n  margin-bottom: 0;\n\n  >:not(:last-child)>:last-child>* {\n\t\tborder-bottom-color: inherit;\n\t}\n\n  thead {\n    th {\n      border-top: 0;\n      font-weight: 500;\n      font-size: 12px;\n      text-transform: uppercase;\n      color: $text-muted;\n      i {\n        margin-left: 0.325rem;\n      }\n    }\n  }\n\n  th,\n  td {\n    white-space: nowrap;\n  }\n  \n  td {\n    img {\n      width: 36px;\n      height: 36px;\n      border-radius: 100%;\n    }\n  }\n}\n", ".timeline {\n  border-left: 3px solid $primary;\n  border-bottom-right-radius: $border-radius;\n  border-top-right-radius: $border-radius;    \n  background: rgba($primary, .2);\n  margin: 0 auto;  \n  position: relative;\n  padding: 50px;\n  list-style: none;\n  max-width: 40%; \n  @media(max-width: 767px) {\n    max-width: 98%;\n    padding: 25px;\n  }\n  \n  .event {\n    border-bottom: 1px dashed $border-color;\n    padding-bottom: (50px * 0.5);\n    margin-bottom: 25px;  \n    position: relative;\n    \n    @media(max-width: 767px) {\n      padding-top: 30px;\n    }\n\n    .title {\n      font-weight: 500;\n      font-size: 1rem;\n      margin-bottom: 10px;\n    }\n\n    &:last-of-type { \n      padding-bottom: 0;\n      margin-bottom: 0; \n      border: none;      \n    }\n\n    &:before, &:after {\n      position: absolute;\n      display: block;\n      top: 0;\n    }\n\n    &:before {\n      left: (((120px * 0.6) + 50px + 4px + 4px + (4px * 2)) * 1.5) * -1;    \n      content: attr(data-date);\n      text-align: right;\n      font-weight: 500;    \n      font-size: 0.9em;\n      min-width: 120px;\n      @media(max-width: 767px) {\n        left: 0px;\n        text-align: left;\n      }\n    }\n\n    &:after {\n      -webkit-box-shadow: 0 0 0 3px $primary;\n              box-shadow: 0 0 0 3px $primary;    \n      left: (50px + 3px + (8px * 0.35)) * -1;        \n      background: $card-bg;    \n      border-radius: 50%;  \n      height: 9px;\n      width: 9px;\n      content: \"\";\n      top: 5px;\n      @media(max-width: 767px) {\n        left: (25px + 4px + (8px * 0.35)) * -1;        \n      }\n    }\n  }\n}", ".chat-wrapper {\n  height: calc(100vh - #{$navbar-height} - 102px);\n  @media(max-width: 991px) {\n    min-height: 100%;\n  }\n  @media(max-width: 991px) {\n    height: 100%;\n  }\n  .chat-aside {\n    @media(min-width: 992px) {\n      padding-right: 23px;\n    }\n    .aside-body {\n      .tab-content {\n        .tab-pane {\n          position: relative;\n          max-height: calc(100vh - 385px);\n          .chat-list {\n            .chat-item {\n              a {\n                > div {\n                  padding-top: 11px;\n                  padding-bottom: 11px;\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n  .chat-content {\n    @media(max-width: 991px) {\n      position: absolute;\n      background: $card-bg;\n      left: 0;\n      bottom: -1px;\n      top: 0;\n      right: 0;\n      display: none;\n      &.show {\n        display: block;\n      }\n    }\n    .chat-header {\n      padding: 0 10px;\n    }\n    .chat-body {\n      position: relative;\n      max-height: calc(100vh - 340px);\n      @media(max-width: 767px) {\n        max-height: calc(100vh - 315px);        \n      }\n      @media(max-width: 991px) {\n        max-height: calc(100vh - 342px);        \n      }\n      margin-top: 20px;\n      margin-bottom: 20px;\n      .messages {\n        padding: 0 10px;\n        list-style-type: none;\n        .message-item {\n          display: flex;\n          max-width: 80%;\n          margin-bottom: 20px;\n          @media(max-width: 767px) {\n            max-width: 95%;\n          }\n          .content {\n            .bubble {\n              position: relative;\n              padding: 7px 15px;\n              margin-bottom: 4px;\n              width: -webkit-fit-content;\n              width: -moz-fit-content;\n              width: fit-content;\n            }\n            span {\n              font-size: 12px;\n              color: $text-muted;\n            }\n          }\n          &.friend {\n            img {\n              order: 1;\n              margin-right: 15px;\n            }\n            .content {\n              order: 2;\n              .bubble {\n                background: rgba($primary, .1);\n                border-radius: 0 5px 5px;\n                &::before {\n                  content: '';\n                  width: 0;\n                  height: 0;\n                  position: absolute;\n                  left: -10px;\n                  top: 0;\n                  border-top: 5px solid rgba($primary, .1);\n                  border-bottom: 5px solid transparent; \n                  border-left: 5px solid transparent; \n                  border-right:5px solid rgba($primary, .1); \n                }\n              }\n            }\n          }\n          &.me {\n            margin-left: auto;\n            img {\n              order: 2;\n              margin-left: 15px;\n            }\n            .content {\n              order: 1;\n              margin-left: auto;\n              .bubble {\n                background: rgba($info, .1);\n                border-radius: 5px 0 5px 5px;\n                margin-left: auto;\n                &::before {\n                  content: '';\n                  width: 0;\n                  height: 0;\n                  position: absolute;\n                  right: -10px;\n                  top: 0;\n                  border-top: 5px solid rgba($info, .1);\n                  border-bottom: 5px solid transparent; \n                  border-left: 5px solid rgba($info, .1); \n                  border-right:5px solid transparent; \n                }\n              }\n              span {\n                text-align: right;\n                display: block;\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n  figure {\n    position: relative;\n    .status {\n      width: 11px;\n      height: 11px;\n      background: $secondary;\n      position: absolute;\n      bottom: 0px;\n      right: -2px;\n      border-radius: 50%;\n      border: 2px solid $card-bg;\n      &.online {\n        background: $success;\n      }\n      &.offline {\n        background: $secondary;\n      }\n    }\n  }\n}", ".auth-page {\n  .auth-side-wrapper {\n    width: 100%;\n    height: 100%;\n    background-image: url(https://via.placeholder.com/219x452);\n    background-size: cover;\n  }\n}", ".email-aside-nav {\n  &.collapse {\n    display: block;\n    @media(max-width: 991px) {\n      display: none;\n    }\n    &.show {\n      @media(max-width: 991px) {\n        display: block;\n      }\n    }\n  }\n  .nav-item {\n    border-radius: .2rem;\n    .nav-link {\n      color: $body-color;\n      svg {\n        color: $text-muted;\n      }\n    }\n    &.active, &:hover {\n      background: rgba($primary, .1);\n      .nav-link {\n        &, svg {\n          color: $primary;\n        }\n      }\n    }\n  }\n}\n\n.email-list-item {\n  display: flex;\n  align-items: center;\n  border-bottom: 1px solid $border-color;\n  padding: 10px 20px;\n  cursor: pointer;\n  &:hover {\n    background: rgba($primary, .08);\n  }\n  &:last-child {\n    margin-bottom: 5px;\n  }\n  .email-list-actions {\n    width: 40px;\n    vertical-align: top;\n    display: table-cell;\n    .form-check {\n      margin-bottom: 0;\n    }\n    .favorite {\n      display: block;\n      padding-left: 1px;\n      line-height: 15px;\n      span {\n        svg {\n          width: 14px;\n          color: $text-muted;\n        }\n      }\n      &:hover span {\n        color: #8d8d8d;\n      }\n      &.active {\n        span {\n          svg {\n            color: $warning;\n          }\n        }\n      }\n    }\n  }\n  .email-list-detail {\n    width: calc(100% - 40px);\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    flex-grow: 1;\n    .content {\n      overflow: hidden;\n      .from {\n        display: block;\n        margin: 0 0 1px 0;\n        color: $body-color;\n      }\n      .msg {\n        width: 97%;\n        color: $secondary;\n        font-size: .8rem;\n        overflow: hidden;\n        text-overflow: ellipsis;\n        white-space: nowrap;\n      }\n    }\n    .date {\n      color: $body-color;\n      white-space: nowrap;\n      .icon {\n        svg {\n          width: 14px;\n          margin-right: 7px;\n          color: #3d405c;\n        }\n      }\n    }\n  }\n  &.email-list-item--unread {\n    background-color: rgba($primary, .09);\n    .content {\n      .from {\n        font-weight: 500;\n      }\n      .msg {\n        font-weight: 700;\n      }\n    }\n  }\n}", "// npm package: ace-builds (Ajax.org Cloud9 Editor)\n// github link: https://github.com/ajaxorg/ace-builds\n\n.ace_editor {\n  border-radius: $input-border-radius;\n  margin: auto;\n  height: 300px;\n  width: 100%;\n  font: 14px/normal SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace;\n  .ace_content{\n    font-size: $font-size-base;\n  }\n}", "// npm package: apexcharts\n// github link: https://github.com/apexcharts/apexcharts.js\n\ndiv.apexcharts-canvas {\n  .apexcharts-menu {\n    background: $dropdown-bg !important;\n    color: $body-color;\n    border-color: $border-color;\n  }\n\n  .apexcharts-zoom-icon svg, \n  .apexcharts-zoomin-icon svg, \n  .apexcharts-zoomout-icon svg, \n  .apexcharts-reset-icon svg, \n  .apexcharts-pan-icon svg, \n  .apexcharts-selection-icon svg, \n  .apexcharts-menu-icon svg, \n  .apexcharts-toolbar-custom-icon svg {\n    fill: $secondary;\n  }\n\n  .apexcharts-legend.apexcharts-align-right .apexcharts-legend-series, \n  .apexcharts-legend.apexcharts-align-left .apexcharts-legend-series {\n    display: flex;\n    align-items: center;\n  }\n\n  .apexcharts-legend-marker {\n    margin-right: 3px;\n  }\n\n  .apexcharts-tooltip {\n    background: rgba($dropdown-bg, .8);\n    color: $body-color;\n    box-shadow: $dropdown-box-shadow;\n    border-radius: $border-radius;\n    border: 1px solid $dropdown-border-color;\n    .apexcharts-tooltip-title {\n      border-color: $border-color;\n      background-color: $dropdown-bg;\n    }\n    * {\n      font-family: $font-family-sans-serif !important;\n    }\n  }\n  .apexcharts-tooltip-series-group.apexcharts-active, \n  .apexcharts-tooltip-series-group:last-child {\n    padding: 0 10px;\n  }\n  .apexcharts-tooltip-text-y-value, \n  .apexcharts-tooltip-text-goals-value, \n  .apexcharts-tooltip-text-z-value {\n    margin-left: 0;\n  }\n\n  .apexcharts-tooltip-title {\n    margin-bottom: 0;\n  }\n\n  .apexcharts-xaxistooltip,\n  .apexcharts-yaxistooltip {\n    background: $dropdown-bg;\n    color: $body-color;\n    border-color: $dropdown-border-color;\n  }\n\n  .apexcharts-xaxistooltip-bottom::before {\n    border-bottom-color: $dropdown-border-color;\n  }\n  .apexcharts-xaxistooltip-bottom::after {\n    border-bottom-color: rgba($dropdown-bg, .8);\n  }\n\n  .apexcharts-yaxistooltip-left:before {\n    border-left-color: $dropdown-border-color;\n  }\n  .apexcharts-yaxistooltip-left:after {\n    border-left-color: rgba($dropdown-bg, .8);\n  }\n\n  .apexcharts-tooltip-marker {\n    /*rtl:raw:\n    margin-right: 0; \n    margin-left: 10px; \n    */\n  }\n\n}", "// npm package: datatables.net-bs5\n// github link: https://github.com/DataTables/Dist-DataTables-Bootstrap5\n\n.dataTables_wrapper {\n  &.dt-bootstrap5 {\n    .dataTables_length {\n      @media(max-width: 767px) {\n        text-align: left;\n      }\n      select {\n        margin-left: 10px;\n        margin-right: 10px;\n      }\n    }\n    .dataTables_filter {\n      @media(max-width: 767px) {\n        text-align: left;\n      }\n    }\n  }\n}\n\ndiv.table-responsive>div.dataTables_wrapper>div.row>div[class^=col-]:first-child {\n  /*rtl:raw:\n    padding-right: 0;\n    padding-left: 12px;\n  */\n}\n\ndiv.table-responsive>div.dataTables_wrapper>div.row>div[class^=col-]:last-child {\n  /*rtl:raw:\n    padding-left: 0;\n    padding-right: 12px;\n  */\n}", "// npm package: dropify\n// github link: https://github.com/JeremyFagis/dropify\n\n.dropify-wrapper {\n  background: $input-bg;\n  border: 1px solid $input-border-color;\n  border-radius: $input-border-radius;\n  &:hover {\n    background-image: linear-gradient(-45deg,darken($input-bg, 5%) 25%,transparent 25%,transparent 50%,darken($input-bg, 5%) 50%,darken($input-bg, 5%) 75%,transparent 75%,transparent)\n  }\n  .dropify-message {\n    span {\n      &.file-icon {\n        font-size: .875rem;\n        color: $text-muted;\n        &::before {\n          font-family: feather;\n          content: '\\e8e3';\n          font-size: 24px;\n        }\n      }\n    }\n  }\n  .dropify-preview {\n    background-color: $input-bg;\n  }\n}", "// npm package: dropify\n// github link: https://github.com/dropzone/dropzone\n\n.dropzone {\n  background: $input-bg;\n  overflow: auto;\n  border: 1px solid $input-border-color;\n  border-radius: $input-border-radius;\n  @media (min-width: 1400px) {\n    min-height: 200px;\n  }\n  max-height: 200px;\n  padding: 0;\n  &.dz-clickable {\n    .dz-message {\n      margin-top: 65px;\n      * {\n        @extend .text-muted;\n      }\n    }\n  }\n  .dz-preview {\n    &.dz-image-preview {\n      background: $input-bg;\n    }\n    &.dz-file-preview,\n    &.dz-image-preview {\n     .dz-image {\n        border-radius: $input-border-radius;\n     }\n    }\n  }\n}", "// npm package: flatpickr\n// github link: https://github.com/flatpickr/flatpickr\n\n.flatpickr-calendar {\n  background-color: $dropdown-bg;\n  border: 1px solid $dropdown-border-color;\n  box-shadow: $dropdown-box-shadow;\n}\n\n.flatpickr-calendar.arrowTop:before,\n.flatpickr-calendar.arrowTop:after {\n  border-bottom-color: $dropdown-border-color;\n}\n\n.flatpickr-calendar.arrowBottom:before,\n.flatpickr-calendar.arrowBottom:after {\n  border-top-color: $dropdown-border-color;\n}\n\n.form-control.flatpickr-input {\n\tbackground-color: $input-bg;\n}\n\n.flatpickr-day.selected, \n.flatpickr-day.startRange, \n.flatpickr-day.endRange, \n.flatpickr-day.selected.inRange, \n.flatpickr-day.startRange.inRange, \n.flatpickr-day.endRange.inRange, \n.flatpickr-day.selected:focus, \n.flatpickr-day.startRange:focus, \n.flatpickr-day.endRange:focus, \n.flatpickr-day.selected:hover, \n.flatpickr-day.startRange:hover, \n.flatpickr-day.endRange:hover, \n.flatpickr-day.selected.prevMonthDay, \n.flatpickr-day.startRange.prevMonthDay, \n.flatpickr-day.endRange.prevMonthDay, \n.flatpickr-day.selected.nextMonthDay, \n.flatpickr-day.startRange.nextMonthDay, \n.flatpickr-day.endRange.nextMonthDay {\n\tbackground: $primary;\n\tborder-color: $primary;\n}\n\n.flatpickr-months {\n\tpadding: 0 1rem;\n\tpadding-top: 0.5rem;\n}\n\n.flatpickr-innerContainer {\n  // padding: 0.5rem 1rem;\n}\n\n.flatpickr-months .flatpickr-prev-month.flatpickr-prev-month, \n.flatpickr-months .flatpickr-next-month.flatpickr-prev-month {\n  \n  left: 11px;\n  right: auto !important;\n  top: 8px;\n}\n\n.flatpickr-months .flatpickr-prev-month.flatpickr-next-month, \n.flatpickr-months .flatpickr-next-month.flatpickr-next-month {\n  \n  right: 11px;\n  left: auto !important;\n  top: 8px;\n}\n\n.flatpickr-months .flatpickr-prev-month, \n.flatpickr-months .flatpickr-next-month {\n  fill: $text-muted;\n}\n\n.flatpickr-months .flatpickr-prev-month:hover svg, \n.flatpickr-months .flatpickr-next-month:hover svg {\n  fill: $primary;\n}\n\n.flatpickr-months .flatpickr-month {\n  height: 42px;\n  color: $text-muted;\n}\n\n.flatpickr-current-month .numInputWrapper span.arrowUp:after {\n  border-bottom-color: $text-muted;\n}\n\n.flatpickr-current-month .numInputWrapper span.arrowDown:after {\n  border-top-color: $text-muted;\n}\n\n.flatpickr-current-month .flatpickr-monthDropdown-months:hover,\n.numInputWrapper:hover {\n  background: rgba($primary, .1);\n}\n\n.flatpickr-current-month .flatpickr-monthDropdown-months {\n  font-size: 1rem;\n  border-radius: $input-border-radius;\n  padding: .3rem .5rem;\n}\n\n.flatpickr-weekdays {\n  padding: 0 10px;\n}\n\nspan.flatpickr-weekday {\n  color: $text-muted;\n}\n\n.dayContainer {\n  padding: 0 10px 10px;\n}\n\n.flatpickr-day {\n  color: $body-color;\n}\n\n.flatpickr-day.today {\n  border-color: $text-muted;\n}\n\n.flatpickr-day.flatpickr-disabled, \n.flatpickr-day.flatpickr-disabled:hover, \n.flatpickr-day.prevMonthDay, \n.flatpickr-day.nextMonthDay, \n.flatpickr-day.notAllowed, \n.flatpickr-day.notAllowed.prevMonthDay, \n.flatpickr-day.notAllowed.nextMonthDay {\n  color: rgba($body-color, .5);\n}\n\n.flatpickr-day.inRange, \n.flatpickr-day.prevMonthDay.inRange, \n.flatpickr-day.nextMonthDay.inRange, \n.flatpickr-day.today.inRange, \n.flatpickr-day.prevMonthDay.today.inRange, \n.flatpickr-day.nextMonthDay.today.inRange, \n.flatpickr-day:hover, \n.flatpickr-day.prevMonthDay:hover, \n.flatpickr-day.nextMonthDay:hover, \n.flatpickr-day:focus, \n.flatpickr-day.prevMonthDay:focus, \n.flatpickr-day.nextMonthDay:focus {\n  background: rgba($primary, .2);\n  border-color: transparent;\n}\n\n.flatpickr-calendar.hasTime .flatpickr-time {\n  border-top-color: $dropdown-border-color;\n}\n\n.flatpickr-time input:hover, \n.flatpickr-time .flatpickr-am-pm:hover, \n.flatpickr-time input:focus, \n.flatpickr-time .flatpickr-am-pm:focus {\n  background: rgba($primary, .2);\n}\n\n.flatpickr-time input,\n.flatpickr-time .flatpickr-time-separator, \n.flatpickr-time .flatpickr-am-pm {\n  color: $body-color;\n}\n\n.flatpickr-time .numInputWrapper span.arrowUp:after {\n  border-bottom-color: $text-muted;\n}\n\n.flatpickr-time .numInputWrapper span.arrowDown:after {\n  border-top-color: $text-muted;\n}", "// npm package: fullcalendar\n// github link: https://github.com/fullcalendar/fullcalendar\n\n.fc {\n  --fc-button-active-bg-color: #{$primary};\n  --fc-button-active-border-color: #{$primary};\n  .fc-button-primary {\n    @extend .btn;\n    @extend .btn-outline-primary;\n    @extend .btn-sm;\n  }\n  .fc-button-primary:not(:disabled).fc-button-active, \n  .fc-button-primary:not(:disabled):active {\n    @extend .btn-primary;\n  }\n  .fc-button .fc-icon {\n    font-size: 1.2 em;\n  }\n  .fc-button-primary:focus, \n  .fc-button-primary:not(:disabled).fc-button-active:focus, \n  .fc-button-primary:not(:disabled):active:focus {\n    box-shadow: none;\n  }\n\n  .fc-button-primary:disabled {\n    border-color: $primary;\n  }\n\n  .fc-daygrid-day-number,\n  .fc-col-header-cell-cushion {\n    color: $body-color;\n  }\n\n  .fc-daygrid-event {\n    padding: 4px;\n  }\n\n  .fc-daygrid-day.fc-day-today {\n    background-color: rgba($primary, .2);\n  }\n\n  .fc-list-event:hover td {\n    background-color: rgba($primary, .2);\n  }\n\n  .fc-list-day-text,\n  .fc-list-day-side-text {\n    color: $body-color;\n  }\n}\n\n.fc-theme-standard td, \n.fc-theme-standard th,\n.fc-theme-standard .fc-scrollgrid {\n  border-color: $border-color;\n}\n\n.fc-timegrid-event-harness-inset .fc-timegrid-event, \n.fc-timegrid-event.fc-event-mirror, \n.fc-timegrid-more-link {\n  box-shadow: none;\n}\n\n.fc-theme-standard .fc-popover {\n  background-color: $dropdown-bg;\n  border-color: $dropdown-border-color;\n  box-shadow: $dropdown-box-shadow;\n  .fc-popover-header {\n    background-color: $secondary;\n  }\n}\n\n.fc-theme-standard .fc-list-day-cushion {\n  background-color: $card-bg;\n}\n\n.fc-theme-standard .fc-list {\n  border-color: $border-color;\n}\n\n.fc-event {\n  margin-bottom: 10px;\n  padding: 8px;\n  border-radius: 2px;\n  background: rgba($primary, .2);\n  border: 0;\n  border-left: 3px solid $primary;\n  color: $body-color;\n  font-weight: 500;\n}\n\n.fc-h-event .fc-event-main {\n  color: $body-color;\n}", "// npm package: jquery.flot\n// github link: https://github.com/flot/flot\n\n.flot-chart-wrapper {\n  .flot-chart {\n    width: 100%;\n    position: relative;\n    max-width: none;\n    height: 400px;\n  }\n  @media(max-width: 767px) {\n    height: 200px;\n    min-height: 200px;\n    .flot-chart {\n      height: 100%;\n    }\n  }\n}\n\n.flot-text {\n  .flot-x-axis,\n  .flot-y-axis {\n    > div,\n    .flot-tick-label {\n      color: $body-color;\n    }\n  }\n}", "// npm package: morris.js\n// github link: https://github.com/morrisjs/morris.js\n\n.morris-hover.morris-default-style {\n  border-radius: $border-radius;\n  color: $body-color;\n  background: rgba($dropdown-bg, .9);\n  border: 1px solid $border-color;\n  font-family: $font-family-sans-serif;\n  box-shadow: $dropdown-box-shadow;\n}", "// npm package: peity\n// github link: https://github.com/benpickles/peity\n\n.peity-custom {\n  svg {\n    margin-right: 10px;\n  }\n}", "// npm package: perfect-scrollbar\n// github link: https://github.com/mdbootstrap/perfect-scrollbar\n\n.ps__thumb-x {\n\tbackground-color: lighten($card-bg, 10%);\n\theight: 4px;\n}\n\n.ps__rail-x.ps--clicking .ps__thumb-x, \n.ps__rail-x:focus>.ps__thumb-x, \n.ps__rail-x:hover>.ps__thumb-x {\n\tbackground-color: lighten($card-bg, 8%);\n\theight: 6px;\n}\n\n.ps__rail-x {\n\theight: 10px;\n}\n\n.ps__thumb-y {\n\tbackground-color: lighten($card-bg, 10%);\n\twidth: 4px;\n\t/*rtl:raw:\n  left: 2px !important;\n\tright: auto !important;\n  */\n}\n\n.ps__rail-y.ps--clicking .ps__thumb-y, \n.ps__rail-y:focus>.ps__thumb-y, \n.ps__rail-y:hover>.ps__thumb-y {\n\tbackground-color: lighten($card-bg, 8%);\n\twidth: 6px;\n}\n\n.ps__rail-y {\n\twidth: 10px;\n\t/*rtl:raw:\n  left: 0 !important;\n\tright: auto !important;\n  */\n}\n\n.ps .ps__rail-x.ps--clicking, \n.ps .ps__rail-x:focus, \n.ps .ps__rail-x:hover, \n.ps .ps__rail-y.ps--clicking, \n.ps .ps__rail-y:focus, \n.ps .ps__rail-y:hover {\n\tbackground-color: $dark;\n}", "// npm package: sweetalert2\n// github link: https://github.com/sweetalert2/sweetalert2\n\n.swal2-popup {\n  font-size: $font-size-base;\n  background: lighten($card-bg, 5%);\n  box-shadow: $card-box-shadow;\n  \n  &.swal2-toast {\n    box-shadow: $card-box-shadow;\n    background: lighten($card-bg, 5%);\n  }\n\n  .swal2-title {\n    font-size: 25px;\n    line-height: 1;\n    font-weight: 500;\n    color: $body-color;\n    margin-bottom: 0;\n  }\n\n  .swal2-html-container {\n    font-size: $font-size-base;\n    color: $text-muted;\n    font-weight: initial;\n    margin-top: 11px;\n    text-decoration: none;\n  }\n\n  .swal2-actions {\n    button {\n      @extend .btn;\n      &.swal2-confirm {\n        @extend .btn-primary;\n      }\n      &.swal2-cancel {\n        @extend .btn-danger;\n        @extend .border-danger;\n      }\n      svg {\n        width: 16px;\n        height: 16px;\n      }\n    }\n  }\n\n  .swal2-close {\n    font-size: 22px;\n    &:focus {\n      box-shadow: none;\n    }\n  }\n\n  .swal2-timer-progress-bar {\n    background: $secondary;\n  }\n\n}", "// npm package: select2\n// github link: https://github.com/select2/select2\n\n.select2-container--default {\n  .select2-selection--single, \n  .select2-selection--multiple {\n    background: $input-bg;\n    border: 1px solid $input-border-color;\n    border-radius: $border-radius;\n    @at-root #{selector-append(\".select2-container--focus\", &)} {\n      border: 1px solid $input-focus-border-color;\n    }\n  }\n}\n\n.select2-container--default .select2-selection--single .select2-selection__rendered {\n  color: $body-color;\n}\n\n.select2-dropdown {\n  background: $dropdown-bg;\n  border: 1px solid $input-focus-border-color;\n  border-radius: $border-radius;\n}\n\n.select2-container--default {\n  .select2-search--dropdown .select2-search__field {\n    @extend .form-control;\n  }\n  .select2-results__option--highlighted[aria-selected],\n  .select2-results__option[aria-selected=true] {\n    background-color: $primary;\n  }\n  .select2-search--inline .select2-search__field {\n    color: $body-color;\n  }\n}\n\n.select2-container .select2-selection--single,\n.select2-container .select2-selection--multiple {\n  height: auto;\n}\n\n.select2-container--default .select2-selection--single {\n  .select2-selection__rendered {\n    line-height: 1.5;\n    padding: $input-btn-padding-y $input-btn-padding-x;\n  }\n  .select2-selection__arrow {\n    height: 100%;\n    b {\n      left: 0;\n    }\n  }\n}\n\n.select2-container--default .select2-selection--multiple {\n  min-height: 38px;\n  .select2-selection__rendered {\n    padding: 0 6px\n  }\n  .select2-selection__choice {\n    background-color: $primary;\n    color: $white;\n    border-color: $primary;\n    padding: 1px 8px;\n    border-radius: .15rem;\n    margin-top: 5px;\n  }\n  .select2-selection__choice__remove {\n    color: $white;\n    opacity: .5;\n  }\n}\n\n.select2-container .select2-search--inline {\n  margin-top: 3px;\n}", "// npm package: easymde\n// github link: https://github.com/Ionaru/easy-markdown-editor\n\n.EasyMDEContainer .CodeMirror {\n  background: $input-bg;\n  border: 1px solid $input-border-color;\n  color: $body-color;\n  border-bottom-left-radius: 0;\n  border-bottom-right-radius: 0;\n}\n\n.editor-toolbar {\n  border: 1px solid $input-border-color;\n  border-bottom: 0;\n  border-radius: 4px 4px 0 0;\n  &, &:hover {\n    opacity: 1;\n  }\n  button {\n    color: rgba($body-color, .7) !important;\n    &:hover {\n      background: lighten($input-bg, 10%);\n      border-color: transparent;\n    }\n  }\n  i.separator {\n    border-left: 1px solid $input-border-color;\n    border-right: 1px solid $input-border-color;\n  }\n}\n\n.CodeMirror-fullscreen, \n.editor-toolbar.fullscreen,\n.editor-preview-side {\n  z-index: 999;\n}\n\n.editor-toolbar.fullscreen,\n.editor-toolbar.fullscreen::before,\n.editor-toolbar.fullscreen::after {\n  background: $input-bg;\n}\n\n.CodeMirror-cursor {\n  border-color: $body-color;\n}\n\n.editor-toolbar.disabled-for-preview button:not(.no-disable) {\n  background: $input-bg;\n}\n.editor-toolbar button.active,\n.editor-toolbar button:hover {\n  background: lighten($input-bg, 10%);\n  border-color: transparent;\n}\n\n.editor-preview, \n.editor-preview-side {\n  background: lighten($input-bg, 1%);\n}\n\n.editor-preview-side {\n  border-color: $input-border-color;\n}\n\n.editor-statusbar {\n  padding: 0 10px;\n  border: 1px solid $input-border-color;\n  border-top-color: transparent;\n  border-bottom-left-radius: $input-border-radius;\n  border-bottom-right-radius: $input-border-radius;\n}", "// npm package: jquery-tags-input\n// github link: https://github.com/xoxco/jQuery-Tags-Input\n\ndiv.tagsinput {\n  padding: 6px 6px 1px;\n  border-color: $input-border-color;\n  border-radius: $input-border-radius;\n  background: $input-bg;\n  color: $text-muted;\n  span.tag {\n    background: $primary;\n    border: 0;\n    color: $white;\n    padding: 3px 7px;\n    font-family: inherit;\n    border-radius: .15rem;\n    margin-bottom: 4px;\n    float: left;\n    /*rtl:raw:\n    margin-left: 0;\n    margin-right: 5px;\n    */\n    a {\n      font-size: 13px;\n      font-weight: 500;\n      color: $white;\n      opacity: .5;\n    }\n  }\n  #tags_addTag {\n    float: left;\n    /*rtl:raw:\n    margin-right: 5px;\n    */\n  }\n  input {\n    margin: 0;\n    padding: 1px;\n    border-radius: $border-radius;\n    @extend .text-muted;\n  }\n}", "// npm package: tinymce\n// github link: https://github.com/tinymce/tinymce\n\n.tox.tox-tinymce {\n  border: 1px solid $input-border-color;\n  border-radius: $input-border-radius;\n  .tox-menubar,\n  .tox-toolbar-overlord,\n  .tox-toolbar,\n  .tox-toolbar__overflow,\n  .tox-toolbar__primary {\n    background-color: $input-bg;\n    background-image: none;\n    border-bottom: 1px solid $input-border-color;\n  }\n  .tox-toolbar-overlord {\n    border-bottom: none;\n  }\n  &:not(.tox-tinymce-inline) .tox-editor-header {\n    padding: 0;\n    box-shadow: none;\n  }\n  .tox-edit-area__iframe {\n    background-color: $input-bg;\n  }\n  &.tox:not([dir=rtl]) .tox-toolbar__group:not(:last-of-type) {\n    border-right-color: $input-border-color;\n    border-left-color: $input-border-color; // RTL\n  }\n  .tox-statusbar {\n    background-color: $input-bg;\n    border-color: $input-border-color;\n    color: $text-muted;\n  }\n  .tox-statusbar a,\n  .tox-statusbar__path-item,\n  .tox-statusbar__wordcount {\n    color: $text-muted;\n  }\n  .tox-mbtn {\n    color: $body-color;\n  }\n  .tox-tbtn {\n    color: rgba($body-color, .7);\n  }\n  .tox-tbtn:hover {\n    background: lighten($input-bg, 10%);\n    color: $body-color;\n    svg {\n      fill: $body-color;\n    }\n  }\n  .tox-tbtn:focus:not(.tox-tbtn--disabled) {\n    color: $body-color;\n  }\n  .tox-mbtn:hover:not(:disabled):not(.tox-mbtn--active) {\n    background: lighten($input-bg, 10%);\n    color: $body-color;\n  }\n  .tox-mbtn:focus:not(:disabled),\n  .tox-mbtn--active {\n    background: lighten($input-bg, 10%);\n    color: $body-color;\n  }\n  .tox-tbtn svg {\n    fill: rgba($body-color, .7);\n  }\n  .tox-tbtn--disabled svg,\n  .tox-tbtn--disabled:hover svg,\n  .tox-tbtn:disabled svg,\n  .tox-tbtn:disabled:hover svg {\n    fill: lighten($input-bg, 10%);\n  }\n  .tox-split-button:hover {\n    box-shadow: 0 0 0 1px $input-border-color inset;\n  }\n  .tox-split-button:focus {\n    background: lighten($input-bg, 10%);\n  }\n  .tox-tbtn--enabled,\n  .tox-tbtn--enabled:hover,\n  .tox-tbtn:focus {\n    background: lighten($input-bg, 10%);\n  }\n}\n\ndiv.tox {\n  .tox-menu {\n    background-color: $dropdown-bg;\n    border-color: $dropdown-border-color;\n  }\n  .tox-collection__item {\n    color: $dropdown-color;\n  }\n  .tox-collection--list .tox-collection__item--enabled {\n    background-color: $primary;\n    color: $body-color;\n  }\n  .tox-collection--list .tox-collection__group {\n    border-color: $border-color;\n  }\n  .tox-collection--toolbar .tox-collection__item--active {\n    background-color: lighten($input-bg, 10%);\n  }\n  .tox-collection--list .tox-collection__item--active {\n    background-color: lighten($input-bg, 10%);\n  }\n  .tox-collection--toolbar .tox-collection__item--active:not(.tox-collection__item--state-disabled) {\n    color: $body-color;\n  }\n  .tox-collection--list .tox-collection__item--active:not(.tox-collection__item--state-disabled) {\n    color: $body-color;\n  }\n\n\n  .tox-dialog-wrap__backdrop {\n    background-color: rgba(0,0,0,.75);\n  }\n  .tox-dialog,\n  .tox-dialog__header,\n  .tox-dialog__footer {\n    background-color: $input-bg;\n    border-color: $border-color;\n    color: $body-color;\n  }\n  .tox-button--secondary {\n    @extend .btn-secondary;\n  }\n  .tox-button {\n    @extend .btn;\n    &:not(.tox-button--naked):not(.tox-button--secondary) {\n      @extend .btn-primary;\n    }\n  }\n  .tox-button--secondary:hover:not(:disabled) {\n    background-color: $secondary;\n    border-color: $secondary;\n    color: $body-color;\n  }\n  .tox-button--naked:hover:not(:disabled) {\n    background-color: transparent;\n    border-color: transparent;\n  }\n  .tox-button--naked.tox-button--icon:hover:not(:disabled) {\n    color: darken($body-color, 20%);\n  }\n  .tox-listboxfield .tox-listbox--select, \n  .tox-textarea, \n  .tox-textfield, \n  .tox-toolbar-textfield {\n    background-color: $input-bg;\n    border-color: $input-border-color;\n    color: $body-color;\n  }\n  .tox-listboxfield .tox-listbox--select:focus, \n  .tox-textarea:focus, \n  .tox-textfield:focus {\n    background-color: $input-bg;\n    border-color: $input-focus-border-color;\n  }\n  .tox-dialog__table tbody tr {\n    border-color: $border-color;\n  }\n  .tox-dialog__body {\n    color: $body-color;\n  }\n}", "// npm package: typeahead.js\n// github link: https://github.com/twitter/typeahead.js\n\n.typeahead.tt-input,\n.typeahead.tt-hint {\n  @extend .form-control;\n  background-color: $input-bg !important;\n  /*rtl:raw:\n  direction: rtl;\n  */\n}\n.tt-menu {\n  @extend .dropdown-menu;\n  .tt-suggestion {\n    @extend .dropdown-item;\n    cursor: pointer;\n  }\n}", "// npm package: jquery-steps\n// github link: https://github.com/rstaib/jquery-steps/\n\n.tabcontrol ul, .wizard ul {\n  display: flex;\n  @media(max-width: 676px) {\n    flex-wrap: wrap;\n  }\n}\n\n.wizard>.steps>ul>li {\n  width: auto;\n  display: flex;\n  flex-grow: 1;\n}\n\n.wizard>.steps .disabled a, \n.wizard>.steps .disabled a:active, \n.wizard>.steps .disabled a:hover {\n  background-color: lighten($input-bg, 5%);\n  color: $white;\n  border: 1px solid transparent;\n}\n\n.wizard>.steps .current a, \n.wizard>.steps .current a:active, \n.wizard>.steps .current a:hover {\n  background-color: $primary;\n  border: 1px solid transparent;\n}\n\n.wizard>.steps a, \n.wizard>.steps a:active, \n.wizard>.steps a:hover {\n  padding: $btn-padding-y $btn-padding-x;\n  border-radius: $input-border-radius;\n  width: 100%;\n}\n\n.wizard>.steps .done a, \n.wizard>.steps .done a:active, \n.wizard>.steps .done a:hover {\n  background-color: rgba($primary, .1);\n  color: $primary;\n  border: 1px solid $primary;\n}\n\n.wizard>.steps ul li {\n  a {\n    &, &:active, &:hover {\n      margin: 0 .5em .5em 0;\n    }\n  }\n  &:last-child {\n    a {\n      &, &:active, &:hover {\n        margin: 0 0 .5em 0;\n      }\n    }\n  }\n}\n\n.wizard>.steps .number {\n  font-size: inherit;\n}\n\n.wizard>.content {\n  background: $input-bg;\n  border: 1px solid $input-border-color;\n  min-height: 23em;\n  overflow: auto;\n  margin: .5em 0;\n}\n\n.wizard>.content>.body {\n  @media(max-width: 767px) {\n    width: 90%;\n    height: 90%;\n    padding: 5%;\n  }\n}\n\n.wizard>.actions {\n  /*rtl:raw:\n    text-align: left; \n  */\n}\n\n.wizard>.actions>ul>li, \n.wizard>.steps>ul>li {\n  /*rtl:raw:\n    float: right;\n  */\n}\n\n.wizard.vertical>.steps {\n  /*rtl:raw:\n    float: right;\n  */\n}\n\n.wizard>.actions a, \n.wizard>.actions a:active, \n.wizard>.actions a:hover {\n  @extend .btn;\n  @extend .btn-primary;\n}\n\n.wizard>.actions .disabled a, \n.wizard>.actions .disabled a:active, \n.wizard>.actions .disabled a:hover {\n  background: lighten($input-bg, 10%);\n  border-color: lighten($input-bg, 10%);\n  cursor: not-allowed;\n}\n\n.wizard>.actions>ul {\n  li {\n    margin-right: 0;\n    margin-left: .7em;\n  }\n}\n\n\n\n// vertical\n\n.wizard.vertical {\n  >.steps ul {\n    flex-direction: column;\n  }\n}\n\n.wizard.vertical>.content {\n  margin: 0 0 .5em 2%;\n  width: 68%;\n}\n\n.wizard.vertical>.steps a, \n.wizard.vertical>.steps a:active, \n.wizard.vertical>.steps a:hover {\n  margin: 0 0 .5em 0;\n}\n\n.wizard.vertical>.actions {\n  margin: 0;\n  width: 100%;\n}"]}