/* Wishlist Heart Icon Visual Feedback Styles */

/* Default heart icon state (not in wishlist) */
.action-btn {
    position: relative;
    display: inline-block;
    width: 40px;
    height: 40px;
    line-height: 40px;
    border-radius: 50%;
    font-size: 20px;
    color: #a6a7af;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.action-btn:hover {
    color: #ffffff;
    background-color: #2D3954;
    transform: scale(1.1);
}

/* Wishlist active state (property is in wishlist) */
.action-btn.wishlist-active {
    color: #ffffff !important;
    background-color: #2D3954 !important;
    box-shadow: 0 4px 12px rgba(45, 57, 84, 0.3);
}

.action-btn.wishlist-active:hover {
    background-color: #43C4E3 !important;
    box-shadow: 0 6px 16px rgba(67, 196, 227, 0.4);
    transform: scale(1.15);
}

/* Loading state during AJAX call */
.action-btn.wishlist-loading {
    opacity: 0.6;
    pointer-events: none;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% { opacity: 0.6; }
    50% { opacity: 0.8; }
    100% { opacity: 0.6; }
}

/* Heart icon specific styling */
.action-btn .icon-13 {
    transition: all 0.3s ease;
}

.action-btn.wishlist-active .icon-13 {
    transform: scale(1.1);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .action-btn {
        width: 35px;
        height: 35px;
        line-height: 35px;
        font-size: 18px;
    }
}

@media (max-width: 480px) {
    .action-btn {
        width: 32px;
        height: 32px;
        line-height: 32px;
        font-size: 16px;
    }
}

/* Animation for state changes */
.action-btn.state-changing {
    animation: heartBeat 0.6s ease-in-out;
}

@keyframes heartBeat {
    0% { transform: scale(1); }
    25% { transform: scale(1.2); }
    50% { transform: scale(1.1); }
    75% { transform: scale(1.15); }
    100% { transform: scale(1); }
}

/* Special styling for recommendation section hearts */
.recommendation-item .action-btn {
    border: 2px solid transparent;
}

.recommendation-item .action-btn.wishlist-active {
    border-color: #2D3954;
}

/* Property details page heart icon */
.property-details .other-option .action-btn {
    margin-right: 5px;
}

.property-details .other-option .action-btn.wishlist-active {
    background-color: #2D3954 !important;
    color: #ffffff !important;
}

/* Tooltip for wishlist status */
.action-btn[data-wishlist-status="active"]::after {
    content: "Remove from Wishlist";
    position: absolute;
    bottom: -35px;
    left: 50%;
    transform: translateX(-50%);
    background-color: #333;
    color: #fff;
    padding: 5px 8px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;
}

.action-btn[data-wishlist-status="inactive"]::after {
    content: "Add to Wishlist";
    position: absolute;
    bottom: -35px;
    left: 50%;
    transform: translateX(-50%);
    background-color: #333;
    color: #fff;
    padding: 5px 8px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;
}

.action-btn:hover::after {
    opacity: 1;
    visibility: visible;
}

/* Success feedback animation */
.action-btn.wishlist-success {
    animation: successPulse 0.8s ease-out;
}

@keyframes successPulse {
    0% { 
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(45, 57, 84, 0.7);
    }
    50% { 
        transform: scale(1.1);
        box-shadow: 0 0 0 10px rgba(45, 57, 84, 0);
    }
    100% { 
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(45, 57, 84, 0);
    }
}

/* Error feedback animation */
.action-btn.wishlist-error {
    animation: errorShake 0.6s ease-in-out;
}

@keyframes errorShake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}
