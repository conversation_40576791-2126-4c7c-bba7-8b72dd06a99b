@extends('frontend.frontend_dashboard')
@section('main')

@php
    $states = App\Models\State::latest()->get();
    $ptypes = App\Models\PropertyType::latest()->get();
@endphp


 <!--Page Title-->
        <section class="page-title-two bg-color-1 centred">
            <div class="pattern-layer">
                <div class="pattern-1" style="background-image: url({{ asset('frontend/assets/images/shape/shape-9.png') }});"></div>
                <div class="pattern-2" style="background-image: url({{ asset('frontend/assets/images/shape/shape-10.png') }});"></div>
            </div>
            <div class="auto-container">
                <div class="content-box clearfix">
                    <h1> {{ $pbread->type_name }} Properties ({{ $property->total() }} Found) </h1>
                    <ul class="bread-crumb clearfix">
                        <li><a href="{{ url('/') }}">Home</a></li>
                        <li><a href="{{ route('all.categories') }}">Categories</a></li>
                        <li>{{ $pbread->type_name }} </li>
                    </ul>
                </div>
            </div>
        </section>
        <!--End Page Title-->


        <!-- property-page-section -->
        <section class="property-page-section property-list">
            <div class="auto-container">
                <div class="row clearfix">
                    <div class="col-lg-4 col-md-12 col-sm-12 sidebar-side">
                        <div class="default-sidebar property-sidebar">
                            <div class="filter-widget sidebar-widget">
                                <div class="widget-title">
                                    <h5>Property</h5>
                                </div>
                                <div class="widget-content">
                                    <form id="property-filter-form" class="search-form">
                                        @csrf

                                        <!-- Property Type -->
                                        <div class="select-box mb-3">
                                            <select name="ptype_id" class="wide" id="property-type-filter">
                                                <option value="">All Type</option>
                                                @foreach($ptypes as $type)
                                                    <option value="{{ $type->id }}">{{ $type->type_name }}</option>
                                                @endforeach
                                            </select>
                                        </div>

                                        <!-- Location/State -->
                                        <div class="select-box mb-3">
                                            <select name="state" class="wide" id="location-filter">
                                                <option value="">Select Location</option>
                                                @foreach($states as $state)
                                                    <option value="{{ $state->id }}">{{ $state->state_name }}</option>
                                                @endforeach
                                            </select>
                                        </div>

                                        <!-- Property Status -->
                                        <div class="select-box mb-3">
                                            <select name="property_status" class="wide" id="status-filter">
                                                <option value="">All Status</option>
                                                <option value="rent">For Rent</option>
                                                <option value="buy">For Buy</option>
                                            </select>
                                        </div>

                                        <!-- Bedrooms -->
                                        <div class="select-box mb-3">
                                            <select name="bedrooms" class="wide" id="bedrooms-filter">
                                                <option value="">Max Rooms</option>
                                                <option value="2">2+ Rooms</option>
                                                <option value="3">3+ Rooms</option>
                                                <option value="4">4+ Rooms</option>
                                                <option value="5">5+ Rooms</option>
                                            </select>
                                        </div>

                                        <!-- Bathrooms -->
                                        <div class="select-box mb-3">
                                            <select name="bathrooms" class="wide" id="bathrooms-filter">
                                                <option value="">Bathrooms</option>
                                                <option value="1">1+ Bath</option>
                                                <option value="2">2+ Baths</option>
                                                <option value="3">3+ Baths</option>
                                                <option value="4">4+ Baths</option>
                                            </select>
                                        </div>

                                        <!-- Sort By -->
                                        <div class="select-box mb-3">
                                            <select name="sort_by" class="wide" id="sort-filter">
                                                <option value="">Sort By</option>
                                                <option value="latest">Latest</option>
                                                <option value="price_low">Price: Low to High</option>
                                                <option value="price_high">Price: High to Low</option>
                                                <option value="popular">Most Popular</option>
                                            </select>
                                        </div>

                                        <div class="filter-btn">
                                            <button type="submit" class="theme-btn btn-one"><i class="fas fa-filter"></i>&nbsp;Filter</button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                            <!-- Price Range Section -->
                            <div class="price-filter sidebar-widget">
                                <div class="widget-title">
                                    <h5>Select Price Range</h5>
                                </div>
                                <div class="range-slider clearfix">
                                    <form id="price-filter-form">
                                        @csrf
                                        <div class="price-input d-flex justify-content-between mb-3">
                                            <div class="field me-2 w-50">
                                                <input type="text" name="min_price" id="min-price" class="input-min form-control" placeholder="Min NPR">
                                            </div>
                                            <div class="field w-50">
                                                <input type="text" name="max_price" id="max-price" class="input-max form-control" placeholder="Max NPR">
                                            </div>
                                        </div>
                                        <div class="filter-btn">
                                            <button type="submit" class="theme-btn btn-one btn-sm w-100">Apply Price Filter</button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                            <div class="category-widget sidebar-widget">
                                <div class="widget-title">
                                    <h5>Status Of Property</h5>
                                </div>
                                <ul class="category-list clearfix">
                                    @php
                                        $rentCount = App\Models\Property::where('status', '1')->where('property_status', 'rent')->count();
                                        $buyCount = App\Models\Property::where('status', '1')->where('property_status', 'buy')->count();
                                    @endphp
                                    <li><a href="{{ route('rent.property') }}">For Rent <span>({{ $rentCount }})</span></a></li>
                                    <li><a href="{{ route('buy.property') }}">For Buy <span>({{ $buyCount }})</span></a></li>
                                </ul>
                            </div>

                        </div>
                    </div>
                    <div class="col-lg-8 col-md-12 col-sm-12 content-side">
                        <div class="property-content-side">
                            <div class="item-shorting clearfix">
                                <div class="left-column pull-left">
                                    <h5 id="search-results-count">Search Results: <span>Showing {{ count($property) }} Listings</span></h5>
                                </div>
                                <div class="right-column pull-right clearfix">
                                    <div class="loading-spinner" id="loading-spinner" style="display: none;">
                                        <i class="fas fa-spinner fa-spin"></i> Loading...
                                    </div>
                                </div>
                            </div>
                            <div class="wrapper list">
                                <div class="deals-list-content list-item" id="property-results-container">



     @foreach($property as $item)
            <div class="deals-block-one">
                <div class="inner-box">
                    <div class="image-box">
                        <figure class="image"><img src="{{ asset($item->property_thambnail  ) }}" alt=""  style="width:300px; height:350px;"></figure>
                        <div class="batch"><i class="icon-11"></i></div>
                       @if($item->featured == 1)
                        <span class="category">Featured</span>
                       @else
                        <span class="category">New</span>
                       @endif


                        <div class="buy-btn"><a href="property-details.html">For {{ $item->property_status }}</a></div>
                    </div>
                    <div class="lower-content">
         <div class="title-text"><h4><a href="{{ url('property/details/'.$item->id.'/'.$item->property_slug) }}">{{ $item->property_name }}</a></h4></div>
                        <div class="price-box clearfix">
                            <div class="price-info pull-left">
                                <h6>Start From</h6>
                                <h4>NPR {{ $item->lowest_price }}</h4>
                            </div>

  @if($item->agent_id == Null)
<div class="author-box pull-right">
        <figure class="author-thumb">
            <img src="{{ url('upload/ariyan.jpg') }}" alt="">
            <span>Admin</span>
        </figure>
    </div>
  @else

   <div class="author-box pull-right">
        <figure class="author-thumb">
            <img src="{{ (!empty($item->user->photo)) ? url('upload/agent_images/'.$item->user->photo) : url('upload/no_image.jpg') }}" alt="">
            <span>{{ $item->user->name }}</span>
        </figure>
    </div>

  @endif
                        </div>
                        <p>{{ $item->short_descp }}</p>
                        <ul class="more-details clearfix">
         <li><i class="icon-14"></i>{{ $item->bedrooms }} Beds</li>
        <li><i class="icon-15"></i>{{ $item->bathrooms }} Baths</li>
        <li><i class="icon-16"></i>{{ $item->property_size }} Sq Ft</li>
                        </ul>
                        <div class="other-info-box clearfix">
                            <div class="btn-box pull-left"><a href="{{ url('property/details/'.$item->id.'/'.$item->property_slug) }}" class="theme-btn btn-two">See Details</a></div>
                            <ul class="other-option pull-right clearfix">
             <li><a aria-label="Compare" class="action-btn" id="{{ $item->id }}" onclick="addToCompare(this.id)"><i class="icon-12"></i></a></li>

        <li><a aria-label="Add To Wishlist" class="action-btn" id="{{ $item->id }}" onclick="addToWishList(this.id)" ><i class="icon-13"></i></a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            @endforeach




                                </div>

                            </div>
                            <div class="pagination-wrapper">
                                {{ $property->links('pagination::custom') }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!-- property-page-section end -->


        <!-- subscribe-section -->
        <section class="subscribe-section bg-color-3">
            <div class="pattern-layer" style="background-image: url(assets/images/shape/shape-2.png);"></div>
            <div class="auto-container">
                <div class="row clearfix">
                    <div class="col-lg-6 col-md-6 col-sm-12 text-column">
                        <div class="text">
                            <span>Subscribe</span>
                            <h2>Sign Up To Our Newsletter To Get The Latest News And Offers.</h2>
                        </div>
                    </div>
                    <div class="col-lg-6 col-md-6 col-sm-12 form-column">
                        <div class="form-inner">
                            <form action="contact.html" method="post" class="subscribe-form">
                                <div class="form-group">
                                    <input type="email" name="email" placeholder="Enter your email" required="">
                                    <button type="submit">Subscribe Now</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!-- subscribe-section end -->

<script>
$(document).ready(function() {
    // Load initial properties
    loadProperties();

    // Handle main filter form submission
    $('#property-filter-form').on('submit', function(e) {
        e.preventDefault();
        loadProperties();
    });

    // Handle price filter form submission
    $('#price-filter-form').on('submit', function(e) {
        e.preventDefault();
        loadProperties();
    });

    // Handle filter changes on select elements
    $('#property-filter-form select').on('change', function() {
        loadProperties();
    });

    function loadProperties() {
        $('#loading-spinner').show();

        // Get all filter values
        var formData = {
            _token: $('meta[name="csrf-token"]').attr('content'),
            ptype_id: $('#property-type-filter').val(),
            state: $('#location-filter').val(),
            property_status: $('#status-filter').val(),
            bedrooms: $('#bedrooms-filter').val(),
            bathrooms: $('#bathrooms-filter').val(),
            sort_by: $('#sort-filter').val(),
            min_price: $('#min-price').val(),
            max_price: $('#max-price').val(),
            current_type_id: {{ $pbread->id ?? 'null' }}
        };

        $.ajax({
            url: '{{ route("property.type.filter") }}',
            type: 'POST',
            data: formData,
            success: function(response) {
                $('#property-results-container').html(response.html);
                $('#search-results-count').html('Search Results: <span>Showing ' + response.count + ' Listings</span>');
                $('#loading-spinner').hide();
            },
            error: function(xhr, status, error) {
                console.error('Filter error:', error);
                $('#loading-spinner').hide();
                alert('Error loading properties. Please try again.');
            }
        });
    }
});
</script>

@endsection