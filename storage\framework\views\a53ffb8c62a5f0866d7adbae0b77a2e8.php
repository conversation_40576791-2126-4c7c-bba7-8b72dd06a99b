   <?php
   $setting = App\Models\SiteSetting::find(1);
   ?>

   <header class="main-header">
            <!-- header-top -->
            <div class="header-top">
                <div class="top-inner clearfix">
                    <div class="left-column pull-left">
                        <ul class="info clearfix">
                            
                            
                            
                        </ul>
                    </div>
                    <div class="right-column pull-right">
    <ul class="social-links clearfix">
        <?php if($setting && $setting->facebook): ?>
            <li><a href="<?php echo e($setting->facebook); ?>"><i class="fab fa-facebook-f"></i></a></li>
        <?php endif; ?>
        <?php if($setting && $setting->twitter): ?>
            <li><a href="<?php echo e($setting->twitter); ?>"><i class="fab fa-twitter"></i></a></li>
        <?php endif; ?>
        <li><a href="index.html"><i class="fab fa-pinterest-p"></i></a></li>
        <li><a href="index.html"><i class="fab fa-google-plus-g"></i></a></li>
        <li><a href="index.html"><i class="fab fa-vimeo-v"></i></a></li>
    </ul>

         <?php if(auth()->guard()->check()): ?>

         <div class="sign-box">
                <a href="<?php echo e(route('dashboard')); ?>"><i class="fas fa-user"></i>Dashboard</a>
               <li>
                   <form method="POST" action="<?php echo e(route('logout')); ?>" style="display: inline;">
                       <?php echo csrf_field(); ?>
                       <button type="submit" style="background: none; border: none; padding: 0; margin: 0; font: inherit; color: inherit; cursor: pointer;">
                           <i class="fas fa-sign-out-alt"></i> Logout
                       </button>
                   </form>
               </li>
        </div>

         <?php else: ?>

         <div class="sign-box">
         <a href="<?php echo e(route('login')); ?>"><i class="fas fa-user"></i>Sign In</a>
                        </div>

         <?php endif; ?>



                    </div>
                </div>
            </div>
<!-- header-lower -->
<div class="header-lower">
<div class="outer-box">
<div class="main-box">
<div class="logo-box">
    <figure class="logo"><a href="<?php echo e(url('/')); ?>"><img src="<?php echo e(asset($setting && $setting->logo ? $setting->logo : 'frontend/assets/images/logo.png')); ?>" alt=""></a></figure>
</div>
<div class="menu-area clearfix">
    <!--Mobile Navigation Toggler-->
    <div class="mobile-nav-toggler">
        <i class="icon-bar"></i>
        <i class="icon-bar"></i>
        <i class="icon-bar"></i>
    </div>
    <nav class="main-menu navbar-expand-md navbar-light">
        <div class="collapse navbar-collapse show clearfix" id="navbarSupportedContent">
            <ul class="navigation clearfix">

     <li><a href="<?php echo e(url('/')); ?>"><span>Home</span></a> </li>
     

<li class="dropdown"><a href="index.html"><span>Property</span></a>
    <ul>
        <li><a href="<?php echo e(route('rent.property')); ?>">Rent Property</a></li>
        <li><a href="<?php echo e(route('buy.property')); ?>">Buy Property </a></li>

    </ul>
</li>
         <li><a href="<?php echo e(route('all.agents')); ?>"><span>Agents </span></a> </li>

 <li><a href="<?php echo e(route('blog.list')); ?>"><span>Blog  </span></a> </li>


     

     


            </ul>
        </div>
    </nav>
</div>

</div>
</div>
</div>

            <!--sticky Header-->
            <div class="sticky-header">
                <div class="outer-box">
                    <div class="main-box">
                        <div class="logo-box">
    <figure class="logo"><a href="<?php echo e(url('/')); ?>"><img src="<?php echo e(asset($setting && $setting->logo ? $setting->logo : 'frontend/assets/images/logo.png')); ?>" alt=""></a></figure>
                        </div>
                        <div class="menu-area clearfix">
                            <nav class="main-menu clearfix">
                                <!--Keep This Empty / Menu will come through Javascript-->
                            </nav>
                        </div>
                        
                    </div>
                </div>
            </div>
        </header><?php /**PATH C:\Users\<USER>\Desktop\FinalProject\realestate\resources\views/frontend/home/<USER>/ ?>