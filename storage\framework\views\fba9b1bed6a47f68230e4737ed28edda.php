<?php $__env->startSection('main'); ?>

<?php $__env->startSection('title'); ?>
  Search Results | RealEstate
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    // Pass filter values to JavaScript
    var propertyFilters = {
        <?php if(isset($property_status) && !empty($property_status)): ?>
        property_status: "<?php echo e($property_status); ?>",
        <?php endif; ?>

        <?php if(isset($stype) && !empty($stype)): ?>
        ptype_id: "<?php echo e($stype); ?>",
        <?php endif; ?>

        <?php if(isset($sstate) && !empty($sstate)): ?>
        state: "<?php echo e($sstate); ?>",
        <?php endif; ?>

        <?php if(isset($bedrooms) && !empty($bedrooms)): ?>
        bedrooms: "<?php echo e($bedrooms); ?>",
        <?php endif; ?>

        <?php if(isset($bathrooms) && !empty($bathrooms)): ?>
        bathrooms: "<?php echo e($bathrooms); ?>",
        <?php endif; ?>

        <?php if(isset($min_price) && !empty($min_price)): ?>
        min_price: "<?php echo e($min_price); ?>",
        <?php endif; ?>

        <?php if(isset($max_price) && !empty($max_price)): ?>
        max_price: "<?php echo e($max_price); ?>"
        <?php endif; ?>
    };
</script>
<?php $__env->stopPush(); ?>

<!--Page Title-->
<section class="page-title-two bg-color-1 centred">
    <div class="pattern-layer">
        <div class="pattern-1" style="background-image: url(<?php echo e(asset('frontend/assets/images/shape/shape-9.png')); ?>);"></div>
        <div class="pattern-2" style="background-image: url(<?php echo e(asset('frontend/assets/images/shape/shape-10.png')); ?>);"></div>
    </div>
    <div class="auto-container">
        <div class="content-box clearfix">
            <h1>Search Results</h1>
            <ul class="bread-crumb clearfix">
                <li><a href="<?php echo e(url('/')); ?>">Home</a></li>
                <li>Property Search Results</li>
            </ul>
        </div>
    </div>
</section>
<!--End Page Title-->


<!-- property-page-section -->
<section class="property-page-section property-list">
    <div class="auto-container">
        <div class="row clearfix">
            <div class="col-lg-8 col-md-12 col-sm-12 content-side">
                <div class="property-content-side">
                    <div class="item-shorting clearfix mb-4">
                        <div class="left-column pull-left">
                            <h5>Found <?php echo e(count($property)); ?> Properties</h5>
                        </div>
                        <div class="right-column pull-right clearfix">
                            <div class="short-box clearfix">
                                <div class="select-box">
                                    <select class="wide" id="view-style">
                                        <option value="list" selected>List View</option>
                                        <option value="grid">Grid View</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Applied Filters Section -->
                    <?php if(isset($property_status) || isset($stype) || isset($sstate) || isset($bedrooms) || isset($bathrooms) || isset($min_price) || isset($max_price)): ?>
                    <div class="applied-filters mb-4">
                        <h6 class="mb-2">Applied Filters:</h6>
                        <div class="filter-tags">
                            <?php if(isset($property_status) && !empty($property_status)): ?>
                                <span class="badge bg-info text-white p-2 me-2">Status: <?php echo e(ucfirst($property_status)); ?></span>
                            <?php endif; ?>

                            <?php if(isset($stype) && !empty($stype)): ?>
                                <span class="badge bg-info text-white p-2 me-2">Type: <?php echo e($stype); ?></span>
                            <?php endif; ?>

                            <?php if(isset($sstate) && !empty($sstate)): ?>
                                <span class="badge bg-info text-white p-2 me-2">Location: <?php echo e($sstate); ?></span>
                            <?php endif; ?>

                            <?php if(isset($bedrooms) && !empty($bedrooms)): ?>
                                <span class="badge bg-info text-white p-2 me-2">Bedrooms: <?php echo e($bedrooms); ?></span>
                            <?php endif; ?>

                            <?php if(isset($bathrooms) && !empty($bathrooms)): ?>
                                <span class="badge bg-info text-white p-2 me-2">Bathrooms: <?php echo e($bathrooms); ?></span>
                            <?php endif; ?>

                            <?php if(isset($min_price) && !empty($min_price)): ?>
                                <span class="badge bg-info text-white p-2 me-2">Min Price: NPR <?php echo e(number_format($min_price)); ?></span>
                            <?php endif; ?>

                            <?php if(isset($max_price) && !empty($max_price)): ?>
                                <span class="badge bg-info text-white p-2 me-2">Max Price: NPR <?php echo e(number_format($max_price)); ?></span>
                            <?php endif; ?>

                            <a href="<?php echo e(url('/properties')); ?>" class="btn btn-sm btn-danger">Clear All</a>
                        </div>
                    </div>
                    <?php endif; ?>

                    <div class="wrapper list">
                        <div class="deals-list-content list-item">

                        <?php if(count($property) > 0): ?>
                            <?php $__currentLoopData = $property; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="deals-block-one">
                                <div class="inner-box">
                                    <div class="image-box">
                                        <figure class="image"><img src="<?php echo e(asset($item->property_thambnail)); ?>" alt="" style="width:300px; height:350px;"></figure>
                                        <div class="batch"><i class="icon-11"></i></div>
                                        <?php if($item->featured == 1): ?>
                                        <span class="category">Featured</span>
                                        <?php else: ?>
                                        <span class="category">New</span>
                                        <?php endif; ?>
                                        <div class="buy-btn"><a href="#">For <?php echo e($item->property_status); ?></a></div>
                                    </div>
                                    <div class="lower-content">
                                        <div class="title-text"><h4><a href="<?php echo e(url('property/details/'.$item->id.'/'.$item->property_slug)); ?>"><?php echo e($item->property_name); ?></a></h4></div>
                                        <div class="price-box clearfix">
                                            <div class="price-info pull-left">
                                                <h6>Start From</h6>
                                                <h4>NPR <?php echo e(number_format($item->lowest_price)); ?></h4>
                                            </div>

                                            <?php if($item->agent_id == Null): ?>
                                            <div class="author-widget pull-right">
                                                <figure class="author-thumb"><img src="<?php echo e(url('upload/ariyan.jpg')); ?>" alt=""></figure><span>Admin</span>
                                            </div>
                                            <?php else: ?>
                                            <div class="author-widget pull-right">
                                                <figure class="author-thumb"><img src="<?php echo e((!empty($item->user->photo)) ? url('upload/agent_images/'.$item->user->photo) : url('upload/no_image.jpg')); ?>" alt=""></figure><span><?php echo e($item->user->name); ?></span>
                                            </div>
                                            <?php endif; ?>
                                        </div>
                                        <p><?php echo e($item->short_descp); ?></p>
                                        <ul class="more-details clearfix">
                                            <li><i class="icon-14"></i><?php echo e($item->bedrooms); ?> Beds</li>
                                            <li><i class="icon-15"></i><?php echo e($item->bathrooms); ?> Baths</li>
                                            <li><i class="icon-16"></i><?php echo e($item->property_size); ?> Sq Ft</li>
                                        </ul>
                                        <div class="other-info-box clearfix">
                                            <div class="btn-box pull-left"><a href="<?php echo e(url('property/details/'.$item->id.'/'.$item->property_slug)); ?>" class="theme-btn btn-two">See Details</a></div>
                                            <ul class="other-option pull-right clearfix">
                                                <li><a aria-label="Compare" class="action-btn" id="<?php echo e($item->id); ?>" onclick="addToCompare(this.id)"><i class="icon-12"></i></a></li>
                                                <li><a aria-label="Add To Wishlist" class="action-btn" id="<?php echo e($item->id); ?>" onclick="addToWishList(this.id)" ><i class="icon-13"></i></a></li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php else: ?>
                            <div class="deals-block-one">
                                <div class="inner-box text-center p-4">
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle fa-2x mb-3"></i>
                                        <h4>No properties found matching your criteria.</h4>
                                        <p>Try adjusting your filters or <a href="<?php echo e(url('/properties')); ?>" class="alert-link">view all properties</a>.</p>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>

                        </div>
                    </div>

                    
                    

                </div>
            </div>
            <div class="col-lg-4 col-md-12 col-sm-12 sidebar-side">
                 <?php echo $__env->make('frontend.property.property_sidebar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            </div>
        </div>
    </div>
</section>
<!-- property-page-section end -->

<?php $__env->stopSection(); ?>
<?php echo $__env->make('frontend.frontend_dashboard', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\FinalProject\realestate\resources\views/frontend/property/property_search.blade.php ENDPATH**/ ?>