/**
 * RealShed Chatbot Styles
 * Responsive design matching RealShed's color scheme (#2D3954 and #43C4E3)
 */

/* Chat Bubble - Floating button */
.chat-bubble {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #43C4E3 0%, #2D3954 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 4px 20px rgba(67, 196, 227, 0.4);
    z-index: 1000;
    transition: all 0.3s ease;
    animation: pulse 2s infinite;
}

.chat-bubble:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 25px rgba(67, 196, 227, 0.6);
}

.chat-bubble i {
    color: white;
    font-size: 24px;
}

.chat-notification {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #ff4757;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    animation: bounce 1s infinite;
}

@keyframes pulse {
    0% { box-shadow: 0 4px 20px rgba(67, 196, 227, 0.4); }
    50% { box-shadow: 0 4px 20px rgba(67, 196, 227, 0.8); }
    100% { box-shadow: 0 4px 20px rgba(67, 196, 227, 0.4); }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-5px); }
    60% { transform: translateY(-3px); }
}

/* Chat Widget - Main container */
.chat-widget {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 350px;
    height: 500px;
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 40px rgba(45, 57, 84, 0.2);
    z-index: 1001;
    display: flex;
    flex-direction: column;
    transform: translateY(100%) scale(0.8);
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    overflow: hidden;
}

.chat-widget-open {
    transform: translateY(0) scale(1);
    opacity: 1;
}

/* Chat Header */
.chat-header {
    background: linear-gradient(135deg, #2D3954 0%, #43C4E3 100%);
    color: white;
    padding: 15px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-radius: 15px 15px 0 0;
}

.chat-header-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.chat-logo {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    background: white;
    padding: 5px;
}

.chat-header-info h4 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.chat-status {
    font-size: 12px;
    opacity: 0.9;
}

.chat-close {
    background: none;
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: background 0.2s ease;
}

.chat-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* Chat Messages Area */
.chat-messages {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background: #f8f9fa;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.chat-messages::-webkit-scrollbar-thumb {
    background: #43C4E3;
    border-radius: 3px;
}

/* Message Styles */
.message {
    display: flex;
    flex-direction: column;
    max-width: 85%;
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.user-message {
    align-self: flex-end;
}

.bot-message {
    align-self: flex-start;
}

.message-content {
    padding: 12px 16px;
    border-radius: 18px;
    font-size: 14px;
    line-height: 1.4;
    word-wrap: break-word;
}

.user-message .message-content {
    background: linear-gradient(135deg, #43C4E3 0%, #2D3954 100%);
    color: white;
    border-bottom-right-radius: 6px;
}

.bot-message .message-content {
    background: white;
    color: #2D3954;
    border: 1px solid #e9ecef;
    border-bottom-left-radius: 6px;
    box-shadow: 0 2px 8px rgba(45, 57, 84, 0.1);
}

.message-content strong {
    color: #2D3954;
    font-weight: 600;
}

.bot-message .message-content strong {
    color: #43C4E3;
}

.message-content ul {
    margin: 8px 0;
    padding-left: 20px;
}

.message-content li {
    margin: 4px 0;
}

.message-time {
    font-size: 11px;
    color: #6c757d;
    margin-top: 4px;
    align-self: flex-end;
}

.user-message .message-time {
    align-self: flex-end;
}

.bot-message .message-time {
    align-self: flex-start;
}

/* Chat Input Area */
.chat-input-container {
    padding: 15px 20px;
    background: white;
    border-top: 1px solid #e9ecef;
    display: flex;
    gap: 10px;
    align-items: center;
}

#chat-input {
    flex: 1;
    border: 1px solid #e9ecef;
    border-radius: 25px;
    padding: 12px 16px;
    font-size: 14px;
    outline: none;
    transition: border-color 0.2s ease;
}

#chat-input:focus {
    border-color: #43C4E3;
    box-shadow: 0 0 0 3px rgba(67, 196, 227, 0.1);
}

.chat-send-btn {
    background: linear-gradient(135deg, #43C4E3 0%, #2D3954 100%);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.chat-send-btn:hover {
    transform: scale(1.1);
}

.chat-send-btn i {
    color: white;
    font-size: 16px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .chat-widget {
        width: calc(100vw - 40px);
        height: calc(100vh - 40px);
        bottom: 20px;
        right: 20px;
        left: 20px;
        max-width: none;
    }
    
    .chat-bubble {
        bottom: 30px;
        right: 30px;
    }
}

@media (max-width: 480px) {
    .chat-widget {
        width: calc(100vw - 20px);
        height: calc(100vh - 20px);
        bottom: 10px;
        right: 10px;
        left: 10px;
        border-radius: 10px;
    }
    
    .chat-header {
        padding: 12px 15px;
        border-radius: 10px 10px 0 0;
    }
    
    .chat-messages {
        padding: 15px;
    }
    
    .chat-input-container {
        padding: 12px 15px;
    }
    
    .chat-bubble {
        width: 50px;
        height: 50px;
        bottom: 20px;
        right: 20px;
    }
    
    .chat-bubble i {
        font-size: 20px;
    }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    .chat-bubble,
    .chat-widget,
    .message {
        animation: none;
        transition: none;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .chat-widget {
        border: 2px solid #000;
    }
    
    .message-content {
        border: 1px solid #000;
    }
}
