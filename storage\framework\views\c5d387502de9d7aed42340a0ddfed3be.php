<?php $__env->startSection('main'); ?>

<?php $__env->startPush('scripts'); ?>
<script src="<?php echo e(asset('frontend/assets/js/mortgage-calculator-new.js')); ?>"></script>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('styles'); ?>
<link rel="stylesheet" href="<?php echo e(asset('frontend/assets/css/suggested-properties.css')); ?>">
<?php $__env->stopPush(); ?>

<?php $__env->startSection('title'); ?>
  <?php echo e($property->property_name); ?> | Easy RealEstate
<?php $__env->stopSection(); ?>


   <!--Page Title-->
        <section class="page-title-two bg-color-1 centred">
            <div class="pattern-layer">
                <div class="pattern-1" style="background-image: url(<?php echo e(asset('frontend/assets/images/shape/shape-9.png')); ?>);"></div>
                <div class="pattern-2" style="background-image: url(<?php echo e(asset('frontend/assets/images/shape/shape-10.png')); ?>);"></div>
            </div>
            <div class="auto-container">
                <div class="content-box clearfix">
                    <h1><?php echo e($property->property_name); ?></h1>
                    <ul class="bread-crumb clearfix">
                        <li><a href="index.html">Home</a></li>
                        <li><?php echo e($property->property_name); ?></li>
                    </ul>
                </div>
            </div>
        </section>
        <!--End Page Title-->


        <!-- property-details -->
        <section class="property-details property-details-one">
            <div class="auto-container">
                <div class="top-details clearfix">
                    <div class="left-column pull-left clearfix">
                        <h3><?php echo e($property->property_name); ?></h3>
                        <div class="author-info clearfix">
                            <div class="author-box pull-left">
                  <?php if($property->agent_id == Null): ?>
  <figure class="author-thumb"><img src="<?php echo e(url('upload/ariyan.jpg')); ?>" alt=""></figure>
                      <h6>Admin</h6>
                  <?php else: ?>

                    <figure class="author-thumb"><img src="<?php echo e((!empty($property->user->photo)) ? url('upload/agent_images/'.$property->user->photo) : url('upload/no_image.jpg')); ?>" alt=""></figure>
                                <h6><?php echo e($property->user->name); ?></h6>

                  <?php endif; ?>



                            </div>
                            <ul class="rating clearfix pull-left">
                                <li><i class="icon-39"></i></li>
                                <li><i class="icon-39"></i></li>
                                <li><i class="icon-39"></i></li>
                                <li><i class="icon-39"></i></li>
                                <li><i class="icon-40"></i></li>
                            </ul>
                        </div>
                    </div>
                    <div class="right-column pull-right clearfix">
                        <div class="price-inner clearfix">
                            <ul class="category clearfix pull-left">
     <li><a href="property-details.html"><?php echo e($property->type->type_name); ?></a></li>
                                <li><a href="property-details.html">For <?php echo e($property->property_status); ?></a></li>
                            </ul>
                            <div class="price-box pull-right">
                                <h3>NPR <?php echo e($property->lowest_price); ?></h3>
                            </div>
                        </div>
                        <ul class="other-option pull-right clearfix">
                            <li><a aria-label="Share" class="action-btn"><i class="icon-37"></i></a></li>
                            <li><a aria-label="Print" class="action-btn"><i class="icon-38"></i></a></li>
                            <li><a aria-label="Compare" class="action-btn" id="<?php echo e($property->id); ?>" onclick="addToCompare(this.id)"><i class="icon-12"></i></a></li>
                            <li><a aria-label="Add To Wishlist" class="action-btn" id="<?php echo e($property->id); ?>" onclick="addToWishList(this.id)"><i class="icon-13"></i></a></li>
                        </ul>
                    </div>
                </div>
                <div class="row clearfix">
                    <div class="col-lg-8 col-md-12 col-sm-12 content-side">
                        <div class="property-details-content">
    <div class="carousel-inner">
        <div class="single-item-carousel owl-carousel owl-theme owl-dots-none">
        	<?php $__currentLoopData = $multiImage; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $img): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <figure class="image-box"><img src="<?php echo e(asset($img->photo_name)); ?>" alt=""></figure>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
                            <div class="discription-box content-widget">
                                <div class="title-box">
                                    <h4>Property Description</h4>
                                </div>
                                <div class="text">
                                    <p><?php echo $property->long_descp; ?></p>
                                </div>
                            </div>
                            <div class="details-box content-widget">
                                <div class="title-box">
                                    <h4>Property Details</h4>
                                </div>
    <ul class="list clearfix">
        <li>Property ID: <span><?php echo e($property->property_code); ?></span></li>
        <li>Rooms: <span><?php echo e($property->bedrooms); ?></span></li>
        <li>Garage Size: <span><?php echo e($property->garage_size); ?> Sq Ft</span></li>

        <li>Property Type: <span><?php echo e($property->type->type_name); ?></span></li>
        <li>Bathrooms: <span><?php echo e($property->bathrooms); ?></span></li>
        <li>Property Status: <span>For <?php echo e($property->property_status); ?></span></li>
        <li>Property Size: <span><?php echo e($property->property_size); ?> Sq Ft</span></li>
        <li>Garage: <span><?php echo e($property->garage); ?></span></li>
    </ul>
                            </div>
                            <div class="amenities-box content-widget">
                                <div class="title-box">
                                    <h4>Amenities</h4>
                                </div>
                                <ul class="list clearfix">
                                	<?php $__currentLoopData = $property_amen; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $amen): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <li><?php echo e($amen); ?></li>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </ul>
                            </div>

                            <div class="location-box content-widget">
                                <div class="title-box">
                                    <h4>Location</h4>
                                </div>
<ul class="info clearfix">
    <li><span>Address:</span> <?php echo e($property->address); ?></li>
    <li><span>State/county:</span> <?php echo e($property->state_name); ?></li>
    <li><span>Neighborhood:</span> <?php echo e($property->neighborhood); ?></li>
    <li><span>Zip/Postal Code:</span> <?php echo e($property->postal_code); ?></li>
    <li><span>City:</span> <?php echo e($property->city); ?></li>
</ul>
<div class="google-map-area">
    <div class="map-container" style="width: 100%; height: 400px; border-radius: 10px; overflow: hidden; position: relative;">
        <!-- Primary Google Maps Embed API -->
        <iframe
            id="google-map-iframe"
            width="100%"
            height="100%"
            style="border:0; border-radius: 10px;"
            loading="lazy"
            allowfullscreen
            referrerpolicy="no-referrer-when-downgrade"
            src="https://www.google.com/maps/embed/v1/place?key=AIzaSyBwjrCx-T4UlUDmXALumCOWkJv-B7m9yCE&q=<?php echo e($property->city); ?>, Nepal&zoom=13&maptype=roadmap&language=en&region=NP">
        </iframe>

        <!-- Fallback static map in case the iframe fails -->
        <div id="static-map-fallback" style="display: none; width: 100%; height: 100%; border-radius: 10px; background-position: center; background-size: cover;"
             title="<?php echo e($property->property_name); ?> - <?php echo e($property->address); ?>, <?php echo e($property->city); ?>">
        </div>

        <!-- Loading indicator and error message -->
        <div id="map-loading" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; display: flex; justify-content: center; align-items: center; background-color: #f8f9fa; border-radius: 10px; z-index: -1;">
            <div style="text-align: center; padding: 20px;">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p id="map-error-message">Loading map...</p>
            </div>
        </div>
    </div>

    <div class="map-info mt-2">
        <p><i class="fas fa-map-marker-alt text-danger"></i> <strong><?php echo e($property->property_name); ?></strong> - Located in <?php echo e($property->city); ?>, Nepal</p>
        <p class="text-muted small">Address: <?php echo e($property->address); ?>, <?php echo e($property->city); ?></p>
    </div>
</div>

<script>
    // Check if the Google Maps iframe loaded correctly
    document.addEventListener('DOMContentLoaded', function() {
        var mapIframe = document.getElementById('google-map-iframe');
        var staticMapFallback = document.getElementById('static-map-fallback');
        var mapLoading = document.getElementById('map-loading');
        var mapErrorMessage = document.getElementById('map-error-message');

        // Set up a fallback static map using the city
        var city = "<?php echo e($property->city); ?>, Nepal";
        var encodedAddress = encodeURIComponent(city);
        var zoom = 13;

        // Use the address for the static map
        var staticMapUrl = 'https://maps.googleapis.com/maps/api/staticmap?center=' + encodedAddress +
                           '&zoom=' + zoom + '&size=600x400&maptype=roadmap&markers=color:red%7C' + encodedAddress +
                           '&key=AIzaSyBwjrCx-T4UlUDmXALumCOWkJv-B7m9yCE';

        // Fallback to coordinates if available
        var lat = <?php echo e($property->latitude ?? 27.7172); ?>;
        var lng = <?php echo e($property->longitude ?? 85.3240); ?>;

        // Alternative OpenStreetMap URL if Google Static Maps also fails
        var openStreetMapUrl = 'https://www.openstreetmap.org/export/embed.html?bbox=' +
                              (lng - 0.01) + '%2C' + (lat - 0.01) + '%2C' +
                              (lng + 0.01) + '%2C' + (lat + 0.01) +
                              '&layer=mapnik&marker=' + lat + '%2C' + lng;

        // Set the background image for the static fallback
        staticMapFallback.style.backgroundImage = 'url("' + staticMapUrl + '")';

        // Function to handle iframe load error
        function handleMapError() {
            mapIframe.style.display = 'none';
            staticMapFallback.style.display = 'block';
            mapLoading.style.zIndex = '-1';

            // Create a link to view on Google Maps
            var viewOnGoogleMapsLink = document.createElement('a');
            viewOnGoogleMapsLink.href = 'https://www.google.com/maps/search/?api=1&query=' + encodedAddress;
            viewOnGoogleMapsLink.target = '_blank';
            viewOnGoogleMapsLink.className = 'btn btn-sm btn-primary mt-2 me-2';
            viewOnGoogleMapsLink.innerHTML = 'View on Google Maps';
            staticMapFallback.appendChild(viewOnGoogleMapsLink);

            // Add a fallback to OpenStreetMap if the static image fails to load
            var img = new Image();
            img.onerror = function() {
                console.log('Google Static Maps failed to load, using OpenStreetMap fallback');
                // Create an iframe for OpenStreetMap
                var openStreetMapIframe = document.createElement('iframe');
                openStreetMapIframe.width = '100%';
                openStreetMapIframe.height = '100%';
                openStreetMapIframe.style.border = '0';
                openStreetMapIframe.style.borderRadius = '10px';
                openStreetMapIframe.src = openStreetMapUrl;

                // Replace the static map div content with the OpenStreetMap iframe
                staticMapFallback.innerHTML = '';
                staticMapFallback.appendChild(openStreetMapIframe);

                // Re-add the Google Maps link
                staticMapFallback.appendChild(viewOnGoogleMapsLink);

                // Add an OpenStreetMap link
                var viewOnOsmLink = document.createElement('a');
                viewOnOsmLink.href = 'https://www.openstreetmap.org/?mlat=' + lat + '&mlon=' + lng + '&zoom=' + zoom;
                viewOnOsmLink.target = '_blank';
                viewOnOsmLink.className = 'btn btn-sm btn-info mt-2';
                viewOnOsmLink.innerHTML = 'View on OpenStreetMap';
                staticMapFallback.appendChild(viewOnOsmLink);

                mapErrorMessage.innerHTML = 'Using OpenStreetMap as fallback.';
            };
            img.src = staticMapUrl;

            console.log('Google Maps iframe failed to load, using static map fallback');
        }

        // Check if the iframe loaded correctly after a timeout
        setTimeout(function() {
            try {
                // If we can access the iframe content, it loaded correctly
                if (mapIframe.contentWindow.document) {
                    mapLoading.style.zIndex = '-1';
                    console.log('Google Maps iframe loaded successfully');
                }
            } catch (e) {
                // If we can't access the iframe content, it failed to load
                handleMapError();
                mapErrorMessage.innerHTML = 'Could not load interactive map. Displaying static map instead.';
            }
        }, 3000);

        // Also set up an error event listener for the iframe
        mapIframe.addEventListener('error', function() {
            handleMapError();
            mapErrorMessage.innerHTML = 'Error loading map. Displaying static map instead.';
        });
    });
</script>
                            </div>
                            <div class="nearby-box content-widget">
                                <div class="title-box">
                                    <h4>What's Nearby?</h4>
                                </div>
<div class="inner-box">


    <div class="single-item">
        <div class="icon-box"><i class="fas fa-book-reader"></i></div>
        <div class="inner">
            <h5>Places:</h5>

            <?php $__currentLoopData = $facility; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="box clearfix">
                <div class="text pull-left">
                    <h6><?php echo e($item->facility_name); ?> <span>(<?php echo e($item->distance); ?> km)</span></h6>
                </div>
                <ul class="rating pull-right clearfix">
                    <li><i class="icon-39"></i></li>
                    <li><i class="icon-39"></i></li>
                    <li><i class="icon-39"></i></li>
                    <li><i class="icon-39"></i></li>
                    <li><i class="icon-40"></i></li>
                </ul>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>




                                </div>
                            </div>
                            <div class="statistics-box content-widget">
                                <div class="title-box">
                                    <h4>Property Video </h4>
                                </div>
<figure class="image-box">
   <iframe width="700" height="415" src="<?php echo e($property->property_video); ?>" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" allowfullscreen></iframe>
</figure>
                            </div>


    <div class="schedule-box content-widget">
        <div class="title-box">
            <h4>Schedule A Tour</h4>
        </div>
        <div class="form-inner">
            <form action="<?php echo e(route('store.schedule')); ?>" method="post">
                <?php echo csrf_field(); ?>


                <div class="row clearfix">

  <input type="hidden" name="property_id" value="<?php echo e($property->id); ?>">

  <?php if($property->agent_id == Null): ?>
  <input type="hidden" name="agent_id" value="">
  <?php else: ?>
<input type="hidden" name="agent_id" value="<?php echo e($property->agent_id); ?>">
  <?php endif; ?>

                    <div class="col-lg-6 col-md-12 col-sm-12 column">
                        <div class="form-group">
                            <i class="far fa-calendar-alt"></i>
                            <input type="text" name="tour_date" placeholder="Tour Date" id="datepicker">
                        </div>
                    </div>
                    <div class="col-lg-6 col-md-12 col-sm-12 column">
                        <div class="form-group">
                            <i class="far fa-clock"></i>
                            <input type="text" name="tour_time" placeholder="Any Time">
                        </div>
                    </div>

                    <div class="col-lg-12 col-md-12 col-sm-12 column">
                        <div class="form-group">
                            <textarea name="message" placeholder="Your message"></textarea>
                        </div>
                    </div>
                    <div class="col-lg-12 col-md-12 col-sm-12 column">
                        <div class="form-group message-btn">
                            <button type="submit" class="theme-btn btn-one">Submit Now</button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
                        </div>
                    </div>


                    <div class="col-lg-4 col-md-12 col-sm-12 sidebar-side">
    <div class="property-sidebar default-sidebar">
        <div class="author-widget sidebar-widget">
            <div class="author-box">

             <?php if($property->agent_id == Null): ?>

              <figure class="author-thumb"><img src="<?php echo e(url('upload/ariyan.jpg')); ?>" alt=""></figure>
                <div class="inner">
                    <h4>Admin </h4>
                    <ul class="info clearfix">
                        <li><i class="fas fa-map-marker-alt"></i>84 St. John Wood High Street,
                        St Johns Wood</li>
                        <li><i class="fas fa-phone"></i><a href="tel:03030571965">030 3057 1965</a></li>
                    </ul>
                    <div class="btn-box"><a href="agents-details.html">View Listing</a></div>
                </div>

             <?php else: ?>

              <figure class="author-thumb"><img src="<?php echo e((!empty($property->user->photo)) ? url('upload/agent_images/'.$property->user->photo) : url('upload/no_image.jpg')); ?>" alt=""></figure>
                <div class="inner">
                    <h4><?php echo e($property->user->name); ?></h4>
                    <ul class="info clearfix">
                        <li><i class="fas fa-map-marker-alt"></i><?php echo e($property->user->address); ?></li>
                        <li><i class="fas fa-phone"></i><a href="tel:03030571965"><?php echo e($property->user->phone); ?></a></li>
                    </ul>


   <?php if(auth()->guard()->check()): ?>
  <div class="btn-box mt-2" style="text-align: center;">
    <a href="<?php echo e(route('user.chat.property', ['propertyId' => $property->id])); ?>" class="theme-btn btn-one">Chat With Agent</a>
  </div>
  <?php else: ?>
  <div class="mt-2" style="text-align: center;">
    <span class="text-danger">Login to Chat with Agent</span>
  </div>
  <?php endif; ?>






                </div>

             <?php endif; ?>

            </div>



    <div class="form-inner">
<?php if(auth()->guard()->check()): ?>

<?php
    $id = Auth::user()->id;
    $userData = App\Models\User::find($id);
?>

 <form action="<?php echo e(route('property.message')); ?>" method="post" class="default-form">
    <?php echo csrf_field(); ?>

    <input type="hidden" name="property_id" value="<?php echo e($property->id); ?>">

    <?php if($property->agent_id == Null): ?>
    <input type="hidden" name="agent_id" value="">

    <?php else: ?>
    <input type="hidden" name="agent_id" value="<?php echo e($property->agent_id); ?>">
    <?php endif; ?>

            <div class="form-group">
                <input type="text" name="msg_name" placeholder="Your name" value="<?php echo e($userData->name); ?>">
            </div>
            <div class="form-group">
                <input type="email" name="msg_email" placeholder="Your Email" value="<?php echo e($userData->email); ?>">
            </div>
            <div class="form-group">
                <input type="text" name="msg_phone" placeholder="Phone" value="<?php echo e($userData->phone); ?>">
            </div>
            <div class="form-group">
                <textarea name="message" placeholder="Message"></textarea>
            </div>
            <div class="form-group message-btn">
                <button type="submit" class="theme-btn btn-one">Send Message</button>
            </div>
        </form>

<?php else: ?>

<form action="<?php echo e(route('property.message')); ?>" method="post" class="default-form">
    <?php echo csrf_field(); ?>

    <input type="hidden" name="property_id" value="<?php echo e($property->id); ?>">

    <?php if($property->agent_id == Null): ?>
    <input type="hidden" name="agent_id" value="">

    <?php else: ?>
    <input type="hidden" name="agent_id" value="<?php echo e($property->agent_id); ?>">
    <?php endif; ?>

            <div class="form-group">
                <input type="text" name="msg_name" placeholder="Your name" required="">
            </div>
            <div class="form-group">
                <input type="email" name="msg_email" placeholder="Your Email" required="">
            </div>
            <div class="form-group">
                <input type="text" name="msg_phone" placeholder="Phone" required="">
            </div>
            <div class="form-group">
                <textarea name="message" placeholder="Message"></textarea>
            </div>
            <div class="form-group message-btn">
                <button type="submit" class="theme-btn btn-one">Send Message</button>
            </div>
        </form>

<?php endif; ?>



    </div>



</div>


                            <div class="calculator-widget sidebar-widget">
                                <div class="calculate-inner">
                                    <div class="widget-title">
                                        <h4>Mortgage Calculator</h4>
                                    </div>
                                    <form id="mortgage-calculator-form" class="default-form">
                                        <div class="form-group">
                                            <i class="fas fa-rupee-sign"></i>
                                            <input type="text" id="total-amount" class="currency-input" placeholder="Total Amount (NPR)" value="<?php echo e($property->lowest_price); ?>">
                                            <div id="property-price" data-price="<?php echo e($property->lowest_price); ?>"></div>
                                        </div>
                                        <div class="form-group">
                                            <i class="fas fa-percent"></i>
                                            <input type="text" id="down-payment" class="percentage-input" placeholder="Down Payment %" value="20">
                                        </div>
                                        <div class="form-group">
                                            <i class="fas fa-percent"></i>
                                            <input type="text" id="interest-rate" class="percentage-input" placeholder="Interest Rate %" value="5.5">
                                        </div>
                                        <div class="form-group">
                                            <i class="far fa-calendar-alt"></i>
                                            <input type="text" id="loan-term" placeholder="Loan Term (Years)" value="30">
                                        </div>
                                        <div class="form-group">
                                            <div class="select-box">
                                                <select id="payment-frequency" class="wide">
                                                   <option value="monthly" selected>Monthly</option>
                                                   <option value="biweekly">Bi-Weekly</option>
                                                   <option value="weekly">Weekly</option>
                                                   <option value="yearly">Yearly</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="form-group message-btn">
                                            <button id="calculate-mortgage" class="theme-btn btn-one">Calculate</button>
                                        </div>

                                        <div class="mortgage-results" style="display: none;">
                                            <div class="result-box">
                                                <h5 id="payment-label">Monthly Payment</h5>
                                                <h3 id="payment-amount">$0.00</h3>
                                            </div>
                                            <div class="result-details">
                                                <div class="detail-item">
                                                    <span>Loan Amount:</span>
                                                    <strong id="loan-amount">$0.00</strong>
                                                </div>
                                                <div class="detail-item">
                                                    <span>Total Interest:</span>
                                                    <strong id="total-interest">$0.00</strong>
                                                </div>
                                                <div class="detail-item">
                                                    <span>Total Payment:</span>
                                                    <strong id="total-payment">$0.00</strong>
                                                </div>
                                            </div>
                                            <div class="reset-btn text-center mt-3">
                                                <button id="reset-calculator" class="btn btn-sm btn-outline-secondary">Reset</button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>



                <!-- Suggested Properties Section -->
                <div class="suggested-content">
                    <div class="title">
                        <h4>Suggested For You
                        <?php if(Auth::check()): ?>
                            <!-- <span class="personalized-indicator">Personalized</span> -->
                        <?php endif; ?>
                        </h4>
                        <p class="text-muted">
                            <?php if(Auth::check()): ?>
                                Properties in <?php echo e($property->state_name); ?> tailored to your browsing history and preferences
                            <?php else: ?>
                                Properties in <?php echo e($property->state_name); ?> similar to what you're viewing
                            <?php endif; ?>
                        </p>
                    </div>

                    <?php if($hasSuggestions): ?>
                    <div class="row clearfix">
                        <?php $__currentLoopData = $suggestedProperties; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="col-lg-4 col-md-6 col-sm-12 feature-block">
                            <div class="feature-block-one wow fadeInUp animated" data-wow-delay="00ms" data-wow-duration="1500ms">
                                <div class="inner-box">
                                    <div class="image-box">
                                        <figure class="image"><img src="<?php echo e(asset($item->property_thambnail)); ?>" alt=""></figure>
                                        <div class="batch"><i class="icon-11"></i></div>
                                        <span class="category"><?php echo e($item->type->type_name); ?></span>
                                        <span class="suggested-badge">Suggested</span>
                                    </div>
                                    <div class="lower-content">
                                        <div class="author-info clearfix">
                                            <div class="author pull-left">
                                              <?php if($item->agent_id == Null): ?>
                                                <figure class="author-thumb"><img src="<?php echo e(url('upload/ariyan.jpg')); ?>" alt=""></figure>
                                                <h6>Admin</h6>
                                              <?php else: ?>
                                                <figure class="author-thumb"><img src="<?php echo e((!empty($item->user->photo)) ? url('upload/agent_images/'.$item->user->photo) : url('upload/no_image.jpg')); ?>" alt=""></figure>
                                                <h6><?php echo e($item->user->name); ?></h6>
                                              <?php endif; ?>
                                            </div>
                                            <div class="buy-btn pull-right"><a href="javascript:void(0)">For <?php echo e($item->property_status); ?></a></div>
                                        </div>
                                        <div class="title-text"><h4><a href="<?php echo e(url('property/details/'.$item->id.'/'.$item->property_slug)); ?>"><?php echo e($item->property_name); ?></a></h4></div>
                                        <div class="price-box clearfix">
                                            <div class="price-info pull-left">
                                                <h6>Start From</h6>
                                                <h4>NPR <?php echo e($item->lowest_price); ?></h4>
                                            </div>
                                            <ul class="other-option pull-right clearfix">
                                                <li><a aria-label="Compare" class="action-btn" id="<?php echo e($item->id); ?>" onclick="addToCompare(this.id)"><i class="icon-12"></i></a></li>
                                                <li><a aria-label="Add to wishlist" class="action-btn" id="<?php echo e($item->id); ?>" onclick="addToWishList(this.id)"><i class="icon-13"></i></a></li>
                                            </ul>
                                        </div>
                                        <p><?php echo e($item->short_descp); ?></p>
                                        <ul class="more-details clearfix">
                                            <li><i class="icon-14"></i><?php echo e($item->bedrooms); ?> Beds</li>
                                            <li><i class="icon-15"></i><?php echo e($item->bathrooms); ?> Baths</li>
                                            <li><i class="icon-16"></i><?php echo e($item->property_size); ?> Sq Ft</li>
                                        </ul>
                                        <div class="btn-box"><a href="<?php echo e(url('property/details/'.$item->id.'/'.$item->property_slug)); ?>" class="theme-btn btn-two">See Details</a></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                    <?php else: ?>
                    <div class="no-suggestions-container text-center py-5">
                        <div class="no-suggestions-icon mb-4">
                            <i class="fas fa-search fa-4x text-muted"></i>
                        </div>
                        <h3 class="mb-3">No properties found in <?php echo e($property->state_name); ?></h3>
                        <p class="mb-4">We couldn't find any similar properties in this state at the moment.</p>
                        <div class="btn-box">
                            <a href="<?php echo e(route('all.property.list')); ?>" class="theme-btn btn-one mb-2">Browse All Properties</a>
                            <?php if(!Auth::check()): ?>
                            <p class="mt-3">
                                <a href="<?php echo e(route('login')); ?>" class="text-primary">Sign in</a> to get personalized property recommendations based on your preferences.
                            </p>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </section>
        <!-- property-details end -->


        <!-- subscribe-section -->
        <section class="subscribe-section bg-color-3">
            <div class="pattern-layer" style="background-image: url(<?php echo e(asset('frontend/assets/images/shape/shape-2.png')); ?>);"></div>
            <div class="auto-container">
                <div class="row clearfix">
                    <div class="col-lg-6 col-md-6 col-sm-12 text-column">
                        <div class="text">
                            <span>Subscribe</span>
                            <h2>Sign Up To Our Newsletter To Get The Latest News And Offers.</h2>
                        </div>
                    </div>
                    <div class="col-lg-6 col-md-6 col-sm-12 form-column">
                        <div class="form-inner">
                            <form action="contact.html" method="post" class="subscribe-form">
                                <div class="form-group">
                                    <input type="email" name="email" placeholder="Enter your email" required="">
                                    <button type="submit">Subscribe Now</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!-- subscribe-section end -->






<?php $__env->stopSection(); ?>
<?php echo $__env->make('frontend.frontend_dashboard', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\FinalProject\realestate\resources\views/frontend/property/property_details.blade.php ENDPATH**/ ?>