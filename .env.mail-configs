# PropNepal Mail Configuration Options
# Copy the desired configuration to your .env file

# Option 1: Log Driver (Current - Safe for Development)
# Emails will be logged to storage/logs/laravel.log instead of being sent
MAIL_MAILER=log
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

# Option 2: Gmail SMTP (Production Ready)
# Replace with your Gmail credentials and enable 2FA + App Password
# MAIL_MAILER=smtp
# MAIL_HOST=smtp.gmail.com
# MAIL_PORT=587
# MAIL_USERNAME=<EMAIL>
# MAIL_PASSWORD=your-app-password
# MAIL_ENCRYPTION=tls
# MAIL_FROM_ADDRESS="<EMAIL>"
# MAIL_FROM_NAME="${APP_NAME}"

# Option 3: Mailtrap (Testing)
# Sign up at mailtrap.io for testing email functionality
# MAIL_MAILER=smtp
# MAIL_HOST=smtp.mailtrap.io
# MAIL_PORT=2525
# MAIL_USERNAME=your-mailtrap-username
# MAIL_PASSWORD=your-mailtrap-password
# MAIL_ENCRYPTION=tls
# MAIL_FROM_ADDRESS="<EMAIL>"
# MAIL_FROM_NAME="${APP_NAME}"

# Option 4: Disable Email Completely
# Use 'array' driver to completely disable email sending
# MAIL_MAILER=array
# MAIL_HOST=127.0.0.1
# MAIL_PORT=2525
# MAIL_USERNAME=null
# MAIL_PASSWORD=null
# MAIL_ENCRYPTION=null
# MAIL_FROM_ADDRESS="<EMAIL>"
# MAIL_FROM_NAME="${APP_NAME}"

# Instructions:
# 1. Choose one of the above configurations
# 2. Copy the uncommented lines to your .env file
# 3. Replace the existing MAIL_* variables
# 4. Run: php artisan config:clear
# 5. Test the schedule approval functionality
