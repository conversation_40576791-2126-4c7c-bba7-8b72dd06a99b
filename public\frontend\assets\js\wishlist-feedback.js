/**
 * Wishlist Visual Feedback System
 * Handles heart icon state management and visual feedback
 */

// Global variable to store user's wishlist property IDs
let userWishlistIds = [];

/**
 * Initialize wishlist visual feedback system
 */
function initializeWishlistFeedback() {
    // Load user's wishlist IDs on page load
    loadUserWishlistIds();
}

/**
 * Load user's wishlist property IDs from server
 */
function loadUserWishlistIds() {
    $.ajax({
        type: "GET",
        dataType: 'json',
        url: "/get-user-wishlist-ids",
        success: function(data) {
            userWishlistIds = data.wishlist_ids || [];
            updateAllHeartIcons();
        },
        error: function(xhr) {
            console.log('Error loading wishlist IDs:', xhr);
            userWishlistIds = [];
        }
    });
}

/**
 * Update all heart icons on the page based on wishlist status
 */
function updateAllHeartIcons() {
    $('a[onclick*="addToWishList"]').each(function() {
        const propertyId = parseInt($(this).attr('id'));
        const isInWishlist = userWishlistIds.includes(propertyId);
        updateHeartIconState($(this), isInWishlist);
    });
}

/**
 * Update individual heart icon state
 * @param {jQuery} $heartIcon - The heart icon element
 * @param {boolean} isInWishlist - Whether property is in wishlist
 */
function updateHeartIconState($heartIcon, isInWishlist) {
    if (isInWishlist) {
        $heartIcon.addClass('wishlist-active');
        $heartIcon.attr('data-wishlist-status', 'active');
        $heartIcon.attr('title', 'Remove from Wishlist');
    } else {
        $heartIcon.removeClass('wishlist-active');
        $heartIcon.attr('data-wishlist-status', 'inactive');
        $heartIcon.attr('title', 'Add to Wishlist');
    }
}

/**
 * Enhanced addToWishList function with visual feedback
 * @param {number} property_id - Property ID to add to wishlist
 */
function addToWishListWithFeedback(property_id) {
    const $heartIcon = $(`a[id="${property_id}"][onclick*="addToWishList"]`);
    const isCurrentlyInWishlist = userWishlistIds.includes(parseInt(property_id));
    
    // Add loading state
    $heartIcon.addClass('wishlist-loading state-changing');
    
    // Determine action based on current state
    if (isCurrentlyInWishlist) {
        // Property is in wishlist, so remove it
        removeFromWishlist(property_id, $heartIcon);
    } else {
        // Property is not in wishlist, so add it
        addToWishlist(property_id, $heartIcon);
    }
}

/**
 * Add property to wishlist
 * @param {number} property_id - Property ID
 * @param {jQuery} $heartIcon - Heart icon element
 */
function addToWishlist(property_id, $heartIcon) {
    $.ajax({
        type: "POST",
        dataType: 'json',
        url: "/add-to-wishList/" + property_id,
        success: function(data) {
            $heartIcon.removeClass('wishlist-loading state-changing');
            
            if ($.isEmptyObject(data.error)) {
                // Success - add to wishlist
                userWishlistIds.push(parseInt(property_id));
                updateHeartIconState($heartIcon, true);
                $heartIcon.addClass('wishlist-success');
                
                // Remove success animation after completion
                setTimeout(() => {
                    $heartIcon.removeClass('wishlist-success');
                }, 800);
                
                // Update wishlist counter
                wishlist();
                
                // Show success message
                showWishlistToast('success', data.success);
            } else {
                // Error
                $heartIcon.addClass('wishlist-error');
                setTimeout(() => {
                    $heartIcon.removeClass('wishlist-error');
                }, 600);
                
                showWishlistToast('error', data.error);
            }
        },
        error: function(xhr) {
            $heartIcon.removeClass('wishlist-loading state-changing');
            $heartIcon.addClass('wishlist-error');
            setTimeout(() => {
                $heartIcon.removeClass('wishlist-error');
            }, 600);
            
            showWishlistToast('error', 'Failed to add property to wishlist');
        }
    });
}

/**
 * Remove property from wishlist (simulated - would need backend endpoint)
 * @param {number} property_id - Property ID
 * @param {jQuery} $heartIcon - Heart icon element
 */
function removeFromWishlist(property_id, $heartIcon) {
    // For now, we'll show a message that property is already in wishlist
    // In a full implementation, you'd create a remove endpoint
    $heartIcon.removeClass('wishlist-loading state-changing');
    $heartIcon.addClass('wishlist-error');
    
    setTimeout(() => {
        $heartIcon.removeClass('wishlist-error');
    }, 600);
    
    showWishlistToast('error', 'This Property Has Already in your WishList');
}

/**
 * Show wishlist toast notification
 * @param {string} type - 'success' or 'error'
 * @param {string} message - Message to display
 */
function showWishlistToast(type, message) {
    const Toast = Swal.mixin({
        toast: true,
        position: 'top-end',
        showConfirmButton: false,
        timer: 3000
    });
    
    Toast.fire({
        type: type,
        icon: type,
        title: message,
    });
}

/**
 * Override the original addToWishList function
 */
function addToWishList(property_id) {
    addToWishListWithFeedback(property_id);
}

/**
 * Handle dynamic content (for AJAX loaded content)
 */
function refreshWishlistStates() {
    loadUserWishlistIds();
}

// Initialize when document is ready
$(document).ready(function() {
    // Only initialize if user is authenticated
    if (typeof userWishlistIds !== 'undefined') {
        initializeWishlistFeedback();
    }
    
    // Handle dynamically loaded content
    $(document).on('DOMNodeInserted', function(e) {
        if ($(e.target).find('a[onclick*="addToWishList"]').length > 0) {
            setTimeout(updateAllHeartIcons, 100);
        }
    });
});

// Refresh wishlist states when page becomes visible (for browser back/forward)
$(document).on('visibilitychange', function() {
    if (!document.hidden) {
        refreshWishlistStates();
    }
});
