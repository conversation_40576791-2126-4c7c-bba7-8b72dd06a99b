
<?php $__env->startSection('main'); ?>
<?php $__env->startSection('title'); ?>
  <?php echo e($breadcat->category_name); ?> | Easy RealEstate  
<?php $__env->stopSection(); ?>

 <!--Page Title-->
       <section class="page-title-two bg-color-1 centred">
            <div class="pattern-layer">
                <div class="pattern-1" style="background-image: url(<?php echo e(asset('frontend/assets/images/shape/shape-9.png')); ?>);"></div>
                <div class="pattern-2" style="background-image: url(<?php echo e(asset('frontend/assets/images/shape/shape-10.png')); ?>);"></div>
            </div>
            <div class="auto-container">
                <div class="content-box clearfix">
                    <h1> <?php echo e($breadcat->category_name); ?>  </h1>
                    <ul class="bread-crumb clearfix">
                        <li><a href="index.html">Home</a></li>
                        <li><?php echo e($breadcat->category_name); ?> </li>
                    </ul>
                </div>
            </div>
        </section>
        <!--End Page Title-->


        <!-- sidebar-page-container -->
        <section class="sidebar-page-container blog-grid sec-pad-2">
            <div class="auto-container">
                <div class="row clearfix">
                    <div class="col-lg-8 col-md-12 col-sm-12 content-side">
                        <div class="blog-grid-content">
                            <div class="row clearfix">


<?php $__currentLoopData = $blog; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
<div class="col-lg-6 col-md-6 col-sm-12 news-block">
<div class="news-block-one wow fadeInUp animated" data-wow-delay="00ms" data-wow-duration="1500ms">
    <div class="inner-box">
        <div class="image-box">
            <figure class="image"><a href="<?php echo e(url('blog/details/'.$item->post_slug)); ?>"><img src="<?php echo e(asset($item->post_image)); ?>" alt=""></a></figure>
            <span class="category">Featured</span>
        </div>
        <div class="lower-content">
            <h4><a href="<?php echo e(url('blog/details/'.$item->post_slug)); ?>"><?php echo e($item->post_title); ?></a></h4>
            <ul class="post-info clearfix">
                <li class="author-box">
                    <figure class="author-thumb"><img src="<?php echo e((!empty($item->user->photo)) ? url('upload/admin_images/'.$item->user->photo) : url('upload/no_image.jpg')); ?>" alt=""></figure>
  <h5><a href=" "><?php echo e($item['user']['name']); ?></a></h5>
                </li>
                <li><?php echo e($item->created_at->format('M d Y')); ?></li>
            </ul>
            <div class="text">
                <p><?php echo e($item->short_descp); ?></p>
            </div>
            <div class="btn-box">
                <a href="<?php echo e(url('blog/details/'.$item->post_slug)); ?>" class="theme-btn btn-two">See Details</a>
            </div>
        </div>
    </div>
</div>
</div>
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>


                             
                            </div>
                            <div class="pagination-wrapper">
                                <ul class="pagination clearfix">
                                    <li><a href="blog-1.html" class="current">1</a></li>
                                    <li><a href="blog-1.html">2</a></li>
                                    <li><a href="blog-1.html">3</a></li>
                                    <li><a href="blog-1.html"><i class="fas fa-angle-right"></i></a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4 col-md-12 col-sm-12 sidebar-side">
                        <div class="blog-sidebar">
                            <div class="sidebar-widget search-widget">
                                <div class="widget-title">
                                    <h4>Search</h4>
                                </div>
                                <div class="search-inner">
                                    <form action="blog-1.html" method="post">
                                        <div class="form-group">
                                            <input type="search" name="search_field" placeholder="Search" required="">
                                            <button type="submit"><i class="fas fa-search"></i></button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                            <div class="sidebar-widget social-widget">
                                <div class="widget-title">
                                    <h4>Follow Us On</h4>
                                </div>
                                <ul class="social-links clearfix">
                                    <li><a href="blog-1.html"><i class="fab fa-facebook-f"></i></a></li>
                                    <li><a href="blog-1.html"><i class="fab fa-google-plus-g"></i></a></li>
                                    <li><a href="blog-1.html"><i class="fab fa-twitter"></i></a></li>
                                    <li><a href="blog-1.html"><i class="fab fa-linkedin-in"></i></a></li>
                                    <li><a href="blog-1.html"><i class="fab fa-instagram"></i></a></li>
                                </ul>
                            </div>
                            <div class="sidebar-widget category-widget">
                                <div class="widget-title">
                                    <h4>Category</h4>
                                </div>
     <div class="widget-content">
        <ul class="category-list clearfix">
           <?php $__currentLoopData = $bcategory; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $cat): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>

           <?php
        $post = App\Models\BlogPost::where('blogcat_id',$cat->id)->get();
           ?>


  <li><a href="<?php echo e(url('blog/cat/list/'.$cat->id)); ?>"><?php echo e($cat->category_name); ?><span>(<?php echo e(count($post)); ?>)</span></a></li>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </ul>
    </div>
                            </div>
                            <div class="sidebar-widget post-widget">
                                <div class="widget-title">
                                    <h4>Recent Posts</h4>
                                </div>
       <div class="post-inner">
        
    	<?php $__currentLoopData = $dpost; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $post): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <div class="post">
            <figure class="post-thumb"><a href="blog-details.html"><img src="<?php echo e(asset($post->post_image)); ?>" alt=""></a></figure>
            <h5><a href="blog-details.html"><?php echo e($post->post_title); ?></a></h5>
            <span class="post-date"><?php echo e($post->created_at->format('M d Y')); ?></span>
        </div>
      	<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>


    </div>
                            </div>
                           
                          
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!-- sidebar-page-container -->

        <!-- subscribe-section -->
        <section class="subscribe-section bg-color-3">
            <div class="pattern-layer" style="background-image: url(assets/images/shape/shape-2.png);"></div>
            <div class="auto-container">
                <div class="row clearfix">
                    <div class="col-lg-6 col-md-6 col-sm-12 text-column">
                        <div class="text">
                            <span>Subscribe</span>
                            <h2>Sign Up To Our Newsletter To Get The Latest News And Offers.</h2>
                        </div>
                    </div>
                    <div class="col-lg-6 col-md-6 col-sm-12 form-column">
                        <div class="form-inner">
                            <form action="contact.html" method="post" class="subscribe-form">
                                <div class="form-group">
                                    <input type="email" name="email" placeholder="Enter your email" required="">
                                    <button type="submit">Subscribe Now</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!-- subscribe-section end -->








<?php $__env->stopSection(); ?>

<?php echo $__env->make('frontend.frontend_dashboard', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\FinalProject\realestate\resources\views/frontend/blog/blog_cat_list.blade.php ENDPATH**/ ?>