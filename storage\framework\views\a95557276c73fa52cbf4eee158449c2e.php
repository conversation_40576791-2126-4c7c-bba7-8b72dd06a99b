<?php $__env->startSection('agent'); ?>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.3/jquery.min.js"></script>

<div class="page-content">

<div class="row">
                    <div class="col-md-6 grid-margin stretch-card">
            <div class="card">
              <div class="card-body">
                                <h6 class="card-title">Property Details </h6>

                                <div class="table-responsive">
        <table class="table table-striped">

            <tbody>
                <tr>
                    <td>Property Name </td>
                    <td><code><?php echo e($property->property_name); ?></code></td>
                </tr>

                <tr>
                    <td>Property Status </td>
                    <td><code><?php echo e($property->property_status); ?></code></td>
                </tr>
                <tr>
                    <td>Lowest Price </td>
                    <td><code><?php echo e($property->lowest_price); ?></code></td>
                </tr>

                <tr>
                    <td>Max Price </td>
                    <td><code><?php echo e($property->max_price); ?></code></td>
                </tr>
                <tr>
                    <td>BedRooms </td>
                    <td><code><?php echo e($property->bedrooms); ?></code></td>
                </tr>

                <tr>
                    <td>Bathrooms </td>
                    <td><code><?php echo e($property->bathrooms); ?></code></td>
                </tr>
                <tr>
                    <td>Garage </td>
                    <td><code><?php echo e($property->garage); ?></code></td>
                </tr>
                <tr>
                    <td>Garage Size </td>
                    <td><code><?php echo e($property->garage_size); ?></code></td>
                </tr>
                <tr>
                    <td>Address </td>
                    <td><code><?php echo e($property->address); ?></code></td>
                </tr>
                <tr>
                    <td>City </td>
                    <td><code><?php echo e($property->city); ?></code></td>
                </tr>
                <tr>
                    <td>State </td>
                    <td><code><?php echo e($property->state_name); ?></code></td>
                </tr>

                 <tr>
                    <td>Postal Code </td>
                    <td><code><?php echo e($property->postal_code); ?></code></td>
                </tr>

                 <tr>
                    <td>Main Image </td>
                    <td>
                    <img src="<?php echo e(asset($property->property_thambnail)); ?>" style="width:100px; height:70px;">
                    </td>
                </tr>

                 <tr>
                    <td>Status </td>
                    <td>
                        <?php if($property->status == 1): ?>
                <span class="badge rounded-pill bg-success">Active</span>
                      <?php else: ?>
               <span class="badge rounded-pill bg-danger">InActive</span>
                      <?php endif; ?>
                  </td>
                </tr>


            </tbody>
        </table>
                                </div>
              </div>
            </div>
                    </div>
                    <div class="col-md-6 grid-margin stretch-card">
            <div class="card">
              <div class="card-body">


<div class="table-responsive">
        <table class="table table-striped">

            <tbody>
                 <tr>
                    <td>Property Code </td>
                    <td><code><?php echo e($property->property_code); ?></code></td>
                </tr>

                <tr>
                    <td>Property Size </td>
                    <td><code><?php echo e($property->property_size); ?></code></td>
                </tr>


                <tr>
                    <td>Property Video</td>
                    <td><code><?php echo e($property->property_video); ?></code></td>
                </tr>

                <tr>
                    <td>Neighborhood </td>
                    <td><code><?php echo e($property->neighborhood); ?></code></td>
                </tr>

                 <tr>
                    <td>Latitude </td>
                    <td><code><?php echo e($property->latitude); ?></code></td>
                </tr>


                 <tr>
                    <td>Longitude </td>
                    <td><code><?php echo e($property->longitude); ?></code></td>
                </tr>


                 <tr>
                    <td>Property Type </td>
                    <td><code><?php echo e($property['type']['type_name']); ?></code></td>
                </tr>

                 <tr>
                    <td>Property Amenities </td>
                    <td>
 <select name="amenities_id[]" class="js-example-basic-multiple form-select" multiple="multiple" data-width="100%">

                 <?php $__currentLoopData = $amenities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $ameni): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <option value="<?php echo e($ameni->amenitis_name); ?>" <?php echo e((in_array($ameni->amenitis_name,$property_ami)) ? 'selected' : ''); ?> ><?php echo e($ameni->amenitis_name); ?></option>
               <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

            </select>
                    </td>
                </tr>


                  <tr>
                    <td>Agent </td>

            <?php if($property->agent_id == NULL): ?>
            <td><code> Admin </code></td>
            <?php else: ?>
            <td><code> <?php echo e($property['user']['name']); ?> </code></td>
            <?php endif; ?>

                </tr>


            </tbody>
        </table>

<br><br>




</div>
              </div>
            </div>
                    </div>
                </div>



  </div>







<?php $__env->stopSection(); ?>
<?php echo $__env->make('agent.agent_dashboard', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\FinalProject\realestate\resources\views/agent/property/details_property.blade.php ENDPATH**/ ?>