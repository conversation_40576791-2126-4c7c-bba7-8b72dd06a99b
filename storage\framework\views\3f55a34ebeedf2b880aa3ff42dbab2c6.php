
   <?php
   $setting = App\Models\SiteSetting::find(1);
   $blog = App\Models\BlogPost::latest()->limit(2)->get();
   ?>

 <footer class="main-footer">
            <div class="footer-top bg-color-2">
                <div class="auto-container">
                    <div class="row clearfix">
                        <div class="col-lg-3 col-md-6 col-sm-12 footer-column">
                            <div class="footer-widget about-widget">
                                <div class="widget-title">
                                    <h3>About</h3>
                                </div>
                                <div class="text">
                                    <p>Welcome to PropNepal , your go-to real estate platform in Nepal. Whether you're buying, selling, or renting property, our user-friendly system connects buyers and sellers directly for a transparent and efficient experience.</p>
                                    <p>As a leading real estate agency, PropNepal Pvt. Ltd. is dedicated to providing expert service and top-notch support for all your property needs.</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 col-sm-12 footer-column">
                            <div class="footer-widget links-widget ml-70">
                                <div class="widget-title">
                                    <h3>Company</h3>
                                </div>
                                <div class="links-inner">
                                    <ul>
                                        <li><a href="<?php echo e(url('/#chooseus-section')); ?>">How It Works</a></li>
                                        <!-- <li><a href="<?php echo e(url('/about')); ?>">About Us</a></li> -->
                                        <li><a href="<?php echo e(route('all.property.list')); ?>">Listing</a></li>
                                        <?php if(isset($property) && $property && !is_a($property, 'Illuminate\Pagination\LengthAwarePaginator') && !is_a($property, 'Illuminate\Database\Eloquent\Collection') && property_exists($property, 'id')): ?>
                                            <li><a href="<?php echo e(route('user.chat.property', ['propertyId' => $property->id])); ?>" class="theme-btn btn-one">Chat With Agent</a></li>
                                        <?php endif; ?>
                                        <!-- <li><a href="index.html">Our Services</a></li> -->
                                        <li><a href="<?php echo e(route('blog.list')); ?>">Our Blog</a></li>
                                        <!-- <li><a href="index.html">Contact Us</a></li> -->
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 col-sm-12 footer-column">
                            <div class="footer-widget post-widget">
                                <div class="widget-title">
                                    <h3>Top News</h3>
                                </div>
                                <div class="post-inner">

     <?php $__currentLoopData = $blog; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <div class="post">
        <figure class="post-thumb"><a href="<?php echo e(url('blog/details/'.$item->post_slug)); ?>"><img src="<?php echo e(asset($item->post_image)); ?>" alt=""></a></figure>
        <h5><a href="<?php echo e(url('blog/details/'.$item->post_slug)); ?>"><?php echo e($item->post_title); ?></a></h5>
        <p><?php echo e($item->created_at->format('M d Y')); ?></p>
    </div>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 col-sm-12 footer-column">
                            <div class="footer-widget contact-widget">
                                <div class="widget-title">
                                    <h3>Contacts</h3>
                                </div>
                                <div class="widget-content">
    <ul class="info-list clearfix">
        <?php if($setting && $setting->company_address): ?>
            <li><i class="fas fa-map-marker-alt"></i><?php echo e($setting->company_address); ?></li>
        <?php endif; ?>
        <?php if($setting && $setting->support_phone): ?>
            <li><i class="fas fa-microphone"></i><a href="tel:23055873407">+<?php echo e($setting->support_phone); ?></a></li>
        <?php endif; ?>
        <?php if($setting && $setting->email): ?>
            <li><i class="fas fa-envelope"></i><a href="mailto:<EMAIL>"><?php echo e($setting->email); ?></a></li>
        <?php endif; ?>
    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <div class="auto-container">
                    <div class="inner-box clearfix">
                        <!-- <figure class="footer-logo"><a href="index.html"><img src="<?php echo e(asset('frontend/assets/images/footer-logo.png')); ?>" alt=""></a></figure> -->
                        <div class="copyright pull-left">
                            <p><a href="index.html"><?php echo e($setting && $setting->copyright ? $setting->copyright : '© ' . date('Y') . ' Real Estate. All rights reserved.'); ?></p>
                        </div>
                        <ul class="footer-nav pull-right clearfix">
                            <li><a href="index.html">Terms of Service</a></li>
                            <li><a href="index.html">Privacy Policy</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </footer><?php /**PATH C:\Users\<USER>\Desktop\FinalProject\realestate\resources\views/frontend/home/<USER>/ ?>