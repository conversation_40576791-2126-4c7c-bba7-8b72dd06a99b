<?php $__env->startSection('agent'); ?>



<div class="page-content">

				<nav class="page-breadcrumb">
					<ol class="breadcrumb">

					</ol>
				</nav>

				<div class="row">
					<div class="col-md-8">
            <div class="card">
              <h6 class="card-title">Schedule Request Details </h6>
 <form method="post" action="<?php echo e(route('agent.update.schedule')); ?>">
 	<?php echo csrf_field(); ?>

  <input type="hidden" name="id" value="<?php echo e($schedule->id); ?>">
  <input type="hidden" name="email" value="<?php echo e($schedule->user->email); ?>">

  <div class="table-responsive pt-3">
                  <table class="table table-bordered">

                    <tbody>
      <tr>
        <td>User Name </td>
        <td><?php echo e($schedule->user->name); ?></td>

      </tr>

      <tr>
        <td>Property Name </td>
        <td><?php echo e($schedule->property->property_name); ?></td>

      </tr>


      <tr>
        <td>Tour Date  </td>
        <td><?php echo e($schedule->tour_date); ?></td>

      </tr>


      <tr>
        <td>Tour Time  </td>
        <td><?php echo e($schedule->tour_time); ?></td>

      </tr>


      <tr>
        <td>Message  </td>
        <td><?php echo e($schedule->message); ?></td>

      </tr>

      <tr>
        <td>Request Send Time  </td>
        <td><?php echo e($schedule->created_at->format('l M d Y')); ?></td>

      </tr>

      <tr>
        <td>Status  </td>
        <td>
          <?php if($schedule->status == 1): ?>
            <span class="badge rounded-pill bg-success">Confirmed</span>
          <?php elseif($schedule->status == 2): ?>
            <span class="badge rounded-pill bg-danger">Rejected</span>
          <?php else: ?>
            <span class="badge rounded-pill bg-warning">Pending</span>
          <?php endif; ?>
        </td>
      </tr>

                    </tbody>
                  </table>
                </div>
                <br><br>

                <?php if($schedule->status == 0): ?>
                  <!-- Only show action buttons for pending requests -->
                  <button type="submit" class="btn btn-success">Approve Request</button>
                  </form>

                  <form method="post" action="<?php echo e(route('agent.reject.schedule')); ?>" style="margin-top: 10px;">
                      <?php echo csrf_field(); ?>
                      <input type="hidden" name="id" value="<?php echo e($schedule->id); ?>">
                      <button type="submit" class="btn btn-danger">Reject Request</button>
                  </form>
                <?php else: ?>
                  </form>
                  <div class="alert alert-info">
                    <?php if($schedule->status == 1): ?>
                      This schedule request has been <strong>confirmed</strong>.
                    <?php else: ?>
                      This schedule request has been <strong>rejected</strong>.
                    <?php endif; ?>
                  </div>
                <?php endif; ?>
        <br><br>
   </div>



					</div>
				</div>
			</div>






<?php $__env->stopSection(); ?>

<?php echo $__env->make('agent.agent_dashboard', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\FinalProject\realestate\resources\views/agent/schedule/schedule_details.blade.php ENDPATH**/ ?>