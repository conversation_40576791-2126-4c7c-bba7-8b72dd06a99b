<!-- Single Property Item -->
<div class="deals-block-one">
    <div class="inner-box">
        <div class="image-box">
            <figure class="image">
                <img src="<?php echo e(asset($item->property_thambnail)); ?>" alt="<?php echo e($item->property_name); ?>" style="width:370px; height:250px;">
            </figure>
            <div class="batch"><i class="icon-11"></i></div>
            <?php if($item->featured == 1): ?>
                <span class="category">Featured</span>
            <?php endif; ?>
        </div>
        <div class="lower-content">
            <div class="title-text">
                <h4><a href="<?php echo e(url('property/details/'.$item->id.'/'.$item->property_slug)); ?>"><?php echo e($item->property_name); ?></a></h4>
            </div>
            <div class="price-box clearfix">
                <div class="price-info pull-left">
                    <h6>Start From</h6>
                    <h4>NPR <?php echo e(number_format($item->lowest_price)); ?></h4>
                </div>
                <?php if($item->property_status == 'rent'): ?>
                    <div class="author-box pull-right">
                        <figure class="author-thumb">
                            <img src="<?php echo e((!empty($item->user->photo)) ? url('upload/agent_images/'.$item->user->photo) : url('upload/no_image.jpg')); ?>" alt="">
                            <span class="batch"><?php echo e($item->user->name); ?></span>
                        </figure>
                    </div>
                <?php endif; ?>
            </div>
            <p><?php echo e($item->short_descp); ?></p>
            <ul class="more-details clearfix">
                <li><i class="icon-14"></i><?php echo e($item->bedrooms); ?> Beds</li>
                <li><i class="icon-15"></i><?php echo e($item->bathrooms); ?> Baths</li>
                <li><i class="icon-16"></i><?php echo e($item->property_size); ?> Sq Ft</li>
            </ul>
            <div class="other-info-box clearfix">
                <div class="btn-box pull-left">
                    <a href="<?php echo e(url('property/details/'.$item->id.'/'.$item->property_slug)); ?>" class="theme-btn btn-two">See Details</a>
                </div>
                <ul class="other-option pull-right clearfix">
                    <li><a href="<?php echo e(url('property/details/'.$item->id.'/'.$item->property_slug)); ?>"><i class="icon-12"></i></a></li>
                    <li><a href="<?php echo e(url('property/details/'.$item->id.'/'.$item->property_slug)); ?>"><i class="icon-13"></i></a></li>
                </ul>
            </div>
        </div>
    </div>
</div>
<?php /**PATH C:\Users\<USER>\Desktop\FinalProject\realestate\resources\views/frontend/property/partials/property_item.blade.php ENDPATH**/ ?>