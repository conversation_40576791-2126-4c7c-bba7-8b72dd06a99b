@extends('agent.agent_dashboard')
@section('agent')



<div class="page-content">

				<nav class="page-breadcrumb">
					<ol class="breadcrumb">

					</ol>
				</nav>

				<div class="row">
					<div class="col-md-8">
            <div class="card">
              <h6 class="card-title">Schedule Request Details </h6>
 <form method="post" action="{{ route('agent.update.schedule') }}">
 	@csrf

  <input type="hidden" name="id" value="{{ $schedule->id }}">
  <input type="hidden" name="email" value="{{ $schedule->user->email }}">

  <div class="table-responsive pt-3">
                  <table class="table table-bordered">

                    <tbody>
      <tr>
        <td>User Name </td>
        <td>{{ $schedule->user->name }}</td>

      </tr>

      <tr>
        <td>Property Name </td>
        <td>{{ $schedule->property->property_name }}</td>

      </tr>


      <tr>
        <td>Tour Date  </td>
        <td>{{ $schedule->tour_date }}</td>

      </tr>


      <tr>
        <td>Tour Time  </td>
        <td>{{ $schedule->tour_time }}</td>

      </tr>


      <tr>
        <td>Message  </td>
        <td>{{ $schedule->message }}</td>

      </tr>

      <tr>
        <td>Request Send Time  </td>
        <td>{{ $schedule->created_at->format('l M d Y') }}</td>

      </tr>

      <tr>
        <td>Status  </td>
        <td>
          @if($schedule->status == 1)
            <span class="badge rounded-pill bg-success">Confirmed</span>
          @elseif($schedule->status == 2)
            <span class="badge rounded-pill bg-danger">Rejected</span>
          @else
            <span class="badge rounded-pill bg-warning">Pending</span>
          @endif
        </td>
      </tr>

                    </tbody>
                  </table>
                </div>
                <br><br>

                @if($schedule->status == 0)
                  <!-- Only show action buttons for pending requests -->
                  <button type="submit" class="btn btn-success">Approve Request</button>
                  </form>

                  <form method="post" action="{{ route('agent.reject.schedule') }}" style="margin-top: 10px;">
                      @csrf
                      <input type="hidden" name="id" value="{{ $schedule->id }}">
                      <button type="submit" class="btn btn-danger">Reject Request</button>
                  </form>
                @else
                  </form>
                  <div class="alert alert-info">
                    @if($schedule->status == 1)
                      This schedule request has been <strong>confirmed</strong>.
                    @else
                      This schedule request has been <strong>rejected</strong>.
                    @endif
                  </div>
                @endif
        <br><br>
   </div>



					</div>
				</div>
			</div>






@endsection
